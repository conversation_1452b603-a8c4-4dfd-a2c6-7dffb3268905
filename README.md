# 文档

~~技术方案：https://365.kdocs.cn/l/cs5y7huhnKFD?from=koa~~

~~文档：    https://365.kdocs.cn/l/coRX1UPclUCN?from=koa~~


# 人工评测后端代码启动方式
- 分支（按需修改）
 
feature-v1.6.0


- 创建Python环境，安装相关依赖：
```angular2html
需要提前安装conda进行python环境管理
conda create --name hanhai_human_eval python=3.10
conda activate hanhai_human_eval
pip install -r requirements.txt
```
- 初始化数据库
  - 可以通过sql文件初始化，./sql/下的文件
  - 可以通过alembic命令初始化
    ```angular2html
     alembic upgrade head
    ```
- idae中启动需要设置对应的conda环境作为Python环境
  ![img_1.png](img_1.png)
- 设置环境变量和配置文件
  ![img.png](img.png)
  - 本地启动需要设置环境变量：APP_ENV=local
  - 配置文件：config_local.ini
  ```angular2html
  [DEFAULT]
  environment = local
  container_root = /Users/<USER>/develop/code/ksyun_inference/evaluation/hanhai-human-eval
  log_root = ${container_root}/log
  
  [mysql]
  db_name = ${project}_hbb_4
  #db_username = hbb
  #db_password = 123456
  #db_password = UOkAPhXhsi6VyYphzhaHQMEH0Fnh6A==
  
  db_username = hbb_2
  #db_password = 12@34%56
  # v1.6.0-before
  # db_password = YfyHGZQF9PYvHLi2ptZqzzeNhMRSylEg
  # v1.6.0-after
  db_password = WViL5A7V2eauQk8xKE9Cqyz0g36TucqCi3ngX2E8Ms684SPvUdTnpiGf314=
  db_host = localhost
  db_port = 3306
  
  [training]
  ;platform = http://hanhai-tob-dev.ai.ksyun.com/model-manage
  platform = http://hanhai-dev.inner.ai.kingsoft.com/model-manage
  
  [inference]
  ;platform = http://hanhai-tob-dev.ai.ksyun.com/inference
  platform = http://hanhai-dev.inner.ai.kingsoft.com/inference
  default_gpus_per_replica = 4
  
  
  [inference_eval]
  ;platform = http://inference-dev.ai.ksyun.com
  platform = http://inference-dev.inner.ai.kingsoft.com
  header = Custom-Host
  
  [common]
  platform = https://hanhai-poc-dev.ai.ksyun.com/common
  ;platform = http://common-service.hanhai-common.svc.cluster.local:8000
  
  [auth_server]
  platform = http://kcde-dev.ai.kingsoft.com
  
  [jwt]
  key_version = 240301
  public_key = ${container_root}/conf/jwt_public_${key_version}-${environment}.pem
  
  [channel]
  ;platform = uss
  ;platform = common
  platform = kcde
  ;role = model_evaluator
  ```
  - 数据库密码加密
    v1.6.0版本之后，通过执行share/util/aes_utils.py可以对需要加密的数据库密码进行加密。

- 添加pem文件

  在conf下添加对应环境的pem文件，具体内容参考dev环境
    ```shell
    jwt_public_240301-local.pem
    ```

- 启动项目

  按需要选择环境变量，APP_ENV=local
  
  启动项目，api.py为入口文件:

    http://localhost:8000/eval/api/v1/docs



# hanhai前端代码启动方式
- 分支（按需修改）

feature/1.6

- 安装依赖
  - 安装NodeJS，大版本为18 
  - 安装pnpm，并执行以下命令安装依赖：
    ```angular2html
    pnpm install
    ```

- 准备环境配置文件
    - {hanhai-fe}/apps/hanhai目录下创建.env.local文件
    - 在.env.local文件中添加以下内容
    ```
  # 区分使用环境
  NEXT_PUBLIC_AUTH_TYPE=dev
  # 要关闭的菜单项和页面，只有非USS环境下有效，如果要在本地开发POC环境内容，可以打开
  # NEXT_PUBLIC_POC_CLOSE_MENU=datasets,evaluation,training,inference/model/manage
  # USS环境地址
  # NEXT_PUBLIC_DEVELOPMENT_HOST=http://hanhai-dev.inner.ai.kingsoft.com
  # POC环境地址
  NEXT_PUBLIC_POC_HOST=http://hanhai-dev.inner.ai.kingsoft.com
  #NEXT_PUBLIC_POC_HOST=https://hanhai-tob-dev.ai.ksyun.com
  NEXT_PUBLIC_ISTOB=true
  NEXT_PUBLIC_PROJECT_NAME_ROUTH=standard
    ```
    - 修改{hanhai-fe}/apps/hanhai/next.config.mjs，更改hanhai-human-eval相关的配置注释，可以切换连接本地还是dev环境
    ```
    /** @type {import('next').NextConfig} */
    const NODE_ENV = process.env.NODE_ENV || 'development';
    const TYPE = process.env.NEXT_PUBLIC_AUTH_TYPE ?? 'uss';
    const DEVELOPMENT_HOST = process.env.NEXT_PUBLIC_DEVELOPMENT_HOST;
    const POC_HOST = process.env.NEXT_PUBLIC_POC_HOST;
    const PRE = '/gateway';
    const POC_HOSTDATA = process.env.NEXT_PUBLIC_PROJECT_NAME_ROUTH;
    const PREDATASETS =
      POC_HOSTDATA == 'inner'
        ? '/gateway/inner'
        : POC_HOSTDATA == 'standard'
          ? '/gateway/standard'
          : '/gateway';
    
    const nextConfig = {
      reactStrictMode: false,
      experimental: {
        swcPlugins: [
          ['@swc-jotai/debug-label', {}],
          ['@swc-jotai/react-refresh', {}],
        ],
      },
      rewrites:
        NODE_ENV === 'development'
          ? async () => [
              {
                source: `${PREDATASETS}/datasets/api/:path*`,
                destination: `${POC_HOST}${PREDATASETS}/datasets/api/:path*`,
              },
                // {
                //     source: `${PRE}/evaluation/api/:path*`,
                //     destination: `${POC_HOST}${PRE}/evaluation/api/:path*`,
                // },
                {
                    source: `${PRE}/evaluation/api/:path*`,
                    destination: `http://127.0.0.1:8008/evaluation/api/:path*`,
                },
              {
                source: `${PRE}/inference/api/:path*`,
                destination: `${TYPE === 'uss' ? POC_HOST : POC_HOST}${PRE}/inference/api/:path*`,
              },
              {
                source: `${PRE}/training/api/:path*`,
                destination: `${POC_HOST}${PRE}/training/api/:path*`,
              },
              {
                source: `/common/api/:path*`,
                // destination: `${TYPE === 'uss' ? DEVELOPMENT_HOST : POC_HOST}/common/api/:path*`,
                destination: `https://hanhai-poc.ai.ksyun.com/common/api/:path*`,
              },
              {
                source: `${PRE}/training/kas/:path*`,
                destination: `${POC_HOST}${PRE}/training/kas/:path*`,
              },
              {
                source: `/basic_user/api/:path*`,
                destination: `${TYPE === 'uss' ? POC_HOST : POC_HOST}/basic_user/api/:path*`,
              },
              {
                source: `${PRE}/model-manage/api/:path*`,
                destination: `${POC_HOST}${PRE}/model-manage/api/:path*`,
              },
                // {
                //     source: `${PRE}/evaluation-human/api/:path*`,
                //     destination: `${POC_HOST}${PRE}/evaluation-human/api/:path*`,
                // },
                {
                    source: `${PRE}/evaluation-human/api/:path*`,
                    destination: `http://127.0.0.1:8000/api/:path*`,
                },
              {
                source: `/authserver/api/:path*`,
                destination: `${POC_HOST}/authserver/api/:path*`,
              },
              {
                source: `${PRE}/authserver/api/:path*`,
                destination: `${POC_HOST}/gateway/authserver/api/:path*`,
              },
              {
                source: `/oauth2/:path*`,
                destination: `${POC_HOST}/oauth2/:path*`,
              },
            ]
          : undefined,
    };
    
    export default nextConfig;
    
      
    ```
  

- 启动项目
  - 在根目录下启动项目
    ```angular2html
    pnpm run dev:hanhai
    ```
  - 添加host映射，否则无法正常进行登陆认证。无法通过localhost、127.0.0.1等登陆完成认证，奇怪的bug！！！
    ```
    127.0.0.1 local.cc.ai.kingsoft.com
    ```
  - 打开页面，认证登陆
    ```
    http://local.cc.ai.kingsoft.com:3000/
    ```
    
- 本地开发需要添加认证配置

  hanhai平台目前认证通过nginx转发到auth，auth会添加认证信息到请求头中，调用本地运行的服务是无法添加认证信息的，因此需要在本地开发时通过浏览器插件添加认证信息。
  - 安装浏览器插件：ModHeader
  - 添加authorization配置
  - 登陆dev环境，访问人工评测页面，查看后台日志打印的请求认证信息，将信息添加到浏览器插件中。

#!/bin/bash

# 设置源数据库参数
SRC_DB_HOST="***********"
SRC_DB_USER="writer"
SRC_DB_PASS="iC4t6nufZNLZFNY"
SRC_DB_NAME="cckb_dev"
SRC_DB_PORT="10606"

# 设置目标数据库参数
TGT_DB_HOST="***********"
TGT_DB_USER="writer"
TGT_DB_PASS="iC4t6nufZNLZFNY"
TGT_DB_NAME="human_eval_dev"
TGT_DB_PORT="10606"

# 设置需要迁移的表
TABLES=(
    "alembic_version"
    "system_quiz_criteria_set_relations"
    "system_quiz_criteria_sets"
    "system_quiz_criterias"
    "system_quiz_plan"
    "system_quiz_question_set_relations_v2"
    "system_quiz_question_sets"
    "system_quiz_question_types"
    "system_quiz_questions_v2"
    "system_quiz_tasks"
    "system_quiz_user_set_relations"
    "system_quiz_user_sets"
    "user"
    "user_info"
    "user_quiz_tasks"
    "user_quiz_tasks_criteria_info"
    "user_quiz_tasks_info"
    "menu"
)

# 导出数据
DUMP_FILE="cckb_dev.sql"
echo "开始导出数据..."
mysqldump --verbose -h $SRC_DB_HOST -P $SRC_DB_PORT -u $SRC_DB_USER -p$SRC_DB_PASS $SRC_DB_NAME --set-gtid-purged=OFF ${TABLES[@]} | pv -pterab > $DUMP_FILE
echo "数据导出完成."

# 导入数据
echo "开始导入数据到目标数据库..."
pv $DUMP_FILE | mysql --verbose -h $TGT_DB_HOST -P $TGT_DB_PORT -u $TGT_DB_USER -p$TGT_DB_PASS $TGT_DB_NAME
echo "数据导入完成."

echo "数据迁移成功!"

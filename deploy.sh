 #!/bin/bash

#./deploy.sh v0.2.5

# 检查是否传递了版本参数
#if [ -z "$1" ]; then
#  echo "错误：未提供版本参数。"
#  echo "用法: $0 <version>"
#  exit 1
#fi

# 保存版本参数
#version=$1
# 保持与hanhai平台版本一致
version=v1.7.0-arm


# 设置默认 Dockerfile 路径
DOCKERFILE_PATH="./docker/Dockerfile"
# 如果 version 包含 "arm"，则使用 Dockerfile-arm64
if [[ $version == *"-arm"* ]]; then
    DOCKERFILE_PATH="./docker/Dockerfile-arm64"
    echo "检测到 ARM 版本，使用 Dockerfile-arm64 构建"
fi



# 执行 git pull
#git pull

# 构建并标记 Docker 镜像
#docker build -t harbor.dev.ai.ksyun.com:11180/kai/hanhai-human-eval:${version} -f ./docker/Dockerfile .
#docker build -t harbor.inner.ai.kingsoft.com:11180/kai/hanhai-human-eval:${version} -f ./docker/Dockerfile .
docker build -t harbor.inner.ai.kingsoft.com:11180/kai/hanhai-human-eval:${version} -f ${DOCKERFILE_PATH} .

# 推送 Docker 镜像到仓库
#docker push harbor.dev.ai.ksyun.com:11180/kai/hanhai-human-eval:${version}
docker push harbor.inner.ai.kingsoft.com:11180/kai/hanhai-human-eval:${version}

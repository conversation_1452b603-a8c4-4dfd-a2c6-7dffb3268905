CREATE TABLE `alembic_version` (
                                   `version_num` varchar(32) NOT NULL,
                                   PRIMARY KEY (`version_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `inference_service` (
                                     `id` int NOT NULL AUTO_INCREMENT,
                                     `plan_id` varchar(500) NOT NULL DEFAULT '0' COMMENT '计划id',
                                     `plan_name` text COMMENT '计划名称',
                                     `model_id` int NOT NULL COMMENT '模型ID',
                                     `model_name` text COMMENT '模型名称',
                                     `inference_service_id` int DEFAULT NULL COMMENT '推理服务ID',
                                     `inference_state` int DEFAULT NULL COMMENT '推理服务状态1-更新中2-运行中3-下线中4-已下线5-异常',
                                     `internal_host` text COMMENT '内部服务Host',
                                     `external_path` text COMMENT '外部服务路径',
                                     `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `updated_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                     `model_type` int DEFAULT NULL COMMENT '模型类型',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `menu` (
                        `id` int NOT NULL AUTO_INCREMENT,
                        `name` tinytext NOT NULL COMMENT '菜单名称',
                        `parent_id` int NOT NULL COMMENT '父菜单ID',
                        `menu_order` int NOT NULL COMMENT '菜单排序',
                        `link` text COMMENT '菜单链接',
                        `icon` text COMMENT '菜单图标',
                        `permission` json DEFAULT NULL COMMENT '菜单权限',
                        `state` int NOT NULL COMMENT '0: default, -1: deleted',
                        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `model` (
                         `id` int NOT NULL AUTO_INCREMENT,
                         `model_id` text NOT NULL COMMENT '模型ID',
                         `name` text NOT NULL COMMENT '模型名称',
                         `description` text COMMENT '模型描述',
                         `type` text NOT NULL COMMENT '模型类型',
                         `version` text NOT NULL COMMENT '模型版本',
                         `source` text NOT NULL COMMENT '模型来源',
                         `parameters` json DEFAULT NULL COMMENT '模型参数',
                         `inference_type` text NOT NULL COMMENT '推理服务提供方类型',
                         `state` int NOT NULL,
                         `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                         `updated_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_question_dislike` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `evaluation_id` varchar(500) NOT NULL COMMENT '评审ID',
                                           `question_id` bigint NOT NULL COMMENT '问题id',
                                           `dislike_count` int NOT NULL DEFAULT '0' COMMENT '点踩数量',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_question_dislike_user` (
                                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                `dislike_id` bigint NOT NULL COMMENT '点踩表主键',
                                                `evaluation_id` varchar(500) DEFAULT NULL COMMENT '评审ID',
                                                `question_id` bigint DEFAULT NULL COMMENT '问题ID',
                                                `content` text COMMENT '点踩内容',
                                                `user_id` text COMMENT '创建人',
                                                `user_name` varchar(500) DEFAULT NULL COMMENT '用户名',
                                                `user_full_name` varchar(500) DEFAULT NULL COMMENT '用户名',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_criteria_set_relations` (
                                                      `id` int NOT NULL AUTO_INCREMENT,
                                                      `criteria_id` int NOT NULL COMMENT '评审标准ID',
                                                      `criteria_set_identifier` varchar(255) NOT NULL COMMENT '评审标准集合版本号',
                                                      `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                      `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_criteria_sets` (
                                             `id` int NOT NULL AUTO_INCREMENT,
                                             `criteria_set_identifier` varchar(255) NOT NULL COMMENT '评审标准集合版本号',
                                             `quiz_type` varchar(100) NOT NULL DEFAULT 'art' COMMENT '评测类型  AB评测:ab, 人工评测:art, 自评测试:objective',
                                             `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                             PRIMARY KEY (`id`),
                                             UNIQUE KEY `criteria_set_identifier` (`criteria_set_identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_criterias` (
                                         `id` int NOT NULL AUTO_INCREMENT,
                                         `title` varchar(255) NOT NULL COMMENT '评分标准标题',
                                         `value` text NOT NULL COMMENT '评分标准描述',
                                         `rating_key` varchar(100) NOT NULL COMMENT '评审标准KEY',
                                         `rating_type` varchar(256) DEFAULT '0' COMMENT '评分标准类型',
                                         `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_plan` (
                                    `id` int NOT NULL AUTO_INCREMENT,
                                    `plan_id` varchar(255) NOT NULL COMMENT '唯一的计划ID',
                                    `plan_name` varchar(255) NOT NULL COMMENT '计划名称',
                                    `quiz_type` varchar(100) NOT NULL DEFAULT 'art' COMMENT '评测类型  AB评测:ab, 人工评测:art, 自评测试:objective ',
                                    `desc` text NOT NULL COMMENT '计划描述',
                                    `eval_product` text COMMENT '评测产品',
                                    `dim_set_id` int NOT NULL COMMENT '评测维度集合版本ID',
                                    `qa_round_dim_set_id` int NOT NULL DEFAULT '0' COMMENT 'QA轮次维度集合版本ID',
                                    `question_set_id` int NOT NULL DEFAULT '0' COMMENT '问题集合版本ID',
                                    `user_set_id` int NOT NULL COMMENT '评测人员集合版本ID',
                                    `created_user` varchar(100) NOT NULL COMMENT '创建人',
                                    `updated_user` varchar(100) NOT NULL COMMENT '更新人',
                                    `trigger` varchar(150) DEFAULT NULL COMMENT '触发条件',
                                    `expire` bigint DEFAULT NULL COMMENT '过期时间',
                                    `state` int NOT NULL DEFAULT '0' COMMENT '0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5: 评测中 6: 结果生成中 7: 任务结束',
                                    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `model_id` int DEFAULT '0' COMMENT '模型ID',
                                    `model_name` text COMMENT '模型名称',
                                    `replica_num` int DEFAULT '0' COMMENT '副本数量',
                                    `replica_gpu_num` int DEFAULT '0' COMMENT '副本gpu数量',
                                    `resource_group` text COMMENT '资源组',
                                    `resource_pool` text COMMENT '资源池',
                                    `deleted` int DEFAULT '0' COMMENT '1-已删除0-未删除',
                                    `gpu_type` text COMMENT 'gpu类型',
                                    `model_parameters` text COMMENT '模型参数',
                                    `create_user_id` text COMMENT '创建人',
                                    `update_user_id` text COMMENT '创建人',
                                    `error_msg` text COMMENT '错误信息',
                                    `model_type` int DEFAULT NULL COMMENT '模型类型1-base2-chat3-embedding4-reranker',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `plan_id` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_plan_snapshot` (
                                             `id` int NOT NULL AUTO_INCREMENT,
                                             `evaluation_id` varchar(500) NOT NULL COMMENT '任务id',
                                             `question_set_config` text COMMENT '题集配置',
                                             `question_set_identifier` varchar(500) DEFAULT NULL COMMENT '题集UUid',
                                             `user_set_config` text COMMENT '评测组配置',
                                             `user_set_identifier` varchar(500) DEFAULT NULL COMMENT '用户集uuid',
                                             `inference_config` text COMMENT '推理配置',
                                             `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_plan_task` (
                                         `id` int NOT NULL AUTO_INCREMENT,
                                         `evaluation_id` varchar(100) NOT NULL COMMENT '唯一的评审ID',
                                         `task_id` varchar(100) NOT NULL COMMENT '任务ID',
                                         `user_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
                                         `task_name` varchar(100) NOT NULL COMMENT '任务名称',
                                         `task_desc` text COMMENT '描述',
                                         `system_version` varchar(255) DEFAULT NULL COMMENT '系统版本号',
                                         `criteria_set_identifier` varchar(255) DEFAULT NULL COMMENT '评审标准集合版本号',
                                         `qa_round_criteria_set_identifier` varchar(255) DEFAULT NULL COMMENT 'QA轮次标准集合版本号',
                                         `dim_set_identifier` varchar(255) DEFAULT NULL COMMENT '评审维度集合版本号',
                                         `qa_round_dim_set_identifier` varchar(255) DEFAULT NULL COMMENT 'QA轮次维度集合版本号',
                                         `system_version_info` varchar(255) DEFAULT NULL COMMENT '系统版本信息',
                                         `question_set_identifier` varchar(255) DEFAULT NULL COMMENT '问题集合版本号',
                                         `evaluation_state` int DEFAULT '0' COMMENT '评审任务状态',
                                         `evaluation_type` varchar(100) DEFAULT 'art' COMMENT '评测类型  ab测试: ab、人工评测: art',
                                         `task_expire` timestamp NULL DEFAULT NULL COMMENT '任务过期时间',
                                         `task_state` int NOT NULL DEFAULT '0' COMMENT '0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4: 待评测 5: 评测中  6: 结果生成中 7: 任务结束',
                                         `task_num` int DEFAULT '0' COMMENT '任务数量',
                                         `task_answer_num` int DEFAULT '0' COMMENT '已获取答案的任务数量',
                                         `task_evaluation_num` int DEFAULT '0' COMMENT '已评测任务的数量',
                                         `eval_product` text COMMENT '评测产品',
                                         `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `created_user` text COMMENT '创建人',
                                         `created_user_name` text COMMENT '创建人',
                                         `error_msg` text COMMENT '错误信息',
                                         PRIMARY KEY (`id`),
                                         KEY `ix_system_quiz_plan_task_task_id` (`task_id`),
                                         KEY `ix_system_quiz_plan_task_task_state` (`task_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_plan_task_info` (
                                              `id` int NOT NULL AUTO_INCREMENT,
                                              `task_id` varchar(100) NOT NULL COMMENT '任务ID',
                                              `parent_task_id` varchar(100) NOT NULL COMMENT '父任务ID',
                                              `user_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
                                              `question_id` int NOT NULL COMMENT '问题ID',
                                              `evaluation_id` varchar(100) DEFAULT NULL COMMENT '评审ID',
                                              `system_answer` text COMMENT '系统答案',
                                              `rating` int DEFAULT '0' COMMENT '评分',
                                              `system_type` varchar(100) DEFAULT 'art' COMMENT '评测方式  a系统: a、b系统: b、人工评测: art',
                                              `trace_id` varchar(255) DEFAULT NULL,
                                              `feedback` text COMMENT '反馈',
                                              `source` text COMMENT '召回数据',
                                              `task_state` int NOT NULL DEFAULT '0' COMMENT '0: 未开始 1: 生成中  2: 生成完成 3: 生成失败 4: 答题进行中 5: 答题完成  6: 答题过期',
                                              `system_url` varchar(255) DEFAULT NULL COMMENT '系统url',
                                              `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                              `question` text COMMENT '问题',
                                              `created_user` text COMMENT '创建人',
                                              `created_user_name` text COMMENT '创建人',
                                              PRIMARY KEY (`id`),
                                              KEY `ix_system_quiz_plan_task_info_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_question_set_relations` (
                                                      `id` int NOT NULL AUTO_INCREMENT,
                                                      `question_set_identifier` varchar(255) NOT NULL COMMENT '问题集合版本号',
                                                      `question_id` int NOT NULL COMMENT '问题ID',
                                                      `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                                                      `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_question_set_relations_v2` (
                                                         `id` int NOT NULL AUTO_INCREMENT,
                                                         `question_set_identifier` varchar(255) NOT NULL COMMENT '问题集合版本号',
                                                         `question_id` varchar(50) NOT NULL COMMENT '问题ID',
                                                         `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                                                         `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_question_sets` (
                                             `id` int NOT NULL AUTO_INCREMENT,
                                             `question_set_identifier` varchar(255) NOT NULL COMMENT '问题集合版本号',
                                             `name` varchar(255) NOT NULL COMMENT '问题集合名称',
                                             `desc` text COMMENT '描述',
                                             `questions_number` int DEFAULT '0' COMMENT '问题数量',
                                             `version` int DEFAULT '0' COMMENT '版本',
                                             `created_user_id` varchar(100) DEFAULT NULL COMMENT '创建人',
                                             `updated_user_id` varchar(100) DEFAULT NULL COMMENT '更新人',
                                             `state` int DEFAULT '1' COMMENT '状态 0: 未发布 1: 可用 2: 不可用 3: 删除态',
                                             `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                                             `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_question_types` (
                                              `id` int NOT NULL AUTO_INCREMENT,
                                              `question_type` varchar(100) NOT NULL DEFAULT 'qa' COMMENT '题集类型 问答: qa单选题: single_select多选题: multiple_select填空题: fill_in_the_blank判断题: true_or_false多轮对话: multi_round_qa',
                                              `question_type_desc` varchar(100) NOT NULL DEFAULT '' COMMENT '题型描述',
                                              `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                                              `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_questions` (
                                         `id` int NOT NULL AUTO_INCREMENT COMMENT '问题ID',
                                         `question` varchar(512) NOT NULL COMMENT '问题',
                                         `question_desc` text NOT NULL COMMENT '问题描述',
                                         `question_type` varchar(100) NOT NULL DEFAULT 'qa' COMMENT '题集类型 问答: qa单选题: single_select多选题: multiple_select填空题: fill_in_the_blank判断题: true_or_false',
                                         `llm_q_list` text NOT NULL COMMENT '填空题问题集',
                                         `options` text COMMENT '选择题选项',
                                         `reference_answer` text NOT NULL COMMENT '参考答案',
                                         `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                                         `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_questions_v2` (
                                            `id` int NOT NULL AUTO_INCREMENT COMMENT '问题ID',
                                            `question_id` varchar(50) NOT NULL,
                                            `question` varchar(512) NOT NULL COMMENT '问题',
                                            `question_desc` text COMMENT '描述',
                                            `question_type_id` int NOT NULL COMMENT '题型ID',
                                            `question_type` varchar(100) NOT NULL DEFAULT 'qa' COMMENT '题集类型 问答: qa单选题: single_select多选题: multiple_select填空题: fill_in_the_blank判断题: true_or_false多轮对话: multi_round_qa',
                                            `difficulty` int DEFAULT NULL COMMENT '难度等级',
                                            `category` varchar(100) DEFAULT NULL COMMENT '题目分类',
                                            `explanation` text COMMENT '题目解释',
                                            `version` int DEFAULT '0' COMMENT '版本',
                                            `details` json DEFAULT NULL COMMENT '题目详情',
                                            `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0: 未删除 1: 已删除',
                                            `created_user_id` varchar(100) DEFAULT NULL COMMENT '创建人',
                                            `updated_user_id` varchar(100) DEFAULT NULL COMMENT '更新人',
                                            `state` int DEFAULT '1' COMMENT '状态 0: 未发布 1:  可用 2: 不可用 3: 删除态',
                                            `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                                            `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`),
                                            UNIQUE KEY `ix_question_id_version` (`question_id`,`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_tasks` (
                                     `id` int NOT NULL AUTO_INCREMENT,
                                     `evaluation_id` varchar(255) NOT NULL COMMENT '唯一的评审ID',
                                     `system_version` varchar(255) NOT NULL COMMENT '系统版本号',
                                     `system_version_info` varchar(255) NOT NULL COMMENT '系统版本信息',
                                     `criteria_set_identifier` varchar(255) NOT NULL COMMENT '评审标准集合版本号',
                                     `question_set_identifier` varchar(255) NOT NULL COMMENT '问题集合版本号',
                                     `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `evaluation_id` (`evaluation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_user_set_relations` (
                                                  `id` int NOT NULL AUTO_INCREMENT,
                                                  `user_id` varchar(256) NOT NULL COMMENT '评测人员ID',
                                                  `user_set_identifier` varchar(255) NOT NULL COMMENT '评测人员集合版本号',
                                                  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                  `version` int DEFAULT '0' COMMENT '版本',
                                                  PRIMARY KEY (`id`),
                                                  UNIQUE KEY `_user_set_identifier_version_user_id_uc` (`user_set_identifier`,`version`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `system_quiz_user_sets` (
                                         `id` int NOT NULL AUTO_INCREMENT,
                                         `user_set_identifier` varchar(255) NOT NULL COMMENT '评测人员集合版本号',
                                         `name` varchar(255) NOT NULL COMMENT '评测人员集合名称',
                                         `quiz_type` varchar(100) NOT NULL DEFAULT 'art' COMMENT '评测类型  AB评测:ab, 人工评测:art, 自评测试:objective',
                                         `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `created_user` varchar(100) DEFAULT '0' COMMENT '创建人',
                                         `updated_user` varchar(100) DEFAULT NULL COMMENT '更新人',
                                         `version` int DEFAULT '0' COMMENT '版本',
                                         `description` text COMMENT '描述',
                                         `state` int DEFAULT '1' COMMENT '状态 0: 未发布 1:  可用 2: 不可用 3: 删除态',
                                         PRIMARY KEY (`id`),
                                         UNIQUE KEY `user_set_identifier` (`user_set_identifier`),
                                         UNIQUE KEY `_user_set_identifier_version_uc` (`user_set_identifier`,`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `user` (
                        `id` int NOT NULL AUTO_INCREMENT,
                        `user_id` tinytext NOT NULL,
                        `name` tinytext NOT NULL COMMENT '系统用户名，例如邮箱前缀等',
                        `full_name` tinytext NOT NULL COMMENT '用户姓名',
                        `department` tinytext NOT NULL COMMENT '用户部门',
                        `permission` json DEFAULT NULL COMMENT '用户权限',
                        `state` int NOT NULL,
                        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `user_group` (
                              `id` int NOT NULL AUTO_INCREMENT,
                              `group_id` tinytext NOT NULL,
                              `name` tinytext NOT NULL COMMENT '用户组名称',
                              `parent_id` int NOT NULL COMMENT '父用户组ID',
                              `permission` json DEFAULT NULL COMMENT '用户组权限',
                              `state` int NOT NULL,
                              `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                              `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `user_quiz_infos` (
                                   `id` int NOT NULL AUTO_INCREMENT,
                                   `evaluation_id` varchar(255) NOT NULL COMMENT '唯一的评审ID',
                                   `user_id` varchar(255) NOT NULL COMMENT '用户ID',
                                   `system_version` varchar(255) NOT NULL COMMENT '系统版本号',
                                   `system_version_info` varchar(255) NOT NULL COMMENT '系统版本信息',
                                   `criteria_set_identifier` varchar(255) NOT NULL COMMENT '评审标准集合版本号',
                                   `question_set_identifier` varchar(255) NOT NULL COMMENT '问题集合版本号',
                                   `evaluation_state` int NOT NULL DEFAULT '0' COMMENT '评审任务状态',
                                   `task_count` int NOT NULL DEFAULT '0' COMMENT '评审任务总数',
                                   `completed_count` int NOT NULL DEFAULT '0' COMMENT '已完成任务数量',
                                   `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `evaluation_id` (`evaluation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `user_quiz_tasks` (
                                   `id` int NOT NULL AUTO_INCREMENT,
                                   `evaluation_id` varchar(100) NOT NULL COMMENT '唯一的评审ID',
                                   `task_id` varchar(100) NOT NULL COMMENT '任务ID',
                                   `user_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
                                   `task_name` varchar(100) DEFAULT '0' COMMENT '任务名称',
                                   `task_desc` text COMMENT '描述',
                                   `system_version` varchar(255) DEFAULT NULL COMMENT '系统版本号',
                                   `criteria_set_identifier` varchar(255) DEFAULT NULL COMMENT '评审标准集合版本号',
                                   `qa_round_criteria_set_identifier` varchar(255) DEFAULT NULL COMMENT 'QA轮次标准集合版本号',
                                   `dim_set_identifier` varchar(255) DEFAULT NULL COMMENT '评审维度集合版本号',
                                   `qa_round_dim_set_identifier` varchar(255) DEFAULT NULL COMMENT 'QA轮次维度集合版本号',
                                   `system_version_info` varchar(255) DEFAULT NULL COMMENT '系统版本信息',
                                   `question_set_identifier` varchar(255) DEFAULT NULL COMMENT '问题集合版本号',
                                   `evaluation_state` int DEFAULT '0' COMMENT '评审任务状态',
                                   `evaluation_type` varchar(100) DEFAULT 'art' COMMENT '评测类型  ab测试: ab、人工评测: art',
                                   `task_expire` timestamp NULL DEFAULT NULL COMMENT '任务过期时间',
                                   `task_state` int NOT NULL DEFAULT '0' COMMENT '1: 未开始  2: 评测中 3: 评测完成',
                                   `task_num` int DEFAULT '0' COMMENT '任务数量',
                                   `task_answer_num` int DEFAULT '0' COMMENT '已获取答案的任务数量',
                                   `task_evaluation_num` int DEFAULT '0' COMMENT '已评测任务的数量',
                                   `eval_product` text COMMENT '评测产品',
                                   `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `plan_id` int DEFAULT '0' COMMENT '计划id',
                                   `created_user` text COMMENT '创建人',
                                   `created_user_name` text COMMENT '创建人',
                                   PRIMARY KEY (`id`),
                                   KEY `ix_user_quiz_tasks_task_id` (`task_id`),
                                   KEY `ix_user_quiz_tasks_task_state` (`task_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `user_quiz_tasks_criteria_info` (
                                                 `id` int NOT NULL AUTO_INCREMENT,
                                                 `task_id` varchar(100) NOT NULL COMMENT '任务ID',
                                                 `user_id` varchar(100) NOT NULL COMMENT '用户ID',
                                                 `question_id` int NOT NULL COMMENT '问题ID',
                                                 `title` varchar(100) NOT NULL COMMENT '标题',
                                                 `rating_key` varchar(100) NOT NULL COMMENT '评审标准KEY',
                                                 `rating_type` varchar(256) DEFAULT '0' COMMENT '评分标准类型',
                                                 `rating_int_value` int NOT NULL COMMENT '评分',
                                                 `rating_string_value` varchar(255) NOT NULL COMMENT '评分',
                                                 `system_type` varchar(100) NOT NULL DEFAULT 'art' COMMENT '评测方式  ab测试: ab、人工评测: art',
                                                 `feedback` text COMMENT '反馈',
                                                 `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `user_quiz_tasks_info` (
                                        `id` int NOT NULL AUTO_INCREMENT,
                                        `task_id` varchar(100) NOT NULL COMMENT '任务ID',
                                        `parent_task_id` varchar(100) NOT NULL COMMENT '父任务ID',
                                        `user_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
                                        `question_id` int NOT NULL COMMENT '问题ID',
                                        `evaluation_id` varchar(100) DEFAULT NULL COMMENT '评审ID',
                                        `system_answer` text COMMENT '系统答案',
                                        `rating` int DEFAULT '0' COMMENT '评分',
                                        `system_type` varchar(100) DEFAULT 'art' COMMENT '评测方式  ab测试: ab、人工评测: art',
                                        `trace_id` varchar(255) DEFAULT NULL COMMENT '追踪ID',
                                        `feedback` text COMMENT '反馈',
                                        `source` text COMMENT '召回数据',
                                        `task_state` int NOT NULL DEFAULT '0' COMMENT '0: 未开始 1: 评分中  2: 评分完成',
                                        `system_url` varchar(255) DEFAULT NULL COMMENT '系统url',
                                        `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `plan_id` int DEFAULT '0' COMMENT '计划id',
                                        `question` text COMMENT '问题',
                                        `created_user` text COMMENT '创建人',
                                        `created_user_name` text COMMENT '创建人',
                                        PRIMARY KEY (`id`),
                                        KEY `ix_user_quiz_tasks_info_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

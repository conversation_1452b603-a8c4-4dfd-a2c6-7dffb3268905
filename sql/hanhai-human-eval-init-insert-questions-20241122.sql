INSERT INTO `system_quiz_questions_v2` VALUES (2053, 'e8bb917c-ebf3-11ee-9fe2-367dda9b3589', '如何开通KS3服务', '在对象存储工单中，该问题比较集中\n开通KS3需要开工单或联系自己的商务\n在KS3开通前需要完成金山云账户注册等操作\n同时要求回答具备逻辑性，比如先开通账号再开通KS3', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"KS3服务是是金山云提供的无限制、多备份、分布式的低成本存储空间解决方案。目前开通需要如下步骤，如没有注册金山云账号，请先注册\\n1. 注册金山云账号，进入链接https://passport.ksyun.com/register.html\\n2. 完成实名认证，实名认证过程可参考https://docs.ksyun.com/documents/6810\\n如已有金山云账号\\n1. 登录KS3控制台\\n2. 点击提交工单或联系在线客服开通KS3服务\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:50', '2024-03-27 20:38:50');
INSERT INTO `system_quiz_questions_v2` VALUES (2054, 'e8ecd778-ebf3-11ee-9fe2-367dda9b3589', '云主机CPU和内存升配如何操作', '给出升配操作的具体步骤\n可以给出其他扩展知识，如何生效等注意事项\n优先解答操作的步骤，再进行知识扩展', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"金山云的云主机产品支持CPU、内存、系统盘、数据盘的配置调整，升级套餐操作需登陆KEC控制台，默认进入云服务器 > 实例页面，选择操作“更多 > 资源调整 > 调整配置进行调整配置；\\n注意，升级配置不支持竞价实例，如在运行中升级需重启云服务器\\n同时KEC产品也支持降配操作，降配有可能造成业务受损，请谨慎操作\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:51', '2024-03-27 20:38:51');
INSERT INTO `system_quiz_questions_v2` VALUES (2055, 'e92b40ee-ebf3-11ee-9fe2-367dda9b3589', '从阿里云OSS迁移到KS3怎么做', '给出准确的回复，可以支持阿里云迁移\n可以给出至少两种迁移方式\n可以描述迁移的步骤，以及给出详细的官网链接', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"金山云对象存储KS3支持从阿里云、百度云、七牛云、腾讯云等多家对象存储迁移至KS3，支持在线迁移和工具两种\\n在线迁移操作过程：登陆管理控制台，配置源站账户的AK/SK，确定目标站信息，创建迁移任务\\n迁移工具支持KS3-import，具体步骤：下载KS3-Import工具，修改配置文件srctype为oss，填写AK SK等关键信息，填写dstDomain、dstBucket等信息，java -jar KS3-import-x.x.x.jar -c xxx.conf start\\n详见：https://docs.ksyun.com/documents/895\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:51', '2024-03-27 20:38:51');
INSERT INTO `system_quiz_questions_v2` VALUES (2056, 'e97e09be-ebf3-11ee-9fe2-367dda9b3589', '负载均衡支持的最大带宽是多少', '准确给出具体数值，同时给出产品的其他限制', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"最大内网带宽支持8G\\n其他产品规格限制：\\n新建连接数CPS：无限制\\n并发连接数：监听器协议类型为tcp限制100w，监听器协议类型为udp限制为50w\\n每秒请求数QPS：单client 1w\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:52', '2024-03-27 20:38:52');
INSERT INTO `system_quiz_questions_v2` VALUES (2057, 'e9a9c96e-ebf3-11ee-9fe2-367dda9b3589', 'DDos实例是否支持调整带宽', '准确给出支持，并给出可以调整的区间', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"支持\\n单个防护实例业务带宽：100M–15Gbps\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:52', '2024-03-27 20:38:52');
INSERT INTO `system_quiz_questions_v2` VALUES (2058, 'e9f2c95c-ebf3-11ee-9fe2-367dda9b3589', '如何将KS3挂载到云主机上', '解决方案问题', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"KS3支持Linux和macOS挂载KS3存储桶在本地文件系统，支持多种开源工具挂载，包括goofys、s3fs等\\ngoofys参考：https://docs.ksyun.com/documents/42566?type=3\\ns3fs：https://docs.ksyun.com/documents/42909?type=3\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:53', '2024-03-27 20:38:53');
INSERT INTO `system_quiz_questions_v2` VALUES (2059, 'ea31819c-ebf3-11ee-9fe2-367dda9b3589', '弹性EIP公网出入带宽是不是一致的', '本问题是明确答案，但客户实际体验不一样，要关注产品的动态，同时阐述清晰产品的标准能力', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"原则上金山云会分配给用户与购买出流量带宽1：1的公网入带宽。金山云在当前可用区整体入流量带宽低于出流量带宽时，会放开用户入流量带宽的限制，允许一定量的超出，增强用户体验。自2023年4月1号后，购买带宽小于10Mbps，入机房最大放开到10Mbps，购买带宽大于等于10Mbps，出机房和入机房仍 1:1 限速。\\n\\n当金山云当前可用区整体入流量带宽大于出流量带宽时，会重新限制用户的入流量带宽，且优先限制入流量带宽与出流量带宽有极端差异的用户。\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:53', '2024-03-27 20:38:53');
INSERT INTO `system_quiz_questions_v2` VALUES (2060, 'ea833ce4-ebf3-11ee-9fe2-367dda9b3589', '如何配置云主机访问外网', '介绍两种方式，同时给出两种方式的具体限制', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"金山云VPC为您提供灵活、高性能的互联网连接方式，包括弹性IP、公网NAT。\\nEIP支持动态多线BGP，电信，联通，移动等多种线路，单个EIP支持的默认带宽峰值为200Mbps，最大支持15Gbps\\nNAT (Network Address Translation)网络地址转换是一种将虚拟私有网络中内网IP 地址和公网IP地址进行转换的网关，能够让虚拟私有网络内无公网IP的云服务器或云物理主机访问互联网。NAT支持最大满足15Gbps流量\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:54', '2024-03-27 20:38:54');
INSERT INTO `system_quiz_questions_v2` VALUES (2061, 'eaca0c50-ebf3-11ee-9fe2-367dda9b3589', '推荐一款云盘可以满足磁盘写入在500MB/s以上', '给出计算过程', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"推荐使用ESSD PL2，同时至少要开通768GB大小才可以满足性能要求\\nESSD各等级容量吞吐计算方法如下\\nPL0：min{100+0.25×容量（GB），180}\\nPL1：min{120+0.5×容量（GB），350}\\nPL2：min{120+0.5×容量（GB），1000}\\nPL3：min{120+0.5*容量（GB），4000}\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:54', '2024-03-27 20:38:54');
INSERT INTO `system_quiz_questions_v2` VALUES (2062, 'eb057a9c-ebf3-11ee-9fe2-367dda9b3589', '容器服务和容器实例有什么区别', '可以给出核心区别，同时分别描述两个产品', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"金山云容器服务（Kingsoft Cloud Container Engine，KCE）基于原生的Kubernetes进行开发和适配，整合了金山云虚拟化、网络、存储等能力，为客户提供高可靠、高性能、高度可扩展的容器管理服务\\n金山云容器实例（Kingsoft Cloud Container Instance，简称KCI）为您提供Serverless化的容器服务，您无需预购和管理底层服务器，即可在云端运行容器，并仅为容器实际运行消耗的资源付费。\\n它们的区别在于：1. 功能特点：容器服务提供了更全面的容器管理功能，包括创建和管理集群、节点、应用、配置等，具有更高的灵活性和扩展性。而容器实例是一种更轻量级的容器解决方案，主要用于快速部署和运行容器应用。\\n2. 使用方式：容器服务需要用户自行创建和管理集群和节点，可以通过API或控制台进行操作。而容器实例是一种无需用户管理基础设施的容器解决方案，只需提供容器镜像和相应的配置参数即可创建和运行容器实例。\\n3. 管理复杂度：容器服务相对于容器实例来说，具有更高的管理复杂度，需要用户自行管理集群和节点、配置调度策略等。而容器实例则更简单易用，无需关注底层基础设施管理，只需关注容器应用本身即可。\\n\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:54', '2024-03-27 20:38:54');
INSERT INTO `system_quiz_questions_v2` VALUES (2063, 'eb3ee638-ebf3-11ee-9fe2-367dda9b3589', '金山云有与阿里对标的日志服务产品吗', '给出明确答复，同时给出功能清单进行佐证', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"金山云有日志服务产品，日志服务（KLog）是针对日志类数据的一站式服务，提供从日志采集、存储、加工、检索分析、实时消费、数据投递等多项服务，提升运维、运营效率。用户无需关注资源扩容问题，五分钟即可快速便捷接入，享受稳定、可靠、智能的日志服务。完全可以对标阿里云日志服务\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:55', '2024-03-27 20:38:55');
INSERT INTO `system_quiz_questions_v2` VALUES (2064, 'eb73ed6a-ebf3-11ee-9fe2-367dda9b3589', 'Kafka支持鉴权吗', '支持，同时给出操作方法', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"支持鉴权。通过控制台配置 SASL 鉴权和 ACL 规则，增强对公网/内网传输中的用户访问控制，增加对 Topic 等资源的生产消费权限控制。\\n操作可以参考https://docs.ksyun.com/documents/41930\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:55', '2024-03-27 20:38:55');
INSERT INTO `system_quiz_questions_v2` VALUES (2065, 'ebb8afb8-ebf3-11ee-9fe2-367dda9b3589', 'S6云主机4核8G的价格是多少', '给出明确计算结果', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"目录价包年包月576元/月，按量付费0.7元/小时\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:56', '2024-03-27 20:38:56');
INSERT INTO `system_quiz_questions_v2` VALUES (2066, 'ebeab832-ebf3-11ee-9fe2-367dda9b3589', 'CDN执行目录刷新后，对应的资源请求如何处理？', '答案准确描述目录刷新后资源请求的处理方式。\n答案完整描述回源校验的过程。\n', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"目录刷新指令的有效期内，涉及目录下对应资源的请求都会进行回源校验。\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:56', '2024-03-27 20:38:56');
INSERT INTO `system_quiz_questions_v2` VALUES (2067, 'ec2346a2-ebf3-11ee-9fe2-367dda9b3589', '当尝试在金山云上为我的域名接入CDN时，为什么会提示该域名已添加？', '答案是否明确并准确地解释了系统提示域名已添加的可能原因以及如何解决这个问题。\n答案中提供的信息和建议是否都是正确的，没有误导或错误的内容。\n答案是否完整地描述了问题的原因以及推荐的解决方案。\n答案是否按照一定的逻辑顺序进行排列，如从问题的原因到解决方案。', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"当您在金山云CDN添加新的加速域名时，可能会提示该域名已添加的原因是其他用户之前创建了该域名。如果您确实拥有该域名，请提交工单进行处理。\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:56', '2024-03-27 20:38:56');
INSERT INTO `system_quiz_questions_v2` VALUES (2068, 'ec646344-ebf3-11ee-9fe2-367dda9b3589', '想了解控制台和API的带宽数据统计方法。它们是采用前打点方式还是后打点方式来统计数据点的？', '根据实际情况和答案内容，判断答案是否准确描述了数据点的统计方式。\n判断答案是否正确地回答了问题，并没有提供错误的信息。\n判断答案是否完整地描述了数据点的统计方式，包括打点方式和统计时间范围。', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"采用前打点方式，例如5分钟粒度数据点，0:05的数据点统计数据为0:05:00-0:09:59的数据。\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:57', '2024-03-27 20:38:57');
INSERT INTO `system_quiz_questions_v2` VALUES (2069, 'ec95b840-ebf3-11ee-9fe2-367dda9b3589', '在金山云的物理隔离私有云上搭建的K8S中，能否自建metalLB作为LoadBalancer来分配局域网IP？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"物理机是可以的，云主机和容器不能自建LB。\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:57', '2024-03-27 20:38:57');
INSERT INTO `system_quiz_questions_v2` VALUES (2070, 'ecca8e1c-ebf3-11ee-9fe2-367dda9b3589', 'KMR主机内存和CPU使用过高的可能原因是什么？', '答案是否直接针对问题进行了原因分析？\n答案中的步骤和建议是否正确无误？\n答案是否完整地覆盖了可能的原因和解决方案？\n答案的结构和内容是否逻辑清晰，步骤是否有序？', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"1、先和客户确认主机资源不足问题是偶然性发生还是近期持续性发生的情况；\\n\\n\\n2、偶然性发生的情况可能是业务运行高峰期原因导致；\\n      先使用云监控看下客户近一周硬件资源使用情况，是否同一时间均处于高峰期时间；\\n\\n\\n3、近期持续性发生的情况可能是硬件资源不足导致的；\\n\\n     a.通过ambari内存使用情况 观察下此主机运行的kmr组件服务（比如hbase、spark、presto等服务）是否有使用硬件资源异常的情况；\\n\\n     b.确认客户此主机是否安装有非kmr组件服务的应用程序（非标安装其他服务）；\\n     c.沟通客户此主机是否提交crontab 定时执行计划，建议迁移到gateway；\\n     d.沟通客户是否使用此主机提交运行占用内存、cpu较高的presto、spark等任务，建议迁移到gateway；\\n\\n     e.使用linux系统相关的top、free命令梳理占用资源较高的应用程序，建议客户调整到其他主机。\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:57', '2024-03-27 20:38:57');
INSERT INTO `system_quiz_questions_v2` VALUES (2071, 'ed0ba406-ebf3-11ee-9fe2-367dda9b3589', '专线与对等连接的VPC网络中断，如何进行故障排查？', '答案是否直接针对问题提供了排查方法？\n答案中的排查步骤是否正确无误？\n答案是否完整地覆盖了可能的排查方向和方法？\n答案的结构和内容是否逻辑清晰，步骤是否有序？', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"1，排查IDC机房路由配置是否添加了云上资源的回程路由。\\n2，排查对等连接的vpc是否自定义路由加上了云下IDC机房的路由。\\n3，排查对等连接的vpc路中，在专线中的其它网段是否添加该vpc的路由。\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:58', '2024-03-27 20:38:58');
INSERT INTO `system_quiz_questions_v2` VALUES (2072, 'ed356930-ebf3-11ee-9fe2-367dda9b3589', 'Mysql实例在公有云上到期后，如果我不续费，会有何后果？', '答案是否准确地描述了公有云Mysql实例到期未续费的影响？\n答案中提到的“实例进入锁定状态”和“只读”是否与实际情况相符？\n答案中的描述是否与公有云Mysql实例到期未续费的实际影响相符？\n答案中关于“续费之后自动解除”的描述是否正确？\n答案是否描述了实例的状态、可用性以及续费后的变化？\n答案是否按照实例状态、可用性和续费后的变化的顺序进行描述？', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"实例进入锁定状态，只读，续费之后自动解除。\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:58', '2024-03-27 20:38:58');
INSERT INTO `system_quiz_questions_v2` VALUES (2073, 'ed6c8bcc-ebf3-11ee-9fe2-367dda9b3589', '金山云监控可以用grafana配置数据源直接看吗？', '答案是否准确地回答了关于金山云监控是否可以用grafana配置数据源直接查看的问题。\n是否涵盖了金山云监控的两种类型（基础监控和业务监控）及其与grafana的兼容性。', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"基础监控可以，其中基础监控基于open-falcon/prometheus 可直接通过对应接口进行获取。业务监控不可以，业务监控基于大数据平台自定义进行展示的。\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:58', '2024-03-27 20:38:58');
INSERT INTO `system_quiz_questions_v2` VALUES (2074, 'eda81ebc-ebf3-11ee-9fe2-367dda9b3589', '云直播的外网拉流是做什么用的，逻辑是什么?', '答案是否准确地回答了关于云直播的外网拉流的用途和逻辑。\n答案中的信息是否正确，没有误导或错误的信息。\n答案是否完整，是否涵盖了外网拉流的主要功能和限制', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"外网拉流其实就是去三方源站拉流，当客户的源站不在金山时，CDN上层直接回三方源站拉流。注意这种场景不支持录像、截图、开关播回调等具有推流属性的功能。\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:59', '2024-03-27 20:38:59');
INSERT INTO `system_quiz_questions_v2` VALUES (2075, 'eddb958a-ebf3-11ee-9fe2-367dda9b3589', 'ks3生命周期这如何配置，是否可配置到目录', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"1、生命周期创建流程\\nKS3支持在特定时间点或对象最后一次被修改后的一段时间，进行批量删除对象或者批量转换对象的存储类型。为实现此目的，用户需要在存储空间上创建生命周期管理规则。\\n2、可以配置到目录\\n生命周期规则匹配逻辑\\n前缀规则生效说明\\n只要Object名称前缀和一条规则的前缀匹配，那么该规则就适用于它。例如，一个Bucket有如下几个Object：\\nlogs/program.log.1\\nlogs/program.log.2\\nlogs/program.log.3\\ndoc/readme.txt\\n如果一个规则指定的前缀是logs/，那么该规则就适用于前三个以logs/开头的Object；如果前缀是doc/readme.txt，那么这条规则就只对doc/readme.txt起作用。\\n详情请参考：https://docs.ksyun.com/documents/6801\\n\"}', 0, '23533', NULL, 1, '2024-03-27 20:38:59', '2024-03-27 20:38:59');
INSERT INTO `system_quiz_questions_v2` VALUES (2076, 'ee1f2110-ebf3-11ee-9fe2-367dda9b3589', '金山云集群可以使用自定义镜像仓库吗？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"1、镜像仓库是用户存放Docker镜像的地方，金山云容器镜像服务提供用户提供安全可靠的企业级私有镜像仓库，用户可将私有的镜像托管至私有的镜像仓库中，方便管理。\\n2、目前金山云支持Docker Hub官方镜像，用户私有镜像以及Ksyun Hub镜像。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:00', '2024-03-27 20:39:00');
INSERT INTO `system_quiz_questions_v2` VALUES (2077, 'ee4bbbf8-ebf3-11ee-9fe2-367dda9b3589', '如何查看AK/SK的使用时长', ' 时长计算方式理论上正确，但是金山云不具备计算条件\n有逻辑性，不支持直接查看但是给出了计算方式，但是不是金山云的使用方法', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"目前没有办法查询到AK/SK的使用时间与记录，如果是关联到子账号的登陆时间，可以在操作审计中查看\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:00', '2024-03-27 20:39:00');
INSERT INTO `system_quiz_questions_v2` VALUES (2078, 'ee77202c-ebf3-11ee-9fe2-367dda9b3589', '交换机出问题了，vm还能热迁移吗', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"1、交换机故障也分为几个级别，如果是轻微的内存告警等，正常是不影响云主机的使用，如果是严重的宕机，极端情况下会导致tor下的云主机不可用的。\\n2、金山侧目前交换机硬件都是有冗余的，所以一个端口宕了，会切换到另一个端口，这个切换流量的过程中，会有次抖动。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:00', '2024-03-27 20:39:00');
INSERT INTO `system_quiz_questions_v2` VALUES (2079, 'eeba52a2-ebf3-11ee-9fe2-367dda9b3589', '目前WAF有全局黑名单功能，加一个黑名单ip，对所有域名生效么？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"1、网站接入Web应用防火墙后，您自定义基于精准匹配条件的访问控制规则。可用于盗链防护、网站管理后台保护等场景。您可以根据实际需求配置自定义规则。参考官网文档；https://docs.ksyun.com/documents/39742?type=3\\n2、目前只支持单个域名配置，不支持全局配置黑名单\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:01', '2024-03-27 20:39:01');
INSERT INTO `system_quiz_questions_v2` VALUES (2080, 'eef2adb4-ebf3-11ee-9fe2-367dda9b3589', '对象存储ks3中归档文件解冻后如何再次冻结？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"1、文件解冻操作只限于归档存储文件。\\n文件上传到归档存储空间，自动存储为归档文件，默认处于冷冻状态，在访问文件之前，需要执行解冻操作，才可以访问文件。\\n操作详情：https://docs.ksyun.com/documents/875\\n2、文件解冻后，类型依旧属于归档文件，只是提供下载功能，如不再次延长解冻，默认24小时后回到冷冻状态。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:01', '2024-03-27 20:39:01');
INSERT INTO `system_quiz_questions_v2` VALUES (2081, 'ef20f520-ebf3-11ee-9fe2-367dda9b3589', '对象存储ks3访问日志如何配置？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"1.确认创建有ks3操作权限的子账号，进入ks3控制台，需要具体配置的bucket内容管理界面；\\n2.空间设置-基础设置，找到日志状态设置进行编辑，设置存储日志的bucket及前缀，第二天即可自动生成日志。\\nhttps://docs.ksyun.com/documents/6797?type=3\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:01', '2024-03-27 20:39:01');
INSERT INTO `system_quiz_questions_v2` VALUES (2082, 'ef5a474e-ebf3-11ee-9fe2-367dda9b3589', '负载均衡是否支持在上传ca证书时配置双向认证?', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"金山侧暂不支持双向认证\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:02', '2024-03-27 20:39:02');
INSERT INTO `system_quiz_questions_v2` VALUES (2083, 'ef9014f0-ebf3-11ee-9fe2-367dda9b3589', '如何处理标签管理添加资源时提示已超限的报错', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"1、检查标签的限额是不是已满\\nhttps://docs.ksyun.com/documents/39806\\n2、如果标签额度还有很大空间，请根据具体的报错信息升级后端处理\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:02', '2024-03-27 20:39:02');
INSERT INTO `system_quiz_questions_v2` VALUES (2084, 'efc35fea-ebf3-11ee-9fe2-367dda9b3589', 'mongo查不到数据了可能是哪些原因？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"检查客户配置连接方式是否是复制集链接方式，避免直连Primary，\\n当遇到复制集轮转升级、Primary宕机、络分区等场景时，复制集可能\\n会选举出一个新的Primary，原来的Primary则会降级为Secondary，即发生主备切换，如果直接指定 Primary 的地址来连接，当时可能可以正确读写数据的，但一旦复制集发生主备切换，会影响线上业务。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:02', '2024-03-27 20:39:02');
INSERT INTO `system_quiz_questions_v2` VALUES (2085, 'f000a58a-ebf3-11ee-9fe2-367dda9b3589', '高防可以透传用户的真实IP吗？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"可以获取，具体方法参见文档：https://docs.ksyun.com/documents/170?type=3\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:03', '2024-03-27 20:39:03');
INSERT INTO `system_quiz_questions_v2` VALUES (2086, 'f049b798-ebf3-11ee-9fe2-367dda9b3589', 'EPC上如何配置金山云的yum源？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"在EPC上配置金山云的yum源，可以按照以下步骤进行操作：\\n\\n登录EPC实例。\\n执行以下命令，备份当前的yum源配置文件：\\nsudo cp /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.bak\\n执行以下命令，下载金山云的yum源配置文件：\\nsudo wget -O /etc/yum.repos.d/CentOS-Base.repo http://mirrors.ksyun.com/centos/7.9.2009/BaseOS/x86_64/os/CentOS-Base.repo\\n注意：此处的链接是示例链接，如果需要其他版本的CentOS，请根据实际情况替换链接中的版本号。\\n执行以下命令，更新系统的软件包信息：\\nsudo yum makecache\\n完成以上步骤后，EPC实例将配置为使用金山云的yum源进行软件包的安装和更新。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:03', '2024-03-27 20:39:03');
INSERT INTO `system_quiz_questions_v2` VALUES (2087, 'f087472a-ebf3-11ee-9fe2-367dda9b3589', '高防IP可以添加白名单吗？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"产品功能未提供，可联系运维添加\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:04', '2024-03-27 20:39:04');
INSERT INTO `system_quiz_questions_v2` VALUES (2088, 'f0c6b234-ebf3-11ee-9fe2-367dda9b3589', '控制台能支持查看托管裸金属的网卡流量吗？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"金山云控制台目前不支持直接查看托管裸金属的网卡流量。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:04', '2024-03-27 20:39:04');
INSERT INTO `system_quiz_questions_v2` VALUES (2089, 'f105cca8-ebf3-11ee-9fe2-367dda9b3589', 'mysql是放在k8s里容器化部署的，还是直接二进制包部署到主机上？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"在金山云中，mysql可以通过两种方式部署: 容器化部署和二进制包部署到主机上。所以可以根据具体需求选择适合的部署方式。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:04', '2024-03-27 20:39:04');
INSERT INTO `system_quiz_questions_v2` VALUES (2090, 'f141b7ae-ebf3-11ee-9fe2-367dda9b3589', 'MySQL库通过停库修改即可缩减磁盘空间吗？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"MySQL库的磁盘空间缩减并不是简单的停库修改就能实现的。下面是一些建议的操作步骤：\\n\\n清理无用数据：删除不再需要的数据，可以使用DELETE语句或者TRUNCATE TABLE语句。执行DELETE语句后，磁盘空间并不会立即释放，需要执行OPTIMIZE TABLE或者ALTER TABLE语句对碎片进行整理。\\n\\n优化表结构：通过ALTER TABLE语句进行表结构优化，可以缩减磁盘空间占用。例如，可以通过修改列的数据类型或者删除不必要的索引来减小表的大小。\\n\\n压缩数据文件：使用工具如myisampack可以对InnoDB表进行压缩，减小磁盘空间占用。\\n\\n清空回收站：MySQL有一个回收站功能，删除的数据并不会立即从磁盘上删除，而是移动到回收站中。可以使用PURGE RECYCLEBIN命令清空回收站，释放磁盘空间。\\n\\n需要注意的是，执行这些操作可能会对数据库的性能产生一定影响，所以建议在低峰期进行操作。同时，对于重要的数据，务必备份好数据再进行操作，以防止意外数据丢失。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:05', '2024-03-27 20:39:05');
INSERT INTO `system_quiz_questions_v2` VALUES (2091, 'f17672be-ebf3-11ee-9fe2-367dda9b3589', 'KS3存储空间可以改项目组吗？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"可以。在创建后，用户可以根据实际需要重新设置存储空间所在的项目组。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:05', '2024-03-27 20:39:05');
INSERT INTO `system_quiz_questions_v2` VALUES (2092, 'f19f9e64-ebf3-11ee-9fe2-367dda9b3589', '如何填写防火墙IP规则？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"防火墙中的IP规则接受CIDR格式，例如*************/24，代表接受192.168.0.开头的所有IP地址。关于CIDR格式可参考无类域间路由\\n\\n以下举例几种常用写法：\\n\\n0.0.0.0/0 代表所有 IP 地址。\\n\\n10.0.0.0/8 代表所有10.开头的内网地址。\\n\\n************/32 代表仅************这个IP地址。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:05', '2024-03-27 20:39:05');
INSERT INTO `system_quiz_questions_v2` VALUES (2093, 'f1f66456-ebf3-11ee-9fe2-367dda9b3589', 'KRN是什么？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"金山云资源名称（KRN）是用于在策略文档中描述一个资源的唯一标识符。它的格式形如\\\"krn:ksc::::/\\\"。KRN由多个组成部分组成，每个部分用冒号分隔。其中，\\\"ksc\\\"表示资源所属的服务类型，比如云服务器（CVM）、云数据库（CDB）等。冒号后面的部分表示资源所属的项目、地域、可用区等信息。使用KRN可以方便地定位和管理云资源。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:06', '2024-03-27 20:39:06');
INSERT INTO `system_quiz_questions_v2` VALUES (2094, 'f2407ef6-ebf3-11ee-9fe2-367dda9b3589', '在业务迁移上云时，如何创建并使用SLB?', '答案回答问题后还可能可以给出SLB的优势推荐信息', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"1. 登录负载均衡控制台。\\n2. 点击新建负载均衡。\\n3. 在负载均衡购买页面，根据需要配置负载均衡实例。\\n4. 点击进入负载均衡\\n5. 进入当前SLB的监听器页面。点击创建监听器。\\n6. 在新的窗口配置监听器。\\n7. 绑定真实服务器实例给 SLB 绑定，选择想要监听的实例，填写云服务器上要监听的网络端口和权重值，点击添加\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:07', '2024-03-27 20:39:07');
INSERT INTO `system_quiz_questions_v2` VALUES (2095, 'f2952f0a-ebf3-11ee-9fe2-367dda9b3589', 'harbor 官方文档说支持 aws s3 作为 storage ，不知道金山云 ks3 这边是否能够兼容', '除了回答这个问题可以添加兼容的具体信息', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"可以兼容的\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:07', '2024-03-27 20:39:07');
INSERT INTO `system_quiz_questions_v2` VALUES (2096, 'f2d0bf16-ebf3-11ee-9fe2-367dda9b3589', 'ES集群如果我只创建2个节点会有什么问题吗？', '推理问题，文档没有，可以推理出答案', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"可能出现脑裂问题，建议您至少选择3个节点，保证 ES 实例具有较高的节点故障容错能力\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:07', '2024-03-27 20:39:07');
INSERT INTO `system_quiz_questions_v2` VALUES (2097, 'f3030f5c-ebf3-11ee-9fe2-367dda9b3589', '我的数据如何写入新创建的ES集群', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"可以通过 Elasticsearch REST client 编写代码访问集群并将自己的数据导入到集群中，也可以通过官方提供的组件（如 logstash 和 beats）接入自己的数据\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:08', '2024-03-27 20:39:08');
INSERT INTO `system_quiz_questions_v2` VALUES (2098, 'f3400fec-ebf3-11ee-9fe2-367dda9b3589', 'RDS欠费后我的数据会被删除吗？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"包年包月实例到期后云服务进入锁定状态，只能提供读服务，无法提供写服务，需要尽快续费。高可用实例将在15天后删除，临时实例及只读实例将在1天后删除，单机版将在7天后删除，删除后此云数据库不可再使用，资源将被系统回收，数据将被清除且不可恢复。\\n按小时实时付费所有类型实例欠费2小时进入锁定状态，只能提供读服务，无法提供写服务。欠费24小时后删除，删除后此云数据库不可再使用，资源将被系统回收，数据将被清除且不可恢复。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:08', '2024-03-27 20:39:08');
INSERT INTO `system_quiz_questions_v2` VALUES (2099, 'f3918e26-ebf3-11ee-9fe2-367dda9b3589', 'CDN业务如何规避被恶意盗刷？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"为了避免资源被非法用户盗用，您可以通过设置Refer黑白名单、IP黑白名单以及时间戳防盗链功能来实现对访客身份的识别和过滤，从而限制访问CDN资源的用户，避免产生不必要的流量带宽消耗。同时建议您启用流量管理的相关配置，监控域名流量或带宽的消耗情况并接收告警，及时了解流量消耗的相关信息。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:09', '2024-03-27 20:39:09');
INSERT INTO `system_quiz_questions_v2` VALUES (2100, 'f3d7c83c-ebf3-11ee-9fe2-367dda9b3589', '我有PB级别的数据，想要快速搜索出来，建议我使用什么服务？', '提供可能ES之外的方法更多信息', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"建议您使用金山云Elasticsearch服务（KES），ES为您提供高可用、高并发、低延时的搜索体验，仅需要几毫秒的时间，即可帮您在 PB 级的数据中搜索到匹配信息\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:09', '2024-03-27 20:39:09');
INSERT INTO `system_quiz_questions_v2` VALUES (2101, 'f419b4cc-ebf3-11ee-9fe2-367dda9b3589', 'CDN和KS3如何搭配使用？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"您可以使用KS3存储原始文件，CDN作为加速分发内容。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:10', '2024-03-27 20:39:10');
INSERT INTO `system_quiz_questions_v2` VALUES (2102, 'f44b44ce-ebf3-11ee-9fe2-367dda9b3589', 'CDN内容不合法会怎么处理？', '准确性：是否能有效解决问题，1. 与问题相关 2. 正确的 3. 完整的\n专业性：在解决问题的前提下，回答是否足够 1. 精简，无赘述 2. 用词准确 3. 语言风格类似售后4.逻辑清晰、条理清楚，体现因果关系\n幻觉：回答内容中是否存在部分或大量幻觉（胡说八道）内容', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"CDN会对平台内加速资源随机抽检，人工复核确认资源有异常时会跟您提前确认再封禁。如果被运营商、工信部扫描会直接封禁，然后通知您，让您确认和处理\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:10', '2024-03-27 20:39:10');
INSERT INTO `system_quiz_questions_v2` VALUES (2103, 'f4b14f26-ebf3-11ee-9fe2-367dda9b3589', '介绍下EPC产品', '本题侧重对准确性、正确性、完整性的评估，回答的知识是否正确，是否是金山云的产品，是否包含产品概念、特性等维度的描述', 1, 'qa', 0, 'subjective', '', 1, '{\"reference_answer\": \"金山云EPC（Elastic Physical Compute）是裸金属服务器的简称，该产品提供可按需购买、按量付费的物理服务器租赁和物理服务器托管服务，提供云端专用的高性能、安全隔离的物理服务器集群，通过安全、稳定、便捷的计算服务帮用户快速构建与扩容高性能需求的应用服务。使用EPC，用户只需确定裸金属服务器配置和数量，标准机型的部署、交付时间将被缩短至30分钟，物理机供应、运维工作由金山云完成。\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:11', '2024-03-27 20:39:11');
INSERT INTO `system_quiz_questions_v2` VALUES (2104, 'f4effa64-ebf3-11ee-9fe2-367dda9b3589', '_______是指金山云在同一地域内电力，网络，机房互相独立的物理数据中心。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"可用区（Availability Zone）\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:11', '2024-03-27 20:39:11');
INSERT INTO `system_quiz_questions_v2` VALUES (2105, 'f533156a-ebf3-11ee-9fe2-367dda9b3589', ' _______是一种网络虚似化技术，目的是试图改进大型云计算的部署时的扩展问题，可以说是对vlan的一种扩展。实现方式是：基于 IP 网络且采用“MAC in UDP”封装形式的二层 VPN 技术。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"VXLAN(Virtual Extensible LAN)\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:11', '2024-03-27 20:39:11');
INSERT INTO `system_quiz_questions_v2` VALUES (2106, 'f566f75e-ebf3-11ee-9fe2-367dda9b3589', ' _______由一个或者多个容器组成（例如Docker容器），它们共享容器存储、网络和容器运行配置项，是kubernetes中可以创建的最小部署单元', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"Pod\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:12', '2024-03-27 20:39:12');
INSERT INTO `system_quiz_questions_v2` VALUES (2107, 'f5a14c4c-ebf3-11ee-9fe2-367dda9b3589', '是一个基于RPM的软件包管理器，能够从指定服务器自动下载RPM包并且安装，可以处理软件之间的依赖关系，一次性安装所有依赖的软件包，无需一个个下载安装。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"YUM(Yellowdog Updater Modified)\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:12', '2024-03-27 20:39:12');
INSERT INTO `system_quiz_questions_v2` VALUES (2108, 'f5d5d020-ebf3-11ee-9fe2-367dda9b3589', 'SSH是一种_______协议，用于计算机之间的加密登录。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"网络\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:13', '2024-03-27 20:39:13');
INSERT INTO `system_quiz_questions_v2` VALUES (2109, 'f61123dc-ebf3-11ee-9fe2-367dda9b3589', 'Bucket是存放_______的容器，所有的Object都必须存放在特定的Bucket中。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"Object\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:13', '2024-03-27 20:39:13');
INSERT INTO `system_quiz_questions_v2` VALUES (2110, 'f648fea6-ebf3-11ee-9fe2-367dda9b3589', '_______通常是指在一个物理空间内实现信息的集中处理、存储、传输、交换、管理，而计算机设备、服务器设备、网络设备、存储设备等通常认为是网络核心机房的关键设备。关键设备运行所需要的环境因素，如供电系统、制冷系统、机柜系统、消防系统、监控系统等通常被认为是关键物理基础设施。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"互联网数据中心(InternetDataCenter)\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:13', '2024-03-27 20:39:13');
INSERT INTO `system_quiz_questions_v2` VALUES (2111, 'f67a041a-ebf3-11ee-9fe2-367dda9b3589', '金山云产品_______为您提供Serverless架构下的全托管计算环境。用户无需管理服务器相关的操作与部署，只需编写核心代码并上传，就可为您以弹性、高可用、低成本的方式运行代码。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"云函数（Kingsoft Cloud Function\", \"2\": \" KCF）\"}, \"reference_answer\": [\"1\", \"2\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:14', '2024-03-27 20:39:14');
INSERT INTO `system_quiz_questions_v2` VALUES (2112, 'f6b10a50-ebf3-11ee-9fe2-367dda9b3589', '在CDN中，_______是指用户通过浏览器发送请求时，响应该请求的是源站点的服务器，而不是各节点上的缓存服务器。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"回源\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:14', '2024-03-27 20:39:14');
INSERT INTO `system_quiz_questions_v2` VALUES (2113, 'f703b0de-ebf3-11ee-9fe2-367dda9b3589', 'ECC是一种公钥加密算法，利用______上的有理点构成Abel加法群。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"椭圆曲线\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:15', '2024-03-27 20:39:15');
INSERT INTO `system_quiz_questions_v2` VALUES (2114, 'f740ba38-ebf3-11ee-9fe2-367dda9b3589', 'NAT是一种通过修改IP数据包的____和/或____地址来实现IP地址转换的技术。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"源地址，目的地址\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:15', '2024-03-27 20:39:15');
INSERT INTO `system_quiz_questions_v2` VALUES (2115, 'f78a0fa8-ebf3-11ee-9fe2-367dda9b3589', 'NAT是通过_______，实现虚拟私有网络内的云服务器或物理机访问互联网功能。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"网络地址转换\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:15', '2024-03-27 20:39:15');
INSERT INTO `system_quiz_questions_v2` VALUES (2116, 'f7d57420-ebf3-11ee-9fe2-367dda9b3589', 'NAT支持最大满足________流量。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"15Gbps\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:16', '2024-03-27 20:39:16');
INSERT INTO `system_quiz_questions_v2` VALUES (2117, 'f8283282-ebf3-11ee-9fe2-367dda9b3589', '数据采集组件可以将数据采集到______和______。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"Kafka的Topic，KS3\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:16', '2024-03-27 20:39:16');
INSERT INTO `system_quiz_questions_v2` VALUES (2118, 'f87e6012-ebf3-11ee-9fe2-367dda9b3589', '流式采集任务可以将______、、、、、______中的数据，实时采集至大数据云平台的Kafka中。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"文件，外部Kafka，文件夹，自定义source，oracle数据库，MySQL数据库\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:17', '2024-03-27 20:39:17');
INSERT INTO `system_quiz_questions_v2` VALUES (2119, 'f8ba7584-ebf3-11ee-9fe2-367dda9b3589', '批量采集可以通过______将用户客户端本地的多个文件、或选定文件夹下的所有文件推送至KS3；批量采集还支持将FTP下的文件周期/单次拉取至KS3。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"文件上传工具包\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:17', '2024-03-27 20:39:17');
INSERT INTO `system_quiz_questions_v2` VALUES (2120, 'f8f772cc-ebf3-11ee-9fe2-367dda9b3589', '调度任务在一个自然日内运行的次数称为______。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"调度的批次\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:18', '2024-03-27 20:39:18');
INSERT INTO `system_quiz_questions_v2` VALUES (2121, 'f92be82c-ebf3-11ee-9fe2-367dda9b3589', '仅处于______、______状态的作业流才可以进行暂停翻牌操作。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"运行中，未就绪\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:18', '2024-03-27 20:39:18');
INSERT INTO `system_quiz_questions_v2` VALUES (2122, 'f9870680-ebf3-11ee-9fe2-367dda9b3589', '数据开发过程中提交/发布操作等都是以______的粒度进行操作。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"作业流\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:19', '2024-03-27 20:39:19');
INSERT INTO `system_quiz_questions_v2` VALUES (2123, 'f9be4370-ebf3-11ee-9fe2-367dda9b3589', '目前支持的流计算引擎有______和______。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"Flink，Spark Streaming\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:19', '2024-03-27 20:39:19');
INSERT INTO `system_quiz_questions_v2` VALUES (2124, 'f9fb04ae-ebf3-11ee-9fe2-367dda9b3589', 'Source支持Kafka，Sink支持______、、、、、______。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"Kafka，Mysql，Oracle，MPP，ES，HBase，Redis\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:19', '2024-03-27 20:39:19');
INSERT INTO `system_quiz_questions_v2` VALUES (2125, 'fa2b1022-ebf3-11ee-9fe2-367dda9b3589', '在IDE开发页面进行在线开发，开发完毕后，可点击______快速进行作业测试。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"单元测试\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:20', '2024-03-27 20:39:20');
INSERT INTO `system_quiz_questions_v2` VALUES (2126, 'fa543948-ebf3-11ee-9fe2-367dda9b3589', '插件是用户在数据集成开发界面上可操作的______，一个插件相当于一个作业类型，当用户拖拽一个插件后生成一个具体的作业。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"最小单元\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:20', '2024-03-27 20:39:20');
INSERT INTO `system_quiz_questions_v2` VALUES (2127, 'fa9b5ea4-ebf3-11ee-9fe2-367dda9b3589', '算子是在以拖拽形式开发的插件内部用户可进行操作的______，单个算子无法进行运行，需组合成一个处理逻辑后作为一个作业整体运行。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"最小单元\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:21', '2024-03-27 20:39:21');
INSERT INTO `system_quiz_questions_v2` VALUES (2128, 'fad2d730-ebf3-11ee-9fe2-367dda9b3589', '流计算服务订阅和发布的最小单位是______。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"主题（Topic）\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:21', '2024-03-27 20:39:21');
INSERT INTO `system_quiz_questions_v2` VALUES (2129, 'fb1fdd0a-ebf3-11ee-9fe2-367dda9b3589', 'Topic存储数据的最小单元是______。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"流连接分区（Partion）\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:21', '2024-03-27 20:39:21');
INSERT INTO `system_quiz_questions_v2` VALUES (2130, 'fb56025e-ebf3-11ee-9fe2-367dda9b3589', '______由用户手动触发，可以支持程序升级后，继续从升级前的那个点开始执行计算，保证数据不中断。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"状态保存点（SavePoint）\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:22', '2024-03-27 20:39:22');
INSERT INTO `system_quiz_questions_v2` VALUES (2131, 'fb8c569c-ebf3-11ee-9fe2-367dda9b3589', '集群是一个或多个______的集合，这些节点共同保存整个数据，并在所有节点上提供联合索引和搜索功能。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"节点（服务器）\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:22', '2024-03-27 20:39:22');
INSERT INTO `system_quiz_questions_v2` VALUES (2132, 'fbbc31e6-ebf3-11ee-9fe2-367dda9b3589', '一个节点是集群中的一个______，用来存储数据并参与集群的索引和搜索。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"服务器\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:22', '2024-03-27 20:39:22');
INSERT INTO `system_quiz_questions_v2` VALUES (2133, 'fc07188c-ebf3-11ee-9fe2-367dda9b3589', '索引是有相同特性的______集合，(相当于关系型数据库里的一个数据库)。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"文档\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:23', '2024-03-27 20:39:23');
INSERT INTO `system_quiz_questions_v2` VALUES (2134, 'fc5b59d8-ebf3-11ee-9fe2-367dda9b3589', '一个类型通常是一个索引的一个______，允许在一个索引下存储不同类型的文档（相当于关系型数据库中的一张表）。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"逻辑分类/分区\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:23', '2024-03-27 20:39:23');
INSERT INTO `system_quiz_questions_v2` VALUES (2135, 'fc9539aa-ebf3-11ee-9fe2-367dda9b3589', '文档是Elasticsearch中最小的______。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"基本数据单元\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:24', '2024-03-27 20:39:24');
INSERT INTO `system_quiz_questions_v2` VALUES (2136, 'fce3e050-ebf3-11ee-9fe2-367dda9b3589', '文档是可以被索引的______，类似于关系型数据库中的一行数据，用JSON格式来表示。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"基本信息单元\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:24', '2024-03-27 20:39:24');
INSERT INTO `system_quiz_questions_v2` VALUES (2137, 'fd2ed29a-ebf3-11ee-9fe2-367dda9b3589', '映射是用于定义索引结构的______，类似于关系型数据库中的schema。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"模式映射\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:25', '2024-03-27 20:39:25');
INSERT INTO `system_quiz_questions_v2` VALUES (2138, 'fd5f4c5e-ebf3-11ee-9fe2-367dda9b3589', '字段是ElasticSearch里的______，相当于数据的某一列，类似于JSON里的一个键。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"最小单元\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:25', '2024-03-27 20:39:25');
INSERT INTO `system_quiz_questions_v2` VALUES (2139, 'fd8fae30-ebf3-11ee-9fe2-367dda9b3589', '副本是Elasticsearch可以设置的，具有提高系统的______和提高Elasticsearch的查询效率的作用。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"容错性\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:26', '2024-03-27 20:39:26');
INSERT INTO `system_quiz_questions_v2` VALUES (2140, 'fdd5b88a-ebf3-11ee-9fe2-367dda9b3589', '副本是Elasticsearch可以设置的，具有提高系统的容错性和提高Elasticsearch的______的作用。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"查询效率\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:26', '2024-03-27 20:39:26');
INSERT INTO `system_quiz_questions_v2` VALUES (2141, 'fe115a3e-ebf3-11ee-9fe2-367dda9b3589', '域名是由一串用点分隔的名字组成的Internet上某一台计算机或计算机组的______，用于在数据传输时对计算机的定位标识。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"名称\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:26', '2024-03-27 20:39:26');
INSERT INTO `system_quiz_questions_v2` VALUES (2142, 'fe41778c-ebf3-11ee-9fe2-367dda9b3589', '加速域名是您提供的需要使用KCDN加速服务的______，一个域名的目的是便于记忆和沟通的一组服务器的地址（网站，电子邮件，FTP等）。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"域名\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:27', '2024-03-27 20:39:27');
INSERT INTO `system_quiz_questions_v2` VALUES (2143, 'fe7b1be0-ebf3-11ee-9fe2-367dda9b3589', '边缘节点也称CDN节点、Cache节点等，指距离最终用户接入具有较少的中间环节的______，对最终接入用户有较好的响应能力和连接速度。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"网络节点\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:27', '2024-03-27 20:39:27');
INSERT INTO `system_quiz_questions_v2` VALUES (2144, 'fec5e31e-ebf3-11ee-9fe2-367dda9b3589', '源站就是“______”，上级服务器，也是用户的源站主机地址。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"源头的站点\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:28', '2024-03-27 20:39:28');
INSERT INTO `system_quiz_questions_v2` VALUES (2145, 'ff045d24-ebf3-11ee-9fe2-367dda9b3589', '在CDN中，回源是指用户通过浏览器发送请求时，响应该请求的是______，而不是各节点上的缓存服务器。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"源站点的服务器\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:28', '2024-03-27 20:39:28');
INSERT INTO `system_quiz_questions_v2` VALUES (2146, 'ff5acf4c-ebf3-11ee-9fe2-367dda9b3589', '卡顿率是测试过程中，____的占比。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"卡顿时长/总时长\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:29', '2024-03-27 20:39:29');
INSERT INTO `system_quiz_questions_v2` VALUES (2147, 'ffd67912-ebf3-11ee-9fe2-367dda9b3589', 'NFS（Network File System）是一种使用于____的协议，通过网络让不同的机器、不同的操作系统能够彼此分享数据。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"分散式文件系统\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:29', '2024-03-27 20:39:29');
INSERT INTO `system_quiz_questions_v2` VALUES (2148, '003c10ec-ebf4-11ee-9fe2-367dda9b3589', 'API的生命周期包括____、、、____、____等阶段。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"创建、测试、发布、下线、版本切换\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:30', '2024-03-27 20:39:30');
INSERT INTO `system_quiz_questions_v2` VALUES (2149, '0081f81e-ebf4-11ee-9fe2-367dda9b3589', 'CIFS是一种____，可实现Windows系统主机之间的网络文件共享。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"通用Internet文件系统访问协议\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:30', '2024-03-27 20:39:30');
INSERT INTO `system_quiz_questions_v2` VALUES (2150, '00be2c80-ebf4-11ee-9fe2-367dda9b3589', 'MongoDB是开源的____，支持多种数据类型。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"文档型NoSQL数据库\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:31', '2024-03-27 20:39:31');
INSERT INTO `system_quiz_questions_v2` VALUES (2151, '00f58cb6-ebf4-11ee-9fe2-367dda9b3589', '实例是服务提供的____，每个实例的不同内存容量对应不同的带宽、CPU处理能力。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"基本单元\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:31', '2024-03-27 20:39:31');
INSERT INTO `system_quiz_questions_v2` VALUES (2152, '011f8eda-ebf4-11ee-9fe2-367dda9b3589', '地域指的是用户所购买的 MongoDB 实例的____。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"服务器所处的地理位置\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:31', '2024-03-27 20:39:31');
INSERT INTO `system_quiz_questions_v2` VALUES (2153, '014af994-ebf4-11ee-9fe2-367dda9b3589', 'VPC是____，从访问源上保证数据安全。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"私有网络\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:32', '2024-03-27 20:39:32');
INSERT INTO `system_quiz_questions_v2` VALUES (2154, '01862730-ebf4-11ee-9fe2-367dda9b3589', '终端子网是终端连接，可以在您的 VPC 和其他金山云服务（ RDS，KS3）之间创建____，无需通过Internet、NAT服务进行访问。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"私有链接\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:32', '2024-03-27 20:39:32');
INSERT INTO `system_quiz_questions_v2` VALUES (2155, '01bc40f4-ebf4-11ee-9fe2-367dda9b3589', '内存是云数据库 MongoDB 实例可以使用的____。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"内存上限\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:32', '2024-03-27 20:39:32');
INSERT INTO `system_quiz_questions_v2` VALUES (2156, '01f07284-ebf4-11ee-9fe2-367dda9b3589', '副本集是一个____，数据在这个簇中相互复制，并自动进行故障切换。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"mongod进程实例簇\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:33', '2024-03-27 20:39:33');
INSERT INTO `system_quiz_questions_v2` VALUES (2157, '0225e7f2-ebf4-11ee-9fe2-367dda9b3589', '分片集群是包括____、____、____三个组件的组合。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"mongos、shard、congfigserver\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:33', '2024-03-27 20:39:33');
INSERT INTO `system_quiz_questions_v2` VALUES (2158, '02693566-ebf4-11ee-9fe2-367dda9b3589', '数据管理产品最大的亮点在于提供____。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"统一的元数据管理\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:34', '2024-03-27 20:39:34');
INSERT INTO `system_quiz_questions_v2` VALUES (2159, '0294ad4a-ebf4-11ee-9fe2-367dda9b3589', '在全域元数据的基础上数据管理提供____、____等高级应用，并提供技术、业务多种数据质量检核规则。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"数据地图、数据血缘\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:34', '2024-03-27 20:39:34');
INSERT INTO `system_quiz_questions_v2` VALUES (2160, '02c0745c-ebf4-11ee-9fe2-367dda9b3589', '日志服务（KLog）是针对____的一站式服务，提供从日志采集、存储、加工、检索分析、实时消费、数据投递等多项服务。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"日志类数据\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:34', '2024-03-27 20:39:34');
INSERT INTO `system_quiz_questions_v2` VALUES (2161, '02f29176-ebf4-11ee-9fe2-367dda9b3589', '日志服务可以提供____与订阅功能，以多种方式的日志写入途径，简单易用的控制台配置方式。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"实时日志消费\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:35', '2024-03-27 20:39:35');
INSERT INTO `system_quiz_questions_v2` VALUES (2162, '03246e80-ebf4-11ee-9fe2-367dda9b3589', '日志服务提供日志生命周期管理，在创建日志集时可以指定日志的____，逾期后数据将会被清理且不会再产生存储费用。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"有效保存周期\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:35', '2024-03-27 20:39:35');
INSERT INTO `system_quiz_questions_v2` VALUES (2163, '035b8b72-ebf4-11ee-9fe2-367dda9b3589', '虚拟私有网络是您指定的独立网络空间地址块，通过____，实现对网络的整体划分。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"IP和掩码结合\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:35', '2024-03-27 20:39:35');
INSERT INTO `system_quiz_questions_v2` VALUES (2164, '038ff786-ebf4-11ee-9fe2-367dda9b3589', '子网是对____进行进一步划分的网络块。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"虚拟私有网络\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:36', '2024-03-27 20:39:36');
INSERT INTO `system_quiz_questions_v2` VALUES (2165, '03ce12f0-ebf4-11ee-9fe2-367dda9b3589', '内网IP无法访问互联网，可以用于____之间的通讯。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"VPC内实例\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:36', '2024-03-27 20:39:36');
INSERT INTO `system_quiz_questions_v2` VALUES (2166, '040b98f0-ebf4-11ee-9fe2-367dda9b3589', '弹性IP是指可以动态地将IP地址绑定到____上的IP地址，使得该实例具有固定的公网IP地址。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"云服务器实例\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:36', '2024-03-27 20:39:36');
INSERT INTO `system_quiz_questions_v2` VALUES (2167, '04364488-ebf4-11ee-9fe2-367dda9b3589', 'NAT是____的缩写。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"网络地址转换\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:37', '2024-03-27 20:39:37');
INSERT INTO `system_quiz_questions_v2` VALUES (2168, '0475360c-ebf4-11ee-9fe2-367dda9b3589', '专线是从____接入一条物理专线到金山云机房，实现IDC和虚拟私有网络的互通。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"用户IDC\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:37', '2024-03-27 20:39:37');
INSERT INTO `system_quiz_questions_v2` VALUES (2169, '04b1dc06-ebf4-11ee-9fe2-367dda9b3589', '网络访问控制列表（Access Control List，ACL）是一个____的无状态安全规则。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"子网级别\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:38', '2024-03-27 20:39:38');
INSERT INTO `system_quiz_questions_v2` VALUES (2170, '04f58c8a-ebf4-11ee-9fe2-367dda9b3589', 'ACL用于____，并根据规则来决定是否允许或拒绝特定的数据包通过网络。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"控制网络流量的进出\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:38', '2024-03-27 20:39:38');
INSERT INTO `system_quiz_questions_v2` VALUES (2171, '05310198-ebf4-11ee-9fe2-367dda9b3589', '实例是云资源的最小监控单元，可以包括____、关系型数据库、弹性IP、NAT等。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"云服务器\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:38', '2024-03-27 20:39:38');
INSERT INTO `system_quiz_questions_v2` VALUES (2172, '056a8f4e-ebf4-11ee-9fe2-367dda9b3589', '由一串用点分隔的名字组成的Internet上某一台计算机或计算机组的名称，用于在数据传输时对计算机的____。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"定位标识\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:39', '2024-03-27 20:39:39');
INSERT INTO `system_quiz_questions_v2` VALUES (2173, '05a7424a-ebf4-11ee-9fe2-367dda9b3589', '加速域名是您提供的需要使用KCDN加速服务的____，一个域名的目的是便于记忆和沟通的一组服务器的地址（网站，电子邮件，FTP等）。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"域名\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:39', '2024-03-27 20:39:39');
INSERT INTO `system_quiz_questions_v2` VALUES (2174, '05e0cb46-ebf4-11ee-9fe2-367dda9b3589', 'CNAME 记录是指域名解析中的____（Canonical Name）。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"别名记录\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:40', '2024-03-27 20:39:40');
INSERT INTO `system_quiz_questions_v2` VALUES (2175, '0616d808-ebf4-11ee-9fe2-367dda9b3589', '边缘节点也称____、Cache节点等，指距离最终用户接入具有较少的中间环节的网络节点。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"CDN节点\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:40', '2024-03-27 20:39:40');
INSERT INTO `system_quiz_questions_v2` VALUES (2176, '064fe22e-ebf4-11ee-9fe2-367dda9b3589', '源站就是____，上级服务器，也是用户的源站主机地址。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"源头的站点\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:40', '2024-03-27 20:39:40');
INSERT INTO `system_quiz_questions_v2` VALUES (2177, '06917d10-ebf4-11ee-9fe2-367dda9b3589', '在CDN中，回源是指用户通过浏览器发送请求时，响应该请求的是____，而不是各节点上的缓存服务器。', '', 4, 'fill_in_the_blank', 0, 'objective', '', 1, '{\"options\": {\"1\": \"源站点的服务器\"}, \"reference_answer\": [\"1\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:41', '2024-03-27 20:39:41');
INSERT INTO `system_quiz_questions_v2` VALUES (2178, '06c8d526-ebf4-11ee-9fe2-367dda9b3589', '路由是网络流量所经过的途径规则，每条路由包含哪些参数', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"目标网段：目的网段\", \"B\": \"下一跳类型：虚拟私有网络下一跳类型支持“互联网网关”、“主机路由”、“隧道网关”等\", \"C\": \"主机ID\", \"D\": \"下一跳：指定关联到该路由的流量具体跳转至哪个下一跳。\"}, \"reference_answer\": [\"A\", \"B\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:41', '2024-03-27 20:39:41');
INSERT INTO `system_quiz_questions_v2` VALUES (2179, '0723a456-ebf4-11ee-9fe2-367dda9b3589', '对象存储KS3中Bucket的名称全局唯一且命名规则与DNS命名规则相同，具体为：\n\n', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"仅包含小写英文字母（a-z），数字，中线，即： abcdefghijklmnopqrstuvwxyz0123456789-\", \"B\": \"必须由字母或数字开头\", \"C\": \"长度在3和63个字符之间\", \"D\": \"不能以kss开头\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:42', '2024-03-27 20:39:42');
INSERT INTO `system_quiz_questions_v2` VALUES (2180, '0766d0be-ebf4-11ee-9fe2-367dda9b3589', 'KS3的三种存储类型', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"标准存储类型\", \"B\": \"低频访问存储类型\", \"C\": \"归档存储类型\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:42', '2024-03-27 20:39:42');
INSERT INTO `system_quiz_questions_v2` VALUES (2181, '07b10ad0-ebf4-11ee-9fe2-367dda9b3589', 'KS3支持通过以下方式转换对象（Object）的存储类型：', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"通过生命周期规则自动转换Object的存储类型\", \"B\": \"通过控制台手动转换Object的存储类型\", \"C\": \"通过SDK手动转换Object的存储类型\", \"D\": \"通过API手动转换Object的存储类型\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:43', '2024-03-27 20:39:43');
INSERT INTO `system_quiz_questions_v2` VALUES (2182, '07e956ba-ebf4-11ee-9fe2-367dda9b3589', '金山云主机目前支持哪些操作系统？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"Windows\", \"B\": \"CENTOS\", \"C\": \"UBUNTU\", \"D\": \"DEBIAN\", \"E\": \"FEDORA\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\", \"E\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:43', '2024-03-27 20:39:43');
INSERT INTO `system_quiz_questions_v2` VALUES (2183, '0823bf94-ebf4-11ee-9fe2-367dda9b3589', '云数据库MongoDB服务采用三节点副本集的高可用架构，副本集提供三种角色是', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"Primary节点（支持读写请求）\", \"B\": \"Secondary节点（支持只读请求）\", \"C\": \"Hidden节点（提供备节点的角色，默认不支持访问）\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:43', '2024-03-27 20:39:43');
INSERT INTO `system_quiz_questions_v2` VALUES (2184, '08789dac-ebf4-11ee-9fe2-367dda9b3589', 'ECC与经典的DSA、RSA相比，具有以下哪些优势？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"高安全性\", \"B\": \"快处理速度\", \"C\": \"小存储空间占用\", \"D\": \"低带宽要求\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:44', '2024-03-27 20:39:44');
INSERT INTO `system_quiz_questions_v2` VALUES (2185, '08ab4b44-ebf4-11ee-9fe2-367dda9b3589', 'SM2算法与RSA算法相比，具有以下哪些特点？（可选择多个）', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"密码复杂度高\", \"B\": \"处理速度快\", \"C\": \"机器性能消耗更小\", \"D\": \"安全性强\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:44', '2024-03-27 20:39:44');
INSERT INTO `system_quiz_questions_v2` VALUES (2186, '090d2eea-ebf4-11ee-9fe2-367dda9b3589', '虚拟私有网络与子网的关系有哪些？（可选择多个）', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"子网是一个独立的互联网网络空间\", \"B\": \"子网是从虚拟私有网络中划分的地址空间\", \"C\": \"子网可以关联各种云服务\", \"D\": \"子网分为云服务器子网、终端子网、裸金属服务器子网\"}, \"reference_answer\": [\"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:45', '2024-03-27 20:39:45');
INSERT INTO `system_quiz_questions_v2` VALUES (2187, '094e5f28-ebf4-11ee-9fe2-367dda9b3589', '公网IP地址具有什么特点？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"可以访问互联网\", \"B\": \"可以访问内部网络\", \"C\": \"全球唯一的IP地址\", \"D\": \"仅限于特定地区访问\"}, \"reference_answer\": [\"A\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:45', '2024-03-27 20:39:45');
INSERT INTO `system_quiz_questions_v2` VALUES (2188, '09a41d64-ebf4-11ee-9fe2-367dda9b3589', '弹性IP可以与哪些云资源进行绑定？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"云服务器\", \"B\": \"物理机\", \"C\": \"负载均衡\", \"D\": \"所有云资源\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:46', '2024-03-27 20:39:46');
INSERT INTO `system_quiz_questions_v2` VALUES (2189, '09e5a0cc-ebf4-11ee-9fe2-367dda9b3589', '路由包含以下哪些参数？（可选择多个）', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"目标网段\", \"B\": \"下一跳类型\", \"C\": \"下一跳\", \"D\": \"源地址\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:46', '2024-03-27 20:39:46');
INSERT INTO `system_quiz_questions_v2` VALUES (2190, '0a0e509e-ebf4-11ee-9fe2-367dda9b3589', '弹性IP的特点有哪些？（可选择多个）', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"有带宽的公网IP地址\", \"B\": \"支持绑定和解绑操作\", \"C\": \"只能与云服务器绑定\", \"D\": \"可以与云资源进行绑定\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:47', '2024-03-27 20:39:47');
INSERT INTO `system_quiz_questions_v2` VALUES (2191, '0a446292-ebf4-11ee-9fe2-367dda9b3589', '告警策略支持以下哪些产品？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"云主机\", \"B\": \"NAT\", \"C\": \"关系型数据库RDS\", \"D\": \"云数据库Redis\", \"E\": \"云数据库MongoDB\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\", \"E\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:47', '2024-03-27 20:39:47');
INSERT INTO `system_quiz_questions_v2` VALUES (2192, '0a9624d8-ebf4-11ee-9fe2-367dda9b3589', '怎么知道我是不是被反垃圾邮件组织屏蔽了？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"确认发信域名能够解析，且问题服务器上设置的域名在有效期内并可正常使用\", \"B\": \"在国际反垃圾邮件组织的网站上查询\", \"C\": \"在邮件服务器上完成测试\", \"D\": \"查看自己域名的MX记录\", \"E\": \"使用TELNET命令进行测试\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\", \"E\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:47', '2024-03-27 20:39:47');
INSERT INTO `system_quiz_questions_v2` VALUES (2193, '0adfc7d2-ebf4-11ee-9fe2-367dda9b3589', '大数据云适用于哪些应用场景？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"精准营销\", \"B\": \"智能风控\", \"C\": \"数仓建设\", \"D\": \"日志分析\", \"E\": \"经营分析\"}, \"reference_answer\": [\"C\", \"D\", \"E\", \"A\", \"B\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:48', '2024-03-27 20:39:48');
INSERT INTO `system_quiz_questions_v2` VALUES (2194, '0b182988-ebf4-11ee-9fe2-367dda9b3589', '大数据云具有哪些特点？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"数据安全保障\", \"B\": \"开箱即用\", \"C\": \"平台免运维\", \"D\": \"资源弹性扩缩容\", \"E\": \"无\"}, \"reference_answer\": [\"D\", \"C\", \"B\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:48', '2024-03-27 20:39:48');
INSERT INTO `system_quiz_questions_v2` VALUES (2195, '0b5590b6-ebf4-11ee-9fe2-367dda9b3589', '以下哪些情况下可以尝试使用大数据云？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"数据存储分散，数据质量不高，数据价值变现困难\", \"B\": \"大数据体系复杂，运维管理难，开发效率低，使用门槛高\", \"C\": \"现有大数据系统分散，无法统一管理、统一调度、统一门户\", \"D\": \"IT架构复杂，资源无法弹性伸缩，得不到合理利用\", \"E\": \"数据安全性低，容易被黑客攻击\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:49', '2024-03-27 20:39:49');
INSERT INTO `system_quiz_questions_v2` VALUES (2196, '0b90ca1e-ebf4-11ee-9fe2-367dda9b3589', '文件存储KFS具有哪些特性？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"高容量\", \"B\": \"高性能\", \"C\": \"多共享\", \"D\": \"高稳定\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:49', '2024-03-27 20:39:49');
INSERT INTO `system_quiz_questions_v2` VALUES (2197, '0bc79c2e-ebf4-11ee-9fe2-367dda9b3589', '用户能在目录内做哪些操作？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"创建文件\", \"B\": \"删除文件\", \"C\": \"修改文件\", \"D\": \"查找文件\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:49', '2024-03-27 20:39:49');
INSERT INTO `system_quiz_questions_v2` VALUES (2198, '0c027498-ebf4-11ee-9fe2-367dda9b3589', '数据服务支持查询哪些数据源？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"Oracle\", \"B\": \"Greenplum\", \"C\": \"Hive\", \"D\": \"ES\", \"E\": \"HBase\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\", \"E\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:50', '2024-03-27 20:39:50');
INSERT INTO `system_quiz_questions_v2` VALUES (2199, '0c363ecc-ebf4-11ee-9fe2-367dda9b3589', '数据管理提供哪些服务？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"云端传统关系型数据库的统一管理\", \"B\": \"Nosql数据库的统一管理\", \"C\": \"数据地图\", \"D\": \"数据质量\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:50', '2024-03-27 20:39:50');
INSERT INTO `system_quiz_questions_v2` VALUES (2200, '0c723030-ebf4-11ee-9fe2-367dda9b3589', '日志服务（KLog）主要包含哪些功能？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"日志采集\", \"B\": \"查询与分析\", \"C\": \"数据可视化\", \"D\": \"实时告警\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:51', '2024-03-27 20:39:51');
INSERT INTO `system_quiz_questions_v2` VALUES (2201, '0ca35b10-ebf4-11ee-9fe2-367dda9b3589', '日志服务的基本概念包括哪些？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"工程\", \"B\": \"日志池\", \"C\": \"日志组\", \"D\": \"分区\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:51', '2024-03-27 20:39:51');
INSERT INTO `system_quiz_questions_v2` VALUES (2202, '0cee7a50-ebf4-11ee-9fe2-367dda9b3589', '报表端常见的问题有哪些？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"报表的筛选\", \"B\": \"多关联了筛选器导致数据变少或者变没\", \"C\": \"展示的字段是计算字段，结果逻辑写的有问题\", \"D\": \"计算字段里面用了fixed函数，但报表中有筛选器\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:51', '2024-03-27 20:39:51');
INSERT INTO `system_quiz_questions_v2` VALUES (2203, '0d271d6a-ebf4-11ee-9fe2-367dda9b3589', '迁移策略中的同名文件覆盖规则包括哪些？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"不覆盖\", \"B\": \"全覆盖\", \"C\": \"条件覆盖\", \"D\": \"最近修改时间覆盖\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:52', '2024-03-27 20:39:52');
INSERT INTO `system_quiz_questions_v2` VALUES (2204, '0d78da38-ebf4-11ee-9fe2-367dda9b3589', '挂载点无法mount，可能需要检查哪些问题？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"查看错误消息\", \"B\": \"检查是否安装了nfs-utils、nfs-common、cifs-utils等工具\", \"C\": \"本地挂载目录是否存在\", \"D\": \"挂载点所在虚拟私有网络VPC是否和客户端云服务器所在VPC一致，地域是否相同\", \"E\": \"KFS客户端所在的主机是否有做禁止访问外部端口的安全组策略\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\", \"E\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:52', '2024-03-27 20:39:52');
INSERT INTO `system_quiz_questions_v2` VALUES (2205, '0dad476e-ebf4-11ee-9fe2-367dda9b3589', 'KENC适用于哪些行业和场景？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"VR/AR\", \"B\": \"实时音视频通信\", \"C\": \"在线互动教育\", \"D\": \"云游戏\", \"E\": \"智能上传\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\", \"E\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:53', '2024-03-27 20:39:53');
INSERT INTO `system_quiz_questions_v2` VALUES (2206, '0dd94fa8-ebf4-11ee-9fe2-367dda9b3589', '您可以通过虚拟私有网络自定义哪些功能？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"网络\", \"B\": \"IP地址\", \"C\": \"路由\", \"D\": \"安全策略\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:53', '2024-03-27 20:39:53');
INSERT INTO `system_quiz_questions_v2` VALUES (2207, '0e1826c4-ebf4-11ee-9fe2-367dda9b3589', '在自定义的虚拟网络中可以部署哪些金山云服务？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"云服务器\", \"B\": \"物理机\", \"C\": \"负载均衡\", \"D\": \"云数据库\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:53', '2024-03-27 20:39:53');
INSERT INTO `system_quiz_questions_v2` VALUES (2208, '0e42f2fa-ebf4-11ee-9fe2-367dda9b3589', '网？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"弹性IP\", \"B\": \"NAT\", \"C\": \"VPN\", \"D\": \"代理服务器\"}, \"reference_answer\": [\"A\", \"B\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:54', '2024-03-27 20:39:54');
INSERT INTO `system_quiz_questions_v2` VALUES (2209, '0e7c7c46-ebf4-11ee-9fe2-367dda9b3589', '可以通过什么方式将VPC和已有数据中心进行连接？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"专线\", \"B\": \"VPN\", \"C\": \"Wi-Fi\", \"D\": \"蓝牙\"}, \"reference_answer\": [\"A\", \"B\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:54', '2024-03-27 20:39:54');
INSERT INTO `system_quiz_questions_v2` VALUES (2210, '0eb28822-ebf4-11ee-9fe2-367dda9b3589', '金山云子网分为哪几种？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"云服务器子网\", \"B\": \"终端子网\", \"C\": \"裸金属服务器子网\", \"D\": \"数据库子网\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:54', '2024-03-27 20:39:54');
INSERT INTO `system_quiz_questions_v2` VALUES (2211, '0ee1166a-ebf4-11ee-9fe2-367dda9b3589', '实例作为云资源的最小监控单元，可以包括哪些？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"云服务器\", \"B\": \"关系型数据库\", \"C\": \"弹性IP\", \"D\": \"NAT\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:55', '2024-03-27 20:39:55');
INSERT INTO `system_quiz_questions_v2` VALUES (2212, '0f0d766a-ebf4-11ee-9fe2-367dda9b3589', '可以通过哪些方式访问互联网？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"弹性IP\", \"B\": \"NAT\", \"C\": \"VPN\", \"D\": \"代理服务器\"}, \"reference_answer\": [\"A\", \"B\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:55', '2024-03-27 20:39:55');
INSERT INTO `system_quiz_questions_v2` VALUES (2213, '0f4b2730-ebf4-11ee-9fe2-367dda9b3589', '可以在虚拟私有网络中部署哪些服务？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"云服务器\", \"B\": \"物理机\", \"C\": \"负载均衡\", \"D\": \"云数据库\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:55', '2024-03-27 20:39:55');
INSERT INTO `system_quiz_questions_v2` VALUES (2214, '0f85b83c-ebf4-11ee-9fe2-367dda9b3589', '金山云子网分为哪几种类型？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"云服务器子网\", \"B\": \"终端子网\", \"C\": \"裸金属服务器子网\", \"D\": \"数据库子网\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:56', '2024-03-27 20:39:56');
INSERT INTO `system_quiz_questions_v2` VALUES (2215, '0faf422e-ebf4-11ee-9fe2-367dda9b3589', '腾讯云云函数有哪些使用限制？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"访问权限限制\", \"B\": \"并发限制\", \"C\": \"数据库连接限制\", \"D\": \"网络带宽限制\"}, \"reference_answer\": [\"A\", \"B\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:56', '2024-03-27 20:39:56');
INSERT INTO `system_quiz_questions_v2` VALUES (2216, '0fe48ef2-ebf4-11ee-9fe2-367dda9b3589', 'Kafka的主要特点包括哪些？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"高吞吐量\", \"B\": \"可持久化\", \"C\": \"支持水平扩展\", \"D\": \"容错性强\"}, \"reference_answer\": [\"A\", \"B\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:56', '2024-03-27 20:39:56');
INSERT INTO `system_quiz_questions_v2` VALUES (2217, '101d96d4-ebf4-11ee-9fe2-367dda9b3589', 'Kafka的工作原理包括哪些？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"通过将数据分为一个或多个主题（topics）来进行组织和存储\", \"B\": \"生产者通过将消息发布到一个或多个主题中\", \"C\": \"消费者可以从一个或多个主题订阅消息\", \"D\": \"Kafka通过以分布式和多副本的方式来存储消息\"}, \"reference_answer\": [\"A\", \"B\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:57', '2024-03-27 20:39:57');
INSERT INTO `system_quiz_questions_v2` VALUES (2218, '104bc018-ebf4-11ee-9fe2-367dda9b3589', '使用BOS节点需要做哪些准备工作？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"注册腾讯云账号\", \"B\": \"在控制台创建一个Bucket来存储对象\", \"C\": \"获取API密钥\", \"D\": \"配置网络环境\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:57', '2024-03-27 20:39:57');
INSERT INTO `system_quiz_questions_v2` VALUES (2219, '10887148-ebf4-11ee-9fe2-367dda9b3589', 'BOS节点如何上传和下载文件？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"使用BOS节点提供的API接口\", \"B\": \"使用SDK进行操作\", \"C\": \"通过控制台进行操作\", \"D\": \"通过FTP进行操作\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:57', '2024-03-27 20:39:57');
INSERT INTO `system_quiz_questions_v2` VALUES (2220, '10b63ac4-ebf4-11ee-9fe2-367dda9b3589', 'BOS节点支持哪些安全认证机制？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"使用API密钥进行访问控制\", \"B\": \"使用STS临时密钥进行访问控制\", \"C\": \"使用IAM控制台进行访问控制\", \"D\": \"使用OAuth进行访问控制\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:58', '2024-03-27 20:39:58');
INSERT INTO `system_quiz_questions_v2` VALUES (2221, '10f7d1aa-ebf4-11ee-9fe2-367dda9b3589', '云计算可以分为哪些部署模式？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"公有云\", \"B\": \"私有云\", \"C\": \"混合云\", \"D\": \"社区云\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:58', '2024-03-27 20:39:58');
INSERT INTO `system_quiz_questions_v2` VALUES (2222, '114019e2-ebf4-11ee-9fe2-367dda9b3589', '云服务器具有哪些特点？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"弹性扩展\", \"B\": \"高可用性\", \"C\": \"灵活性\", \"D\": \"高成本\"}, \"reference_answer\": [\"A\", \"B\", \"C\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:59', '2024-03-27 20:39:59');
INSERT INTO `system_quiz_questions_v2` VALUES (2223, '1183a89c-ebf4-11ee-9fe2-367dda9b3589', 'CIFS/SMB需要放开哪些端口？', '', 3, 'multiple_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"TCP: 139\", \"B\": \"TCP: 445\", \"C\": \"UDP: 137\", \"D\": \"UDP: 138\"}, \"reference_answer\": [\"A\", \"B\", \"C\", \"D\"]}', 0, '23533', NULL, 1, '2024-03-27 20:39:59', '2024-03-27 20:39:59');
INSERT INTO `system_quiz_questions_v2` VALUES (2224, '11bb5ae4-ebf4-11ee-9fe2-367dda9b3589', '当给Bucket配置Logging之后，依据什么频度自动把该Bucket的操作日志上传到指定的Bucket。', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"每小时\", \"B\": \"每分钟\", \"C\": \"每天\", \"D\": \"每半小时\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:39:59', '2024-03-27 20:39:59');
INSERT INTO `system_quiz_questions_v2` VALUES (2225, '11e65eba-ebf4-11ee-9fe2-367dda9b3589', ' KS3 对外服务的访问域名为？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"Endpoint\", \"B\": \"Region\", \"C\": \"AK\", \"D\": \"EPC\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:00', '2024-03-27 20:40:00');
INSERT INTO `system_quiz_questions_v2` VALUES (2226, '1212da1c-ebf4-11ee-9fe2-367dda9b3589', 'KS3中单个用户可创建Bucket的个数限制是多少？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": 10, \"B\": 100, \"C\": 1000, \"D\": 500}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:00', '2024-03-27 20:40:00');
INSERT INTO `system_quiz_questions_v2` VALUES (2227, '127ca942-ebf4-11ee-9fe2-367dda9b3589', '一个Bucket最多可以绑定多少个域名？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": 10, \"B\": 100, \"C\": 50, \"D\": 200}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:01', '2024-03-27 20:40:01');
INSERT INTO `system_quiz_questions_v2` VALUES (2228, '12baccc2-ebf4-11ee-9fe2-367dda9b3589', 'SSL协议的目的是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"提供互联网通信的安全性和数据完整性保障\", \"B\": \"加密通信数据\", \"C\": \"身份认证\", \"D\": \"防止窃听和攻击\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:01', '2024-03-27 20:40:01');
INSERT INTO `system_quiz_questions_v2` VALUES (2229, '12f177ea-ebf4-11ee-9fe2-367dda9b3589', 'SSL协议使用哪种认证方式？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"对称加密\", \"B\": \"非对称加密\", \"C\": \"X.509认证\", \"D\": \"会话密钥交换\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:01', '2024-03-27 20:40:01');
INSERT INTO `system_quiz_questions_v2` VALUES (2230, '132916dc-ebf4-11ee-9fe2-367dda9b3589', 'SSL协议中的会话密钥用来做什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"加密通信数据\", \"B\": \"身份认证\", \"C\": \"防止窃听和攻击\", \"D\": \"交换数据完整性保障\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:02', '2024-03-27 20:40:02');
INSERT INTO `system_quiz_questions_v2` VALUES (2231, '136a5886-ebf4-11ee-9fe2-367dda9b3589', 'SSL协议的继任者是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"TLS\", \"B\": \"SSL 2.0\", \"C\": \"SSL 3.0\", \"D\": \"HTTPS\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:02', '2024-03-27 20:40:02');
INSERT INTO `system_quiz_questions_v2` VALUES (2232, '139e0ff0-ebf4-11ee-9fe2-367dda9b3589', 'SSL协议使用的加密方式是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"对称加密\", \"B\": \"非对称加密\", \"C\": \"混合加密\", \"D\": \"无加密\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:03', '2024-03-27 20:40:03');
INSERT INTO `system_quiz_questions_v2` VALUES (2233, '13d8cc3a-ebf4-11ee-9fe2-367dda9b3589', 'CA认证中心承担以下哪项责任？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"发放和管理数字证书\", \"B\": \"承担私钥体系的责任\", \"C\": \"监控电子商务交易的安全性\", \"D\": \"进行公钥体系的安全性验证\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:03', '2024-03-27 20:40:03');
INSERT INTO `system_quiz_questions_v2` VALUES (2234, '14052ed8-ebf4-11ee-9fe2-367dda9b3589', 'CSR是以下哪个英文缩写的缩写？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"Certificate Submission Request\", \"B\": \"Certificate Signing Request\", \"C\": \"Certification Service Requirement\", \"D\": \"Certificate Security Request\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:03', '2024-03-27 20:40:03');
INSERT INTO `system_quiz_questions_v2` VALUES (2235, '145397d0-ebf4-11ee-9fe2-367dda9b3589', '用户申请证书时，谁在生成私钥的同时也生成证书请求文件？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"CA机构\", \"B\": \"CSP\", \"C\": \"用户本身\", \"D\": \"证书颁发机构\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:04', '2024-03-27 20:40:04');
INSERT INTO `system_quiz_questions_v2` VALUES (2236, '149abc50-ebf4-11ee-9fe2-367dda9b3589', '用户提交CSR文件给证书颁发机构后，颁发机构如何生成证书公钥文件？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"使用根证书私钥签名\", \"B\": \"使用用户的私钥签名\", \"C\": \"使用中间证书的私钥签名\", \"D\": \"使用CA机构的私钥签名\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:04', '2024-03-27 20:40:04');
INSERT INTO `system_quiz_questions_v2` VALUES (2237, '14d1f436-ebf4-11ee-9fe2-367dda9b3589', 'RSA算法用于以下哪种加密方式？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"对称加密\", \"B\": \"非对称加密\", \"C\": \"哈希算法\", \"D\": \"数字签名\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:05', '2024-03-27 20:40:05');
INSERT INTO `system_quiz_questions_v2` VALUES (2238, '15130926-ebf4-11ee-9fe2-367dda9b3589', 'RSA算法的安全性基于以下哪个数论问题？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"离散对数问题\", \"B\": \"最大公约数问题\", \"C\": \"大整数分解问题\", \"D\": \"概率论问题\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:05', '2024-03-27 20:40:05');
INSERT INTO `system_quiz_questions_v2` VALUES (2239, '154aaa48-ebf4-11ee-9fe2-367dda9b3589', '证书链的最后一项是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"证书序列\", \"B\": \"根CA证书\", \"C\": \"CA证书\", \"D\": \"主办者证件\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:05', '2024-03-27 20:40:05');
INSERT INTO `system_quiz_questions_v2` VALUES (2240, '15823a76-ebf4-11ee-9fe2-367dda9b3589', '首次备案是指什么情况？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"域名备案信息转入金山云\", \"B\": \"域名与主办者证件号码都没有成功的备案号\", \"C\": \"对备案信息进行修改和变更\", \"D\": \"域名在工信部没有备案\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:06', '2024-03-27 20:40:06');
INSERT INTO `system_quiz_questions_v2` VALUES (2241, '15c31f00-ebf4-11ee-9fe2-367dda9b3589', '接入备案是指什么情况？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"域名备案信息转入金山云\", \"B\": \"域名与主办者证件号码都没有成功的备案号\", \"C\": \"对备案信息进行修改和变更\", \"D\": \"域名在工信部没有备案\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:06', '2024-03-27 20:40:06');
INSERT INTO `system_quiz_questions_v2` VALUES (2242, '15ff14d8-ebf4-11ee-9fe2-367dda9b3589', '变更备案是指对什么进行修改和变更？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"证书链\", \"B\": \"根CA证书\", \"C\": \"备案信息\", \"D\": \"域名\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:07', '2024-03-27 20:40:07');
INSERT INTO `system_quiz_questions_v2` VALUES (2243, '1649d824-ebf4-11ee-9fe2-367dda9b3589', '金山云新增网站是指什么情况？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"域名备案信息转入金山云\", \"B\": \"域名与主办者证件号码都没有成功的备案号\", \"C\": \"对备案信息进行修改和变更\", \"D\": \"主办者证件在金山云存在备案，且本次备案的域名在工信部没有备案\"}, \"reference_answer\": \"D\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:07', '2024-03-27 20:40:07');
INSERT INTO `system_quiz_questions_v2` VALUES (2244, '167ce124-ebf4-11ee-9fe2-367dda9b3589', 'SM2算法是用来替换以下哪个公钥密码算法？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"RSA算法\", \"B\": \"ECC算法\", \"C\": \"DSA算法\", \"D\": \"DES算法\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:07', '2024-03-27 20:40:07');
INSERT INTO `system_quiz_questions_v2` VALUES (2245, '16c954d2-ebf4-11ee-9fe2-367dda9b3589', '可用区（Availability Zone）是指金山云中的什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"互联网网关和私有网络的组合\", \"B\": \"电力和网络独立的物理数据中心\", \"C\": \"路由和子网独立的网络空间\", \"D\": \"虚拟私有网络（Virtual Private Cloud）\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:08', '2024-03-27 20:40:08');
INSERT INTO `system_quiz_questions_v2` VALUES (2246, '1706f486-ebf4-11ee-9fe2-367dda9b3589', '虚拟私有网络（Virtual Private Cloud）可以帮助您在金山云中构建什么样的网络空间？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"易于访问互联网的网络空间\", \"B\": \"公共网络资源池\", \"C\": \"完全逻辑隔离的网络空间\", \"D\": \"物理数据中心\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:08', '2024-03-27 20:40:08');
INSERT INTO `system_quiz_questions_v2` VALUES (2247, '1738f530-ebf4-11ee-9fe2-367dda9b3589', '子网是从虚拟私有网络中划分的一个什么空间？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"互联网网络空间\", \"B\": \"独立网络空间\", \"C\": \"IPv6网络空间\", \"D\": \"公共网络资源池\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:09', '2024-03-27 20:40:09');
INSERT INTO `system_quiz_questions_v2` VALUES (2248, '1779a12a-ebf4-11ee-9fe2-367dda9b3589', '网段是由什么来指定的独立网络空间地址块？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"内网 IP 地址\", \"B\": \"虚拟私有网络的标识符\", \"C\": \"用户指定的 IP 地址和掩码\", \"D\": \"云服务器 IP 地址\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:09', '2024-03-27 20:40:09');
INSERT INTO `system_quiz_questions_v2` VALUES (2249, '17a69ee6-ebf4-11ee-9fe2-367dda9b3589', '路由是网络流量经过的什么规则？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"目标网段规则\", \"B\": \"下一跳规则\", \"C\": \"源地址规则\", \"D\": \"逻辑连接规则\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:09', '2024-03-27 20:40:09');
INSERT INTO `system_quiz_questions_v2` VALUES (2250, '17d2f96e-ebf4-11ee-9fe2-367dda9b3589', 'NAT的作用是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"实现虚拟私有网络内的云服务器或物理机访问互联网功能\", \"B\": \"提供网络安全保护功能\", \"C\": \"实现跨网络的流量转发功能\", \"D\": \"控制网络流量的负载均衡功能\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:10', '2024-03-27 20:40:10');
INSERT INTO `system_quiz_questions_v2` VALUES (2251, '18212cce-ebf4-11ee-9fe2-367dda9b3589', '告警策略是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"一个告警规则的集合\", \"B\": \"一个监控实例的集合\", \"C\": \"一个包含告警规则、告警实例和告警接收人的配置\", \"D\": \"一个指定告警通知方式的配置\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:10', '2024-03-27 20:40:10');
INSERT INTO `system_quiz_questions_v2` VALUES (2252, '184cf8a4-ebf4-11ee-9fe2-367dda9b3589', '默认告警策略能够做什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"自动应用告警规则到新购买的实例\", \"B\": \"自动删除过期的告警规则\", \"C\": \"自动发送告警通知给预设的接收人\", \"D\": \"自动更新告警策略的配置\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:10', '2024-03-27 20:40:10');
INSERT INTO `system_quiz_questions_v2` VALUES (2253, '18791600-ebf4-11ee-9fe2-367dda9b3589', 'Agent启动与关闭的区别是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"启动Agent能够采集详细的监控数据，关闭Agent会导致监控采集数据异常\", \"B\": \"启动Agent会导致监控采集数据异常，关闭Agent能够采集详细的监控数据\", \"C\": \"启动Agent和关闭Agent均不会对监控数据产生影响\", \"D\": \"启动Agent和关闭Agent都会导致监控采集数据异常\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:11', '2024-03-27 20:40:11');
INSERT INTO `system_quiz_questions_v2` VALUES (2254, '18ba7064-ebf4-11ee-9fe2-367dda9b3589', '如果不想接收告警消息，可以采取以下措施：', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"在联系人管理中禁用联系人\", \"B\": \"在告警策略中禁用告警规则\", \"C\": \"在监控系统设置中关闭告警功能\", \"D\": \"在云主机设置中关闭告警通知\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:11', '2024-03-27 20:40:11');
INSERT INTO `system_quiz_questions_v2` VALUES (2255, '18e0711a-ebf4-11ee-9fe2-367dda9b3589', '监控短信和邮件告警是否收费？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"是，每条告警短信和邮件都会收取费用\", \"B\": \"否，所有的监控短信和邮件都是免费的\", \"C\": \"每个月提供一定数量的免费告警短信和邮件\", \"D\": \"收费标准根据使用的监控服务和发送数量来决定\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:11', '2024-03-27 20:40:11');
INSERT INTO `system_quiz_questions_v2` VALUES (2256, '1913fb66-ebf4-11ee-9fe2-367dda9b3589', '如果不想接收告警消息，应该在哪个管理中进行操作？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"监控视图管理\", \"B\": \"告警策略管理\", \"C\": \"监控指标配置\", \"D\": \"联系人管理\"}, \"reference_answer\": \"D\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:12', '2024-03-27 20:40:12');
INSERT INTO `system_quiz_questions_v2` VALUES (2257, '194c30e4-ebf4-11ee-9fe2-367dda9b3589', '监控短信和邮件告警是否收费？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"是，每条消息都需要支付费用\", \"B\": \"是，根据发送的数量计费\", \"C\": \"否，每个月提供免费额度\", \"D\": \"否，完全免费\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:12', '2024-03-27 20:40:12');
INSERT INTO `system_quiz_questions_v2` VALUES (2258, '1975ce68-ebf4-11ee-9fe2-367dda9b3589', '联系人组和联系人、实例组和实例的对应关系是否有限制？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"是，一个联系人只能属于一个联系人组\", \"B\": \"是，一个联系人可以属于多个联系人组\", \"C\": \"是，一个实例只能属于一个实例组\", \"D\": \"否，一个实例可以属于多个实例组\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:12', '2024-03-27 20:40:12');
INSERT INTO `system_quiz_questions_v2` VALUES (2259, '19a19e8a-ebf4-11ee-9fe2-367dda9b3589', '如果无法看到监控数据，应该进行哪些操作？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"检查Agent是否安装并启动\", \"B\": \"重启监控Agent\", \"C\": \"提交工单\", \"D\": \"打开监控视图管理\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:13', '2024-03-27 20:40:13');
INSERT INTO `system_quiz_questions_v2` VALUES (2260, '19e8a8f2-ebf4-11ee-9fe2-367dda9b3589', '大数据云覆盖数据从哪里到哪里的一体化流程？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"数据分析到消费展现\", \"B\": \"源端采集到数据分析\", \"C\": \"源端采集到消费展现\", \"D\": \"数据分析到数据存储\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:13', '2024-03-27 20:40:13');
INSERT INTO `system_quiz_questions_v2` VALUES (2261, '1a1c5cba-ebf4-11ee-9fe2-367dda9b3589', '大数据云能助力企业/政府实现什么转型？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"网络化转型\", \"B\": \"数字化转型\", \"C\": \"信息化转型\", \"D\": \"智能化转型\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:14', '2024-03-27 20:40:14');
INSERT INTO `system_quiz_questions_v2` VALUES (2262, '1a721380-ebf4-11ee-9fe2-367dda9b3589', '目前支持业务检核的数据源不包括哪一项？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"HIVE\", \"B\": \"Oracle\", \"C\": \"MySQL\", \"D\": \"MongoDB\"}, \"reference_answer\": \"D\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:14', '2024-03-27 20:40:14');
INSERT INTO `system_quiz_questions_v2` VALUES (2263, '1aa49670-ebf4-11ee-9fe2-367dda9b3589', '调度运维的优势不包括哪一项？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"可视化的任务编排\", \"B\": \"对不同类型的任务进行统一的调度，监控运维\", \"C\": \"实现一个多任务，高并发，体验好的调度平台\", \"D\": \"无法在数据平台建设中起到基础和保障的作用\"}, \"reference_answer\": \"D\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:14', '2024-03-27 20:40:14');
INSERT INTO `system_quiz_questions_v2` VALUES (2264, '1ae0b470-ebf4-11ee-9fe2-367dda9b3589', '以下哪种插件不支持选择数据源？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"Spark SQL\", \"B\": \"Spark Shell\", \"C\": \"Shell\", \"D\": \"Python2.7\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:15', '2024-03-27 20:40:15');
INSERT INTO `system_quiz_questions_v2` VALUES (2265, '1b1218ee-ebf4-11ee-9fe2-367dda9b3589', '专线的主要作用是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"从用户IDC接入一条物理专线到金山云机房\", \"B\": \"从用户IDC接入一条物理专线到用户自己的机房\", \"C\": \"从金山云机房接入一条物理专线到用户IDC\", \"D\": \"无法实现IDC和虚拟私有网络的互通\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:15', '2024-03-27 20:40:15');
INSERT INTO `system_quiz_questions_v2` VALUES (2266, '1b4a3846-ebf4-11ee-9fe2-367dda9b3589', '对等连接是指什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"两个虚拟私有云之间通过专线连接\", \"B\": \"两个虚拟私有云之间通过互联网连接\", \"C\": \"两个虚拟私有云之间无法直接通信\", \"D\": \"两个虚拟私有云之间通过无线连接\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:15', '2024-03-27 20:40:15');
INSERT INTO `system_quiz_questions_v2` VALUES (2267, '1b91085c-ebf4-11ee-9fe2-367dda9b3589', '网络访问控制列表（ACL）是什么级别的安全规则？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"用户级别\", \"B\": \"系统级别\", \"C\": \"子网级别\", \"D\": \"网络级别\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:16', '2024-03-27 20:40:16');
INSERT INTO `system_quiz_questions_v2` VALUES (2268, '1bd6dfc6-ebf4-11ee-9fe2-367dda9b3589', '实例是云资源的什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"最大监控单元\", \"B\": \"最小监控单元\", \"C\": \"最大存储单元\", \"D\": \"最小存储单元\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:16', '2024-03-27 20:40:16');
INSERT INTO `system_quiz_questions_v2` VALUES (2269, '1c1190d0-ebf4-11ee-9fe2-367dda9b3589', '实例组的主要作用是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"管理单个实例\", \"B\": \"管理多个实例\", \"C\": \"配置告警\", \"D\": \"配置联系人\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:17', '2024-03-27 20:40:17');
INSERT INTO `system_quiz_questions_v2` VALUES (2270, '1c416030-ebf4-11ee-9fe2-367dda9b3589', '联系人需要配置什么信息？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"姓名和地址\", \"B\": \"手机号码和邮箱\", \"C\": \"用户名和密码\", \"D\": \"身份证号和银行卡号\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:17', '2024-03-27 20:40:17');
INSERT INTO `system_quiz_questions_v2` VALUES (2271, '1c7f8f72-ebf4-11ee-9fe2-367dda9b3589', '一个联系人可以属于几个联系组？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"一个\", \"B\": \"两个\", \"C\": \"三个\", \"D\": \"不限\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:17', '2024-03-27 20:40:17');
INSERT INTO `system_quiz_questions_v2` VALUES (2272, '1cb3418c-ebf4-11ee-9fe2-367dda9b3589', '告警策略是什么的集合？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"告警规则、告警对象、告警接收人\", \"B\": \"告警规则、告警对象、告警方式\", \"C\": \"告警规则、告警接收人、告警方式\", \"D\": \"告警对象、告警接收人、告警方式\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:18', '2024-03-27 20:40:18');
INSERT INTO `system_quiz_questions_v2` VALUES (2273, '1ce33f36-ebf4-11ee-9fe2-367dda9b3589', 'Dashboard可以查看多少条监控数据？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"50条\", \"B\": \"100条\", \"C\": \"150条\", \"D\": \"200条\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:18', '2024-03-27 20:40:18');
INSERT INTO `system_quiz_questions_v2` VALUES (2274, '1d1db0bc-ebf4-11ee-9fe2-367dda9b3589', '数据集成包括哪些内容？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"数据同步，数据分析，业务检核\", \"B\": \"数据同步，数据加工，业务检核\", \"C\": \"数据加工，数据分析，业务检核\", \"D\": \"数据同步，数据加工，数据分析\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:19', '2024-03-27 20:40:19');
INSERT INTO `system_quiz_questions_v2` VALUES (2275, '1d5506ca-ebf4-11ee-9fe2-367dda9b3589', '数据同步工具的主要特点是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"在复杂的网络情况下进行同构数据源之间数据高效稳定的同步迁移\", \"B\": \"在简单的网络情况下进行同构数据源之间数据高效稳定的同步迁移\", \"C\": \"在复杂的网络情况下进行异构数据源之间数据高效稳定的同步迁移\", \"D\": \"在简单的网络情况下进行异构数据源之间数据高效稳定的同步迁移\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:19', '2024-03-27 20:40:19');
INSERT INTO `system_quiz_questions_v2` VALUES (2276, '1d82c2ea-ebf4-11ee-9fe2-367dda9b3589', '数据加工工具的主要特点是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"是非可视化拖拽式的工具，只能满足同一数据源在数据加工过程中的整合，转换，聚合等\", \"B\": \"是可视化拖拽式的工具，能够满足不同数据源在数据加工过程中的整合，转换，聚合等\", \"C\": \"是非可视化拖拽式的工具，能够满足不同数据源在数据加工过程中的整合，转换，聚合等\", \"D\": \"是可视化拖拽式的工具，只能满足同一数据源在数据加工过程中的整合，转换，聚合等\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:19', '2024-03-27 20:40:19');
INSERT INTO `system_quiz_questions_v2` VALUES (2277, '1db60e52-ebf4-11ee-9fe2-367dda9b3589', '业务检核的主要作用是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"和数据质量中的业务规则无缝衔接，对数据进行全面的规则检核\", \"B\": \"和数据质量中的业务规则无缝衔接，对数据进行部分的规则检核\", \"C\": \"和数据质量中的业务规则有缝隙衔接，对数据进行全面的规则检核\", \"D\": \"和数据质量中的业务规则有缝隙衔接，对数据进行部分的规则检核\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:19', '2024-03-27 20:40:19');
INSERT INTO `system_quiz_questions_v2` VALUES (2278, '1dde9188-ebf4-11ee-9fe2-367dda9b3589', '数据源是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"数据集成所处理的数据来源，支持多种不同类型的数据来源，且支持不同数据源之间的转换\", \"B\": \"数据集成所处理的数据来源，只支持一种类型的数据来源，且支持不同数据源之间的转换\", \"C\": \"数据集成所处理的数据来源，支持多种不同类型的数据来源，但不支持不同数据源之间的转换\", \"D\": \"数据集成所处理的数据来源，只支持一种类型的数据来源，且不支持不同数据源之间的转换\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:20', '2024-03-27 20:40:20');
INSERT INTO `system_quiz_questions_v2` VALUES (2279, '1e07e880-ebf4-11ee-9fe2-367dda9b3589', '脏数据是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"数据格式本身符合规范，且满足用户定义的格式的数据\", \"B\": \"数据格式本身不符合规范，或者不满足用户定义的格式的数据\", \"C\": \"数据格式本身符合规范，但不满足用户定义的格式的数据\", \"D\": \"数据格式本身不符合规范，但满足用户定义的格式的数据\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:20', '2024-03-27 20:40:20');
INSERT INTO `system_quiz_questions_v2` VALUES (2280, '1e47108c-ebf4-11ee-9fe2-367dda9b3589', '跨域访问是指什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"不同域名之间相互访问\", \"B\": \"同一域名内部的访问\", \"C\": \"不同服务器之间的访问\", \"D\": \"同一服务器内部的访问\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:20', '2024-03-27 20:40:20');
INSERT INTO `system_quiz_questions_v2` VALUES (2281, '1e6f4570-ebf4-11ee-9fe2-367dda9b3589', '忽略参数缓存在CDN配置中的作用是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"降低CDN的命中率\", \"B\": \"提高CDN的命中率\", \"C\": \"降低CDN的访问速度\", \"D\": \"提高CDN的访问速度\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:21', '2024-03-27 20:40:21');
INSERT INTO `system_quiz_questions_v2` VALUES (2282, '1eb41448-ebf4-11ee-9fe2-367dda9b3589', '卡顿率越低，意味着什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"视频质量越差\", \"B\": \"视频质量越好\", \"C\": \"视频播放速度越慢\", \"D\": \"视频播放速度越快\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:21', '2024-03-27 20:40:21');
INSERT INTO `system_quiz_questions_v2` VALUES (2283, '1eff54d0-ebf4-11ee-9fe2-367dda9b3589', '文件存储KFS是什么类型的存储？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"私有云存储\", \"B\": \"公有云存储\", \"C\": \"混合云存储\", \"D\": \"本地存储\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:22', '2024-03-27 20:40:22');
INSERT INTO `system_quiz_questions_v2` VALUES (2284, '1f3718f2-ebf4-11ee-9fe2-367dda9b3589', '文件存储KFS为哪些计算产品提供文件访问接口？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"云服务器、裸金属服务器和容器\", \"B\": \"云服务器和裸金属服务器\", \"C\": \"裸金属服务器和容器\", \"D\": \"云服务器和容器\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:22', '2024-03-27 20:40:22');
INSERT INTO `system_quiz_questions_v2` VALUES (2285, '1f72f7dc-ebf4-11ee-9fe2-367dda9b3589', 'API分组是以什么为单位进行管理的？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"单个API\", \"B\": \"同一种业务API的集合\", \"C\": \"不同业务API的集合\", \"D\": \"所有API\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:22', '2024-03-27 20:40:22');
INSERT INTO `system_quiz_questions_v2` VALUES (2286, '1fab30c0-ebf4-11ee-9fe2-367dda9b3589', 'MongoDB是什么类型的数据库？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"关系型数据库\", \"B\": \"文档型NoSQL数据库\", \"C\": \"图形数据库\", \"D\": \"键值数据库\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:23', '2024-03-27 20:40:23');
INSERT INTO `system_quiz_questions_v2` VALUES (2287, '1fe07e10-ebf4-11ee-9fe2-367dda9b3589', '实例是服务提供的什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"基本单元\", \"B\": \"基本服务\", \"C\": \"基本功能\", \"D\": \"基本配置\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:23', '2024-03-27 20:40:23');
INSERT INTO `system_quiz_questions_v2` VALUES (2288, '20235ef6-ebf4-11ee-9fe2-367dda9b3589', '地域指的是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"用户所购买的 MongoDB 实例的服务器所处的地理位置\", \"B\": \"用户所购买的 MongoDB 实例的服务器所处的网络位置\", \"C\": \"用户所购买的 MongoDB 实例的服务器所处的数据中心\", \"D\": \"用户所购买的 MongoDB 实例的服务器所处的城市\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:24', '2024-03-27 20:40:24');
INSERT INTO `system_quiz_questions_v2` VALUES (2289, '2052b3ea-ebf4-11ee-9fe2-367dda9b3589', '如果您的数据存在多个零散、异构的数据存储系统里，您可能需要什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"数据分析\", \"B\": \"数据管理\", \"C\": \"数据清洗\", \"D\": \"数据挖掘\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:24', '2024-03-27 20:40:24');
INSERT INTO `system_quiz_questions_v2` VALUES (2290, '208d5824-ebf4-11ee-9fe2-367dda9b3589', '报表端常见的问题不包括哪项？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"报表的筛选\", \"B\": \"多关联了筛选器导致数据变少或者变没\", \"C\": \"展示的字段是计算字段，结果逻辑写的有问题\", \"D\": \"数据模型是抽取类型，数据变化只在源头，却没有执行抽取调度\"}, \"reference_answer\": \"D\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:24', '2024-03-27 20:40:24');
INSERT INTO `system_quiz_questions_v2` VALUES (2291, '20c96fd0-ebf4-11ee-9fe2-367dda9b3589', '数据模型常见的问题不包括哪项？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"数据模型是抽取类型，数据变化只在源头，却没有执行抽取调度\", \"B\": \"数据模型上有筛选器\", \"C\": \"数据模型有多表关联，因为连接条件有问题导致数据变多倍，或者数据减少\", \"D\": \"参考数据是生产环境，数据连接连的是测试环境\"}, \"reference_answer\": \"D\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:25', '2024-03-27 20:40:25');
INSERT INTO `system_quiz_questions_v2` VALUES (2292, '20ff1f7c-ebf4-11ee-9fe2-367dda9b3589', '在哪里可以查看迁移任务中迁移了哪些对象？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"迁移任务详情\", \"B\": \"迁移任务设置\", \"C\": \"迁移任务日志\", \"D\": \"迁移任务报告\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:25', '2024-03-27 20:40:25');
INSERT INTO `system_quiz_questions_v2` VALUES (2293, '2141a8e2-ebf4-11ee-9fe2-367dda9b3589', 'Windows SMB挂载失败的主要原因是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"网络连接问题\", \"B\": \"文件系统错误\", \"C\": \"系统因安全策略阻挡了以来宾访问权限\", \"D\": \"系统资源不足\"}, \"reference_answer\": \"C\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:25', '2024-03-27 20:40:25');
INSERT INTO `system_quiz_questions_v2` VALUES (2294, '21761fa0-ebf4-11ee-9fe2-367dda9b3589', '虚拟私有网络是通过什么实现对网络的整体划分的？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"IP和掩码\", \"B\": \"端口和协议\", \"C\": \"带宽和延迟\", \"D\": \"服务器和客户端\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:26', '2024-03-27 20:40:26');
INSERT INTO `system_quiz_questions_v2` VALUES (2295, '21aa3308-ebf4-11ee-9fe2-367dda9b3589', '子网是对什么进行进一步划分的网络块？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"虚拟私有网络\", \"B\": \"物理网络\", \"C\": \"本地网络\", \"D\": \"公共网络\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:26', '2024-03-27 20:40:26');
INSERT INTO `system_quiz_questions_v2` VALUES (2296, '21d721ba-ebf4-11ee-9fe2-367dda9b3589', '内网IP可以用于什么之间的通讯？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"VPC内实例\", \"B\": \"不同VPC的实例\", \"C\": \"云服务器和物理机\", \"D\": \"云服务器和数据库\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:26', '2024-03-27 20:40:26');
INSERT INTO `system_quiz_questions_v2` VALUES (2297, '2201bbd2-ebf4-11ee-9fe2-367dda9b3589', '弹性IP可以与哪些云资源进行绑定？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"云服务器\", \"B\": \"物理机\", \"C\": \"负载均衡\", \"D\": \"所有以上\"}, \"reference_answer\": \"D\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:27', '2024-03-27 20:40:27');
INSERT INTO `system_quiz_questions_v2` VALUES (2298, '223a4bb4-ebf4-11ee-9fe2-367dda9b3589', '弹性IP的主要作用是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"实现云资源访问互联网\", \"B\": \"实现云资源之间的通讯\", \"C\": \"提高云资源的安全性\", \"D\": \"提高云资源的访问速度\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:27', '2024-03-27 20:40:27');
INSERT INTO `system_quiz_questions_v2` VALUES (2299, '22703f30-ebf4-11ee-9fe2-367dda9b3589', '专线是从哪里接入一条物理专线到金山云机房？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"用户IDC\", \"B\": \"用户办公室\", \"C\": \"用户家庭网络\", \"D\": \"用户移动设备\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:27', '2024-03-27 20:40:27');
INSERT INTO `system_quiz_questions_v2` VALUES (2300, '22b17108-ebf4-11ee-9fe2-367dda9b3589', '网络访问控制列表（ACL）是什么级别的安全规则？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"网络级别\", \"B\": \"子网级别\", \"C\": \"实例级别\", \"D\": \"用户级别\"}, \"reference_answer\": \"B\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:28', '2024-03-27 20:40:28');
INSERT INTO `system_quiz_questions_v2` VALUES (2301, '22ec7b7c-ebf4-11ee-9fe2-367dda9b3589', 'ACL的主要作用是什么？', '', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"控制网络流量的进出\", \"B\": \"加密网络数据\", \"C\": \"优化网络性能\", \"D\": \"管理网络设备\"}, \"reference_answer\": \"A\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:28', '2024-03-27 20:40:28');
INSERT INTO `system_quiz_questions_v2` VALUES (2302, '2320a460-ebf4-11ee-9fe2-367dda9b3589', '通过内网和外网访问同一个 Region 所需要的 Endpoint 是相同的。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:29', '2024-03-27 20:40:29');
INSERT INTO `system_quiz_questions_v2` VALUES (2303, '2356edea-ebf4-11ee-9fe2-367dda9b3589', 'Region 是在创建 Bucket 的时候指定的，一旦指定之后就不允许更改。该 Bucket 下所有的 Object 都存储在对应的数据中心，目前不支持 Object 级别的 Region 设置。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:29', '2024-03-27 20:40:29');
INSERT INTO `system_quiz_questions_v2` VALUES (2304, '238d5e84-ebf4-11ee-9fe2-367dda9b3589', 'KS3中的Bucket无个数限制。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:29', '2024-03-27 20:40:29');
INSERT INTO `system_quiz_questions_v2` VALUES (2305, '23cc16c4-ebf4-11ee-9fe2-367dda9b3589', 'KS3不支持防盗链。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:30', '2024-03-27 20:40:30');
INSERT INTO `system_quiz_questions_v2` VALUES (2306, '23f9fe18-ebf4-11ee-9fe2-367dda9b3589', '云主机的竞价型实例为实时结算，不支持转为预付费（包年包月）。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:30', '2024-03-27 20:40:30');
INSERT INTO `system_quiz_questions_v2` VALUES (2307, '243476d8-ebf4-11ee-9fe2-367dda9b3589', '金山云的CDN产品目前不提供带宽报警服务。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:30', '2024-03-27 20:40:30');
INSERT INTO `system_quiz_questions_v2` VALUES (2308, '2468e27e-ebf4-11ee-9fe2-367dda9b3589', '用户在金山云创建云数据库实例后，系统会自动生成资源ID，资源ID全局唯一', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:31', '2024-03-27 20:40:31');
INSERT INTO `system_quiz_questions_v2` VALUES (2309, '2497035c-ebf4-11ee-9fe2-367dda9b3589', 'CA认证中心是电子商务交易中的公信第三方。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:31', '2024-03-27 20:40:31');
INSERT INTO `system_quiz_questions_v2` VALUES (2310, '24cdaf60-ebf4-11ee-9fe2-367dda9b3589', '监控图表没有数据很有可能是因为未安装监控Agent。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:31', '2024-03-27 20:40:31');
INSERT INTO `system_quiz_questions_v2` VALUES (2311, '24f7c55c-ebf4-11ee-9fe2-367dda9b3589', '安装监控Agent后监控视图会提示异常，需要检查Agent是否是正常启动状态。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:32', '2024-03-27 20:40:32');
INSERT INTO `system_quiz_questions_v2` VALUES (2312, '25270632-ebf4-11ee-9fe2-367dda9b3589', '安装监控Agent后磁盘性能数据不显示可能是因为相关功能未开启，执行diskperf -y命令并重试。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:32', '2024-03-27 20:40:32');
INSERT INTO `system_quiz_questions_v2` VALUES (2313, '2562ee7c-ebf4-11ee-9fe2-367dda9b3589', '已有的阈值规则已经自动迁移到告警策略。相同阈值规则、实例放置在同一个告警策略中。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:32', '2024-03-27 20:40:32');
INSERT INTO `system_quiz_questions_v2` VALUES (2314, '2599e148-ebf4-11ee-9fe2-367dda9b3589', '一个实例只能绑定一个告警策略。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:33', '2024-03-27 20:40:33');
INSERT INTO `system_quiz_questions_v2` VALUES (2315, '25cc2f68-ebf4-11ee-9fe2-367dda9b3589', '一些第三方的反垃圾邮件组织屏蔽了用户的IP。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:33', '2024-03-27 20:40:33');
INSERT INTO `system_quiz_questions_v2` VALUES (2316, '261315fe-ebf4-11ee-9fe2-367dda9b3589', '第三方的反垃圾邮件组织屏蔽用户的IP是因为用户的IP对外发送大量垃圾邮件。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:34', '2024-03-27 20:40:34');
INSERT INTO `system_quiz_questions_v2` VALUES (2317, '2658784c-ebf4-11ee-9fe2-367dda9b3589', '垃圾邮件是人们意愿去接收到的电子邮件，例如商业广告、政治言论、蠕虫病毒邮件、恐吓、欺骗性的恶意邮件。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:34', '2024-03-27 20:40:34');
INSERT INTO `system_quiz_questions_v2` VALUES (2318, '26a3fb00-ebf4-11ee-9fe2-367dda9b3589', '可以通过页面直接将文件上传至KS3中。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:35', '2024-03-27 20:40:35');
INSERT INTO `system_quiz_questions_v2` VALUES (2319, '26e7900e-ebf4-11ee-9fe2-367dda9b3589', '流式采集Agent只能部署在一个客户端上。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:35', '2024-03-27 20:40:35');
INSERT INTO `system_quiz_questions_v2` VALUES (2320, '27171086-ebf4-11ee-9fe2-367dda9b3589', '启动流式采集任务后，无法查看哪些客户端正在进行采集任务。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:35', '2024-03-27 20:40:35');
INSERT INTO `system_quiz_questions_v2` VALUES (2321, '27636364-ebf4-11ee-9fe2-367dda9b3589', '修改流式Agent采集的Agent信息后，不需要进行任何操作就可以立即生效。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:36', '2024-03-27 20:40:36');
INSERT INTO `system_quiz_questions_v2` VALUES (2322, '27a425fc-ebf4-11ee-9fe2-367dda9b3589', '将Agent停止后，可以在平台页面上重启采集动作。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:36', '2024-03-27 20:40:36');
INSERT INTO `system_quiz_questions_v2` VALUES (2323, '27fdf3ac-ebf4-11ee-9fe2-367dda9b3589', '批量采集任务无法查看采集历史。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:37', '2024-03-27 20:40:37');
INSERT INTO `system_quiz_questions_v2` VALUES (2324, '283b0bc0-ebf4-11ee-9fe2-367dda9b3589', 'Flink流计算引擎只支持上传jar包的方式进行流计算作业的开发。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:37', '2024-03-27 20:40:37');
INSERT INTO `system_quiz_questions_v2` VALUES (2325, '288b87e4-ebf4-11ee-9fe2-367dda9b3589', '已配置的阈值规则无法自动迁移到告警策略。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:38', '2024-03-27 20:40:38');
INSERT INTO `system_quiz_questions_v2` VALUES (2326, '28c98968-ebf4-11ee-9fe2-367dda9b3589', '实时流数据是不持续生成的数据。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:38', '2024-03-27 20:40:38');
INSERT INTO `system_quiz_questions_v2` VALUES (2327, '290007ae-ebf4-11ee-9fe2-367dda9b3589', '在大数据云服务中，一个流计算服务的数据源对应多个topic。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:39', '2024-03-27 20:40:39');
INSERT INTO `system_quiz_questions_v2` VALUES (2328, '294882b8-ebf4-11ee-9fe2-367dda9b3589', '在Elastic 7.x 及以后版本中Type概念被删除。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:39', '2024-03-27 20:40:39');
INSERT INTO `system_quiz_questions_v2` VALUES (2329, '2979215c-ebf4-11ee-9fe2-367dda9b3589', '在一个索引中，可以存储任意多的文档，但文档不必被索引。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:39', '2024-03-27 20:40:39');
INSERT INTO `system_quiz_questions_v2` VALUES (2330, '29b2bbce-ebf4-11ee-9fe2-367dda9b3589', '二级域名是创建分组时，系统给分组绑定的域名，用于 API 调用。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:40', '2024-03-27 20:40:40');
INSERT INTO `system_quiz_questions_v2` VALUES (2331, '29ef04e4-ebf4-11ee-9fe2-367dda9b3589', '签名密钥由一对Key和Secret组成。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:40', '2024-03-27 20:40:40');
INSERT INTO `system_quiz_questions_v2` VALUES (2332, '2a2064e4-ebf4-11ee-9fe2-367dda9b3589', '签名密钥用于前端服务验证API网关的身份，保障前端服务的安全。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:40', '2024-03-27 20:40:40');
INSERT INTO `system_quiz_questions_v2` VALUES (2333, '2a762bfe-ebf4-11ee-9fe2-367dda9b3589', '常量参数是后端服务需要始终接收的变化参数，需要用户传入。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:41', '2024-03-27 20:40:41');
INSERT INTO `system_quiz_questions_v2` VALUES (2334, '2aa92b44-ebf4-11ee-9fe2-367dda9b3589', '可以设置网关向您后端抛请求时，附带一些系统参数，如CaClientIp，即请求IP等。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:41', '2024-03-27 20:40:41');
INSERT INTO `system_quiz_questions_v2` VALUES (2335, '2b07cfe6-ebf4-11ee-9fe2-367dda9b3589', '用户需要创建APP作为调用API时的身份。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:42', '2024-03-27 20:40:42');
INSERT INTO `system_quiz_questions_v2` VALUES (2336, '2b3da0d0-ebf4-11ee-9fe2-367dda9b3589', '每个APP都有一对密钥对，用于解密计算并放入请求中作为签名信息。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:42', '2024-03-27 20:40:42');
INSERT INTO `system_quiz_questions_v2` VALUES (2337, '2b6d6e8c-ebf4-11ee-9fe2-367dda9b3589', 'VPC是私有网络，从访问源上保证数据安全。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:43', '2024-03-27 20:40:43');
INSERT INTO `system_quiz_questions_v2` VALUES (2338, '2ba8f510-ebf4-11ee-9fe2-367dda9b3589', '终端子网是终端连接，可以在您的 VPC 和其他金山云服务（ RDS，KS3）之间创建私有链接，无需通过Internet、NAT服务进行访问。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:43', '2024-03-27 20:40:43');
INSERT INTO `system_quiz_questions_v2` VALUES (2339, '2bdf5baa-ebf4-11ee-9fe2-367dda9b3589', '内存是云数据库 MongoDB 实例可以使用的内存上限。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:43', '2024-03-27 20:40:43');
INSERT INTO `system_quiz_questions_v2` VALUES (2340, '2c1098be-ebf4-11ee-9fe2-367dda9b3589', '磁盘容量是云数据库 MongoDB 实例可以使用的存储空间上限。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:44', '2024-03-27 20:40:44');
INSERT INTO `system_quiz_questions_v2` VALUES (2341, '2c62992a-ebf4-11ee-9fe2-367dda9b3589', '副本集是一个mongod进程实例簇，数据在这个簇中相互复制，并自动进行故障切换。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:44', '2024-03-27 20:40:44');
INSERT INTO `system_quiz_questions_v2` VALUES (2342, '2c9716a0-ebf4-11ee-9fe2-367dda9b3589', '分片集群是包括mongos、shard、congfigserver三个组件的组合。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:44', '2024-03-27 20:40:44');
INSERT INTO `system_quiz_questions_v2` VALUES (2343, '2cc367b4-ebf4-11ee-9fe2-367dda9b3589', '流计算支持用户自定义扩展插件。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:45', '2024-03-27 20:40:45');
INSERT INTO `system_quiz_questions_v2` VALUES (2344, '2d00274e-ebf4-11ee-9fe2-367dda9b3589', 'Flink Sql不支持TopN、Emit等语法，可采用ETL实现。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:45', '2024-03-27 20:40:45');
INSERT INTO `system_quiz_questions_v2` VALUES (2345, '2d2ffe10-ebf4-11ee-9fe2-367dda9b3589', '数据服务产品服务于各大应用系统，为开发者提供数据快速查询分析的能力和快速将数据表变成API服务的能力。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:46', '2024-03-27 20:40:46');
INSERT INTO `system_quiz_questions_v2` VALUES (2346, '2d7bdb28-ebf4-11ee-9fe2-367dda9b3589', 'API支持多版本发布，可将不同版本的API发布至API网关，供各应用调用。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:46', '2024-03-27 20:40:46');
INSERT INTO `system_quiz_questions_v2` VALUES (2347, '2dad0324-ebf4-11ee-9fe2-367dda9b3589', '生产环境支持灰度发布。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:46', '2024-03-27 20:40:46');
INSERT INTO `system_quiz_questions_v2` VALUES (2348, '2dec3b52-ebf4-11ee-9fe2-367dda9b3589', '文档是可以被索引的基本信息单元，使用XML格式表示。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:47', '2024-03-27 20:40:47');
INSERT INTO `system_quiz_questions_v2` VALUES (2349, '2e2bf9cc-ebf4-11ee-9fe2-367dda9b3589', '在一个索引中，可以存储任意多的文档，且文档必须被索引。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:47', '2024-03-27 20:40:47');
INSERT INTO `system_quiz_questions_v2` VALUES (2350, '2e587d8a-ebf4-11ee-9fe2-367dda9b3589', '映射是用于定义索引结构的模式映射，存储有关字段的信息。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:48', '2024-03-27 20:40:48');
INSERT INTO `system_quiz_questions_v2` VALUES (2351, '2e9ff0b6-ebf4-11ee-9fe2-367dda9b3589', '字段是ElasticSearch里的最小单元，相当于数据的某一行。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:48', '2024-03-27 20:40:48');
INSERT INTO `system_quiz_questions_v2` VALUES (2352, '2ee7d96c-ebf4-11ee-9fe2-367dda9b3589', '分片是将一个完整的索引分成多个子索引分布到不同的节点上，构成分布式搜索。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:48', '2024-03-27 20:40:48');
INSERT INTO `system_quiz_questions_v2` VALUES (2353, '2f201aac-ebf4-11ee-9fe2-367dda9b3589', '副本是对索引分片的备份，具有容错性和提高查询效率的作用。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:49', '2024-03-27 20:40:49');
INSERT INTO `system_quiz_questions_v2` VALUES (2354, '2f5e888c-ebf4-11ee-9fe2-367dda9b3589', '镜像回源主要用于数据无缝迁移到OSS的场景。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:49', '2024-03-27 20:40:49');
INSERT INTO `system_quiz_questions_v2` VALUES (2355, '2fa22574-ebf4-11ee-9fe2-367dda9b3589', '在迁移过程中使用镜像回源规则可以获取未迁移至OSS的部分数据，保证服务的正常运行。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:50', '2024-03-27 20:40:50');
INSERT INTO `system_quiz_questions_v2` VALUES (2356, '2ffdbf9c-ebf4-11ee-9fe2-367dda9b3589', '挂载点是VPC内终端子网分配出来的一个访问目标地址（即MAC地址）。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:50', '2024-03-27 20:40:50');
INSERT INTO `system_quiz_questions_v2` VALUES (2357, '30361248-ebf4-11ee-9fe2-367dda9b3589', '在操作系统内部可以通过指定挂载点的MAC地址挂载该文件系统到本地。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:51', '2024-03-27 20:40:51');
INSERT INTO `system_quiz_questions_v2` VALUES (2358, '306d58f2-ebf4-11ee-9fe2-367dda9b3589', '文件系统挂载到计算节点后是操作系统内的一个文件。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:51', '2024-03-27 20:40:51');
INSERT INTO `system_quiz_questions_v2` VALUES (2359, '30a6c0ba-ebf4-11ee-9fe2-367dda9b3589', '用户可以在目录内创建文件进行增删改查读写。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:51', '2024-03-27 20:40:51');
INSERT INTO `system_quiz_questions_v2` VALUES (2360, '30ed95c6-ebf4-11ee-9fe2-367dda9b3589', 'CIFS是通用Internet文件系统，是一种网络文件系统访问协议。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:52', '2024-03-27 20:40:52');
INSERT INTO `system_quiz_questions_v2` VALUES (2361, '3132ea0e-ebf4-11ee-9fe2-367dda9b3589', '通过CIFS协议，可以实现Linux系统主机之间的网络文件共享。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:52', '2024-03-27 20:40:52');
INSERT INTO `system_quiz_questions_v2` VALUES (2362, '316c1b26-ebf4-11ee-9fe2-367dda9b3589', 'Windows客户端建议使用CIFS协议。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:53', '2024-03-27 20:40:53');
INSERT INTO `system_quiz_questions_v2` VALUES (2363, '31968190-ebf4-11ee-9fe2-367dda9b3589', 'API分组是同一种业务API的集合，API开发者以API分组为单位，管理分组内的所有API。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:53', '2024-03-27 20:40:53');
INSERT INTO `system_quiz_questions_v2` VALUES (2364, '31e2797e-ebf4-11ee-9fe2-367dda9b3589', '签名密钥由一对用户名和密码组成。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:53', '2024-06-13 23:41:13');
INSERT INTO `system_quiz_questions_v2` VALUES (2365, '320d9e4c-ebf4-11ee-9fe2-367dda9b3589', '签名密钥用于后端服务验证API网关的身份。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:54', '2024-03-27 20:40:54');
INSERT INTO `system_quiz_questions_v2` VALUES (2366, '32379ec2-ebf4-11ee-9fe2-367dda9b3589', '签名密钥会被用于网关到服务方后端的请求。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:54', '2024-03-27 20:40:54');
INSERT INTO `system_quiz_questions_v2` VALUES (2367, '3263362c-ebf4-11ee-9fe2-367dda9b3589', '签名密钥的作用是保障后端服务的安全。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:54', '2024-03-27 20:40:54');
INSERT INTO `system_quiz_questions_v2` VALUES (2368, '329393bc-ebf4-11ee-9fe2-367dda9b3589', '常量参数指的是需要用户传入的，但是后端服务需要始终接收的参数。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:55', '2024-03-27 20:40:55');
INSERT INTO `system_quiz_questions_v2` VALUES (2369, '32bb8674-ebf4-11ee-9fe2-367dda9b3589', '可以设置网关向您后端抛请求时，附带一些系统参数，如CaClientIp，即请求IP等。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:55', '2024-03-27 20:40:55');
INSERT INTO `system_quiz_questions_v2` VALUES (2370, '32e29390-ebf4-11ee-9fe2-367dda9b3589', '私有主机指用户自己的私有云、服务器都可以叫私有主机。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:55', '2024-03-27 20:40:55');
INSERT INTO `system_quiz_questions_v2` VALUES (2371, '33347912-ebf4-11ee-9fe2-367dda9b3589', '消息的创建者，负责创建和推送数据到消息服务器。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 3, '2024-03-27 20:40:56', '2024-09-20 16:52:44');
INSERT INTO `system_quiz_questions_v2` VALUES (2372, '33604646-ebf4-11ee-9fe2-367dda9b3589', '消息的接收方，用于处理数据和确认消息。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 3, '2024-03-27 20:40:56', '2024-09-20 16:52:36');
INSERT INTO `system_quiz_questions_v2` VALUES (2373, '3390f228-ebf4-11ee-9fe2-367dda9b3589', 'Secret是一种用于保存敏感信息（如密码、OAuth令牌和SSH密钥）的键值对。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:56', '2024-03-27 20:40:56');
INSERT INTO `system_quiz_questions_v2` VALUES (2374, '33d459c8-ebf4-11ee-9fe2-367dda9b3589', 'Volume是Kubernetes集群中的存储卷，用于在Pod中共享存储。每个Pod中声明的存储卷由Pod中的单个容器共享。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 1, '2024-03-27 20:40:57', '2024-06-14 00:21:01');
INSERT INTO `system_quiz_questions_v2` VALUES (2376, '3434cb8c-ebf4-11ee-9fe2-367dda9b3589', 'PVC是用户对存储的请求，类似于Pod对节点资源的耗用。PVC可以请求任意的大小和访问模式。', '', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"False\"}', 0, '23533', NULL, 3, '2024-03-27 20:40:57', '2024-09-20 16:52:52');
INSERT INTO `system_quiz_questions_v2` VALUES (2378, 'd586eb81-725e-11ef-98fc-e40d36854c87', '问答题1', '问答题1', 1, 'qa', 0, 'subjective', '', 2, '{\"reference_answer\": \"问答题1\"}', 0, '43171', '43171', 3, '2024-09-14 14:01:50', '2024-09-14 14:23:00');
INSERT INTO `system_quiz_questions_v2` VALUES (2381, 'd5948d90-7732-11ef-9781-e40d36854c87', 'danxuan', '单选desc', 2, 'single_select', 0, 'objective', '', 1, '{\"options\": {\"A\": \"op1\", \"B\": \"op2\"}, \"reference_answer\": []}', 0, '24665', NULL, 1, '2024-09-20 17:29:28', '2024-09-20 17:29:28');
INSERT INTO `system_quiz_questions_v2` VALUES (2384, 'f3e4dee5-7732-11ef-b593-e40d36854c87', 'pand', 'went', 5, 'true_or_false', 0, 'objective', '', 1, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '24665', NULL, 1, '2024-09-20 17:30:19', '2024-09-20 17:30:19');
INSERT INTO `system_quiz_questions_v2` VALUES (2386, '34059420-ebf4-11ee-9fe2-367dda9b3589', 'PV是集群中的存储资源，可以由管理员事先供应，或者使用存', 'desc', 5, 'true_or_false', 0, 'objective', '', 3, '{\"options\": {\"True\": \"正确\", \"False\": \"错误\"}, \"reference_answer\": \"True\"}', 0, '23533', '24665', 1, '2024-03-27 20:40:57', '2024-09-20 17:57:05');
INSERT INTO `system_quiz_questions_v2` VALUES (2387, 'fd62c6cb-7732-11ef-980e-e40d36854c87', 'q1', 'duolun', 6, 'multi_round_qa', 0, 'subjective', '', 2, '{\"qa_round\": [{\"question\": \"q1\", \"reference_answer\": \"a1\"}, {\"question\": \"q2\", \"reference_answer\": \"a2\"}]}', 0, '24665', '24665', 1, '2024-09-20 17:30:35', '2024-09-20 18:01:49');
INSERT INTO `system_quiz_questions_v2` VALUES (2389, 'e66003fa-7732-11ef-a2a8-e40d36854c87', 'tiankongddd', 'tiankongdes', 4, 'fill_in_the_blank', 0, 'objective', '', 3, '{\"options\": {\"A\": \"red\"}, \"reference_answer\": [\"A\"]}', 0, '24665', '24665', 1, '2024-09-20 17:29:56', '2024-09-20 18:02:27');
INSERT INTO `system_quiz_questions_v2` VALUES (2391, 'dfaacb62-7732-11ef-8861-e40d36854c87', 'duoxuan', 'desduoxuan', 3, 'multiple_select', 0, 'objective', '', 3, '{\"options\": {\"A\": \"o1\", \"B\": \"o2\", \"C\": \"o3\"}, \"reference_answer\": [\"A\", \"B\"]}', 0, '24665', '24665', 1, '2024-09-20 17:29:45', '2024-09-20 18:02:48');
INSERT INTO `system_quiz_questions_v2` VALUES (2392, 'b190ca35-7732-11ef-9fc2-e40d36854c87', 'wenda', 'wedesc', 1, 'qa', 0, 'subjective', '', 2, '{\"reference_answer\": \"1111\"}', 0, '24665', '43171', 1, '2024-09-20 17:28:28', '2024-09-24 17:55:41');

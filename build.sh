#!/bin/bash

# 检查是否传递了版本参数
if [ -z "$1" ]; then
  echo "错误：未提供版本参数。"
  echo "用法: $0 <version>"
  exit 1
fi

# 保存版本参数
# v1.4.0-dev
version=$1
# 保持与hanhai平台版本一致
#version=v1.3.0

# 如果 version 包含 "arm"，则使用 Dockerfile-arm64
DOCKERFILE_PATH="./docker/Dockerfile"
if [[ $version == *"-arm"* ]]; then
    DOCKERFILE_PATH="./docker/Dockerfile-arm64"
    echo "检测到 ARM 版本，使用 ./evaluation/scheduler/Dockerfile-arm64  ./docker/Dockerfile.evaluation-arm64 构建"
fi


# 构建并标记 Docker 镜像 （使用ezone镜像仓库）
docker build -t ezone.ksyun.com/ezone/ai-hanhai/snapshot/hanhai-human-eval/human-eval:${version} -f ${DOCKERFILE_PATH} .

# 推送 Docker 镜像到仓库, 仓库地址默认：ezone.ksyun.com/ezone/ai-hanhai/snapshot
if [ $? == 0 ];then
  echo "镜像build成功"
  docker push ezone.ksyun.com/ezone/ai-hanhai/snapshot/hanhai-human-eval/human-eval:${version}
else
  echo "镜像build失败！"
  exit 1
fi

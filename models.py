# coding: utf-8
from sqlalchemy import Column, DateTime, Index, JSON, String, TIMESTAMP, Text, text
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, TINYINT, TINYTEXT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class InferenceService(Base):
    __tablename__ = 'inference_service'

    id = Column(INTEGER(11), primary_key=True)
    plan_id = Column(String(500), nullable=False, server_default=text("'0'"), comment='计划id')
    plan_name = Column(Text, comment='计划名称')
    model_id = Column(INTEGER(11), nullable=False, comment='模型ID')
    model_name = Column(Text, comment='模型名称')
    inference_service_id = Column(INTEGER(11), comment='推理服务ID')
    inference_state = Column(INTEGER(11), comment='推理服务状态1-更新中2-运行中3-下线中4-已下线5-异常')
    internal_host = Column(Text, comment='内部服务Host')
    external_path = Column(Text, comment='外部服务路径')
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    model_type = Column(INTEGER(11), comment='模型类型')


class Menu(Base):
    __tablename__ = 'menu'

    id = Column(INTEGER(11), primary_key=True)
    name = Column(TINYTEXT, nullable=False, comment='菜单名称')
    parent_id = Column(INTEGER(11), nullable=False, comment='父菜单ID')
    menu_order = Column(INTEGER(11), nullable=False, comment='菜单排序')
    link = Column(Text(collation='utf8mb4_bin'), comment='菜单链接')
    icon = Column(Text(collation='utf8mb4_bin'), comment='菜单图标')
    permission = Column(JSON, comment='菜单权限')
    state = Column(INTEGER(11), nullable=False, comment='0: default, -1: deleted')
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))


class Model(Base):
    __tablename__ = 'model'

    id = Column(INTEGER(11), primary_key=True)
    model_id = Column(Text, nullable=False, comment='模型ID')
    name = Column(Text, nullable=False, comment='模型名称')
    description = Column(Text, comment='模型描述')
    type = Column(Text, nullable=False, comment='模型类型')
    version = Column(Text, nullable=False, comment='模型版本')
    source = Column(Text, nullable=False, comment='模型来源')
    parameters = Column(JSON, comment='模型参数')
    inference_type = Column(Text, nullable=False, comment='推理服务提供方类型')
    state = Column(INTEGER(11), nullable=False)
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')


class SystemQuestionDislike(Base):
    __tablename__ = 'system_question_dislike'

    id = Column(BIGINT(20), primary_key=True, comment='主键')
    evaluation_id = Column(String(500, 'utf8mb4_bin'), nullable=False, comment='评审ID')
    question_id = Column(BIGINT(20), nullable=False, comment='问题id')
    dislike_count = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='点踩数量')
    create_time = Column(DateTime)


class SystemQuestionDislikeUser(Base):
    __tablename__ = 'system_question_dislike_user'

    id = Column(BIGINT(20), primary_key=True, comment='主键')
    dislike_id = Column(BIGINT(20), nullable=False, comment='点踩表主键')
    evaluation_id = Column(String(500, 'utf8mb4_bin'), comment='评审ID')
    question_id = Column(BIGINT(20), comment='问题ID')
    content = Column(Text(collation='utf8mb4_bin'), comment='点踩内容')
    user_id = Column(BIGINT(20), nullable=False, comment='用户ID')
    user_name = Column(String(500, 'utf8mb4_bin'), comment='用户名')
    user_full_name = Column(String(500, 'utf8mb4_bin'), comment='用户名')
    create_time = Column(DateTime)


class SystemQuizCriteriaSetRelation(Base):
    __tablename__ = 'system_quiz_criteria_set_relations'

    id = Column(INTEGER(11), primary_key=True)
    criteria_id = Column(INTEGER(11), nullable=False, comment='评审标准ID')
    criteria_set_identifier = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='评审标准集合版本号')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class SystemQuizCriteriaSet(Base):
    __tablename__ = 'system_quiz_criteria_sets'

    id = Column(INTEGER(11), primary_key=True)
    criteria_set_identifier = Column(String(255, 'utf8mb4_bin'), nullable=False, unique=True, comment='评审标准集合版本号')
    quiz_type = Column(String(100, 'utf8mb4_bin'), nullable=False, server_default=text("'art'"), comment='评测类型  AB评测:ab, 人工评测:art, 自评测试:objective')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class SystemQuizCriteria(Base):
    __tablename__ = 'system_quiz_criterias'

    id = Column(INTEGER(11), primary_key=True)
    title = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='评分标准标题')
    value = Column(Text(collation='utf8mb4_bin'), nullable=False, comment='评分标准描述')
    rating_key = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='评审标准KEY')
    rating_type = Column(String(256, 'utf8mb4_bin'), server_default=text("'0'"), comment='评分标准类型')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class SystemQuizPlan(Base):
    __tablename__ = 'system_quiz_plan'

    id = Column(INTEGER(11), primary_key=True)
    plan_id = Column(String(255, 'utf8mb4_bin'), nullable=False, unique=True, comment='唯一的计划ID')
    plan_name = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='计划名称')
    quiz_type = Column(String(100, 'utf8mb4_bin'), nullable=False, server_default=text("'art'"), comment='评测类型  AB评测:ab, 人工评测:art, 自评测试:objective ')
    desc = Column(Text(collation='utf8mb4_bin'), nullable=False, comment='计划描述')
    eval_product = Column(Text(collation='utf8mb4_bin'), comment='评测产品')
    dim_set_id = Column(INTEGER(11), nullable=False, comment='评测维度集合版本ID')
    qa_round_dim_set_id = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='QA轮次维度集合版本ID')
    question_set_id = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='问题集合版本ID')
    user_set_id = Column(INTEGER(11), nullable=False, comment='评测人员集合版本ID')
    created_user = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='创建人')
    updated_user = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='更新人')
    trigger = Column(String(150, 'utf8mb4_bin'), comment='触发条件')
    expire = Column(BIGINT(20), comment='过期时间')
    state = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    model_id = Column(INTEGER(11), server_default=text("'0'"), comment='模型ID')
    model_name = Column(Text(collation='utf8mb4_bin'), comment='模型名称')
    replica_num = Column(INTEGER(11), server_default=text("'0'"), comment='副本数量')
    replica_gpu_num = Column(INTEGER(11), server_default=text("'0'"), comment='副本gpu数量')
    resource_group = Column(Text(collation='utf8mb4_bin'), comment='资源组')
    resource_pool = Column(Text(collation='utf8mb4_bin'), comment='资源池')
    deleted = Column(INTEGER(11), server_default=text("'0'"), comment='1-已删除0-未删除')
    gpu_type = Column(Text(collation='utf8mb4_bin'), comment='gpu类型')
    model_parameters = Column(Text(collation='utf8mb4_bin'), comment='模型参数')
    create_user_id = Column(INTEGER(11), comment='创建人')
    update_user_id = Column(INTEGER(11), comment='更新人')
    error_msg = Column(Text(collation='utf8mb4_bin'), comment='错误信息')
    model_type = Column(INTEGER(11), comment='模型类型1-base2-chat3-embedding4-reranker')


class SystemQuizPlanTask(Base):
    __tablename__ = 'system_quiz_plan_task'

    id = Column(INTEGER(11), primary_key=True)
    evaluation_id = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='唯一的评审ID')
    task_id = Column(String(100, 'utf8mb4_bin'), nullable=False, index=True, comment='任务ID')
    user_id = Column(String(100, 'utf8mb4_bin'), comment='用户ID')
    task_name = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='任务名称')
    task_desc = Column(Text(collation='utf8mb4_bin'), nullable=False, comment='任务描述')
    system_version = Column(String(255, 'utf8mb4_bin'), comment='系统版本号')
    criteria_set_identifier = Column(String(255, 'utf8mb4_bin'), comment='评审标准集合版本号')
    qa_round_criteria_set_identifier = Column(String(255, 'utf8mb4_bin'), comment='QA轮次标准集合版本号')
    dim_set_identifier = Column(String(255, 'utf8mb4_bin'), comment='评审维度集合版本号')
    qa_round_dim_set_identifier = Column(String(255, 'utf8mb4_bin'), comment='QA轮次维度集合版本号')
    system_version_info = Column(String(255, 'utf8mb4_bin'), comment='系统版本信息')
    question_set_identifier = Column(String(255, 'utf8mb4_bin'), comment='问题集合版本号')
    evaluation_state = Column(INTEGER(11), server_default=text("'0'"), comment='评审任务状态')
    evaluation_type = Column(String(100, 'utf8mb4_bin'), server_default=text("'art'"), comment='评测类型  ab测试: ab、人工评测: art')
    task_expire = Column(TIMESTAMP, comment='任务过期时间')
    task_state = Column(INTEGER(11), nullable=False, index=True, server_default=text("'0'"), comment='0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束')
    task_num = Column(INTEGER(11), server_default=text("'0'"), comment='任务数量')
    task_answer_num = Column(INTEGER(11), server_default=text("'0'"), comment='已获取答案的任务数量')
    task_evaluation_num = Column(INTEGER(11), server_default=text("'0'"), comment='已评测任务的数量')
    eval_product = Column(Text(collation='utf8mb4_bin'), comment='评测产品')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    created_user = Column(INTEGER(11), server_default=text("'0'"), comment='创建人id')
    created_user_name = Column(Text(collation='utf8mb4_bin'), comment='创建人')
    error_msg = Column(Text(collation='utf8mb4_bin'), comment='错误信息')


class SystemQuizPlanTaskInfo(Base):
    __tablename__ = 'system_quiz_plan_task_info'

    id = Column(INTEGER(11), primary_key=True)
    task_id = Column(String(100, 'utf8mb4_bin'), nullable=False, index=True, comment='任务ID')
    parent_task_id = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='父任务ID')
    user_id = Column(String(100, 'utf8mb4_bin'), comment='用户ID')
    question_id = Column(INTEGER(11), nullable=False, comment='问题ID')
    evaluation_id = Column(String(100, 'utf8mb4_bin'), comment='评审ID')
    system_answer = Column(Text(collation='utf8mb4_bin'), comment='系统答案')
    rating = Column(INTEGER(11), server_default=text("'0'"), comment='评分')
    system_type = Column(String(100, 'utf8mb4_bin'), server_default=text("'art'"), comment='评测方式  a系统: a、b系统: b、人工评测: art')
    trace_id = Column(String(255, 'utf8mb4_bin'))
    feedback = Column(Text(collation='utf8mb4_bin'), comment='反馈')
    source = Column(Text(collation='utf8mb4_bin'), comment='召回数据')
    task_state = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='0: 未开始 1: 生成中  2: 生成完成 3: 生成失败 4:答题进行中 5 答题完成  6 答题过期')
    system_url = Column(String(255, 'utf8mb4_bin'), comment='系统url')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    question = Column(Text(collation='utf8mb4_bin'), comment='问题')
    created_user = Column(INTEGER(11), server_default=text("'0'"), comment='创建人id')
    created_user_name = Column(Text(collation='utf8mb4_bin'), comment='创建人')


class SystemQuizQuestionSetRelation(Base):
    __tablename__ = 'system_quiz_question_set_relations'

    id = Column(INTEGER(11), primary_key=True)
    question_set_identifier = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='问题集合版本号')
    question_id = Column(INTEGER(11), nullable=False, comment='问题ID')
    created_at = Column(TIMESTAMP, comment='创建时间')
    updated_at = Column(TIMESTAMP, comment='更新时间')


class SystemQuizQuestionSetRelationsV2(Base):
    __tablename__ = 'system_quiz_question_set_relations_v2'

    id = Column(INTEGER(11), primary_key=True)
    question_set_identifier = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='问题集合版本号')
    question_id = Column(String(50, 'utf8mb4_bin'), nullable=False, comment='问题ID')
    created_at = Column(TIMESTAMP, comment='创建时间')
    updated_at = Column(TIMESTAMP, comment='更新时间')


class SystemQuizQuestionSet(Base):
    __tablename__ = 'system_quiz_question_sets'

    id = Column(INTEGER(11), primary_key=True)
    question_set_identifier = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='问题集合版本号')
    name = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='问题集合名称')
    desc = Column(Text(collation='utf8mb4_bin'), nullable=False, comment='问题集合描述')
    questions_number = Column(INTEGER(11), nullable=False, comment='问题数量')
    version = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='版本号')
    created_user_id = Column(String(100, 'utf8mb4_bin'), comment='创建人')
    updated_user_id = Column(String(100, 'utf8mb4_bin'), comment='更新人')
    state = Column(INTEGER(11), nullable=False, server_default=text("'1'"), comment='状态 0: 未发布 1:  可用 2: 不可用 3、删除态')
    created_at = Column(TIMESTAMP, comment='创建时间')
    updated_at = Column(TIMESTAMP, comment='更新时间')


class SystemQuizQuestionType(Base):
    __tablename__ = 'system_quiz_question_types'

    id = Column(INTEGER(11), primary_key=True)
    question_type = Column(String(100, 'utf8mb4_bin'), nullable=False, server_default=text("'qa'"), comment='题集类型 问答: qa单选题: single_select多选题: multiple_select填空题: fill_in_the_blank判断题: true_or_false多轮对话: multi_round_qa')
    question_type_desc = Column(String(100, 'utf8mb4_bin'), nullable=False, server_default=text("''"), comment='题型描述')
    created_at = Column(TIMESTAMP, comment='创建时间')
    updated_at = Column(TIMESTAMP, comment='更新时间')


class SystemQuizQuestion(Base):
    __tablename__ = 'system_quiz_questions'

    id = Column(INTEGER(11), primary_key=True, comment='问题ID')
    question = Column(String(512, 'utf8mb4_bin'), nullable=False, comment='问题')
    question_desc = Column(Text(collation='utf8mb4_bin'), nullable=False, comment='问题描述')
    question_type = Column(String(100, 'utf8mb4_bin'), nullable=False, server_default=text("'qa'"), comment='题集类型 问答: qa单选题: single_select多选题: multiple_select填空题: fill_in_the_blank判断题: true_or_false')
    llm_q_list = Column(Text(collation='utf8mb4_bin'), nullable=False, comment='填空题问题集')
    options = Column(Text(collation='utf8mb4_bin'), comment='选择题选项')
    reference_answer = Column(Text(collation='utf8mb4_bin'), nullable=False, comment='参考答案')
    created_at = Column(TIMESTAMP, comment='创建时间')
    updated_at = Column(TIMESTAMP, comment='更新时间')


class SystemQuizQuestionsV2(Base):
    __tablename__ = 'system_quiz_questions_v2'
    __table_args__ = (
        Index('ix_question_id_version', 'question_id', 'version', unique=True),
    )

    id = Column(INTEGER(11), primary_key=True, comment='问题ID')
    question_id = Column(String(50, 'utf8mb4_bin'), nullable=False)
    question = Column(String(512, 'utf8mb4_bin'), nullable=False, comment='问题')
    question_desc = Column(Text(collation='utf8mb4_bin'), nullable=False, comment='问题描述')
    question_type_id = Column(INTEGER(11), nullable=False, comment='题型ID')
    question_type = Column(String(100, 'utf8mb4_bin'), nullable=False, server_default=text("'qa'"), comment='题集类型 问答: qa单选题: single_select多选题: multiple_select填空题: fill_in_the_blank判断题: true_or_false多轮对话: multi_round_qa')
    difficulty = Column(INTEGER(11), comment='难度等级')
    category = Column(String(100, 'utf8mb4_bin'), comment='题目分类')
    explanation = Column(Text(collation='utf8mb4_bin'), comment='题目解释')
    version = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='版本号')
    details = Column(JSON, comment='题目详情')
    is_deleted = Column(TINYINT(1), nullable=False, server_default=text("'0'"), comment='是否删除 0: 未删除 1: 已删除')
    created_user_id = Column(String(100, 'utf8mb4_bin'), comment='创建人')
    updated_user_id = Column(String(100, 'utf8mb4_bin'), comment='更新人')
    state = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='状态 0: 未发布 1:  可用 2: 不可用 3、删除态')
    created_at = Column(TIMESTAMP, comment='创建时间')
    updated_at = Column(TIMESTAMP, comment='更新时间')


class SystemQuizTask(Base):
    __tablename__ = 'system_quiz_tasks'

    id = Column(INTEGER(11), primary_key=True)
    evaluation_id = Column(String(255, 'utf8mb4_bin'), nullable=False, unique=True, comment='唯一的评审ID')
    system_version = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='系统版本号')
    system_version_info = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='系统版本信息')
    criteria_set_identifier = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='评审标准集合版本号')
    question_set_identifier = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='问题集合版本号')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class SystemQuizUserSetRelation(Base):
    __tablename__ = 'system_quiz_user_set_relations'
    __table_args__ = (
        Index('_user_set_identifier_version_user_id_uc', 'user_set_identifier', 'version', 'user_id', unique=True),
    )

    id = Column(INTEGER(11), primary_key=True)
    user_id = Column(String(256, 'utf8mb4_bin'), nullable=False, comment='评测人员ID')
    user_set_identifier = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='评测人员集合版本号')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    version = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='版本号')


class SystemQuizUserSet(Base):
    __tablename__ = 'system_quiz_user_sets'
    __table_args__ = (
        Index('_user_set_identifier_version_uc', 'user_set_identifier', 'version', unique=True),
    )

    id = Column(INTEGER(11), primary_key=True)
    user_set_identifier = Column(String(255, 'utf8mb4_bin'), nullable=False, unique=True, comment='评测人员集合版本号')
    name = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='评测人员集合名称')
    quiz_type = Column(String(100, 'utf8mb4_bin'), nullable=False, server_default=text("'art'"), comment='评测类型  AB评测:ab, 人工评测:art, 自评测试:objective')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    created_user = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='创建人')
    updated_user = Column(String(100, 'utf8mb4_bin'), comment='更新人')
    version = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='版本号')
    description = Column(Text(collation='utf8mb4_bin'), nullable=False, comment='描述')
    state = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='状态 0: 未发布 1:  可用 2: 不可用 3、删除态')


class User(Base):
    __tablename__ = 'user'

    id = Column(INTEGER(11), primary_key=True)
    user_id = Column(TINYTEXT, nullable=False)
    name = Column(TINYTEXT, nullable=False, comment='系统用户名，例如邮箱前缀等')
    full_name = Column(TINYTEXT, nullable=False, comment='用户姓名')
    department = Column(TINYTEXT, nullable=False, comment='用户部门')
    permission = Column(JSON, comment='用户权限')
    state = Column(INTEGER(11), nullable=False)
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))


class UserGroup(Base):
    __tablename__ = 'user_group'

    id = Column(INTEGER(11), primary_key=True)
    group_id = Column(TINYTEXT, nullable=False)
    name = Column(TINYTEXT, nullable=False, comment='用户组名称')
    parent_id = Column(INTEGER(11), nullable=False, comment='父用户组ID')
    permission = Column(JSON, comment='用户组权限')
    state = Column(INTEGER(11), nullable=False)
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))


class UserQuizInfo(Base):
    __tablename__ = 'user_quiz_infos'

    id = Column(INTEGER(11), primary_key=True)
    evaluation_id = Column(String(255, 'utf8mb4_bin'), nullable=False, unique=True, comment='唯一的评审ID')
    user_id = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='用户ID')
    system_version = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='系统版本号')
    system_version_info = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='系统版本信息')
    criteria_set_identifier = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='评审标准集合版本号')
    question_set_identifier = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='问题集合版本号')
    evaluation_state = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='评审任务状态')
    task_count = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='评审任务总数')
    completed_count = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='已完成任务数量')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class UserQuizTask(Base):
    __tablename__ = 'user_quiz_tasks'

    id = Column(INTEGER(11), primary_key=True)
    evaluation_id = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='唯一的评审ID')
    task_id = Column(String(100, 'utf8mb4_bin'), nullable=False, index=True, comment='任务ID')
    user_id = Column(String(100, 'utf8mb4_bin'), comment='用户ID')
    task_name = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='任务名称')
    task_desc = Column(Text(collation='utf8mb4_bin'), nullable=False, comment='任务描述')
    system_version = Column(String(255, 'utf8mb4_bin'), comment='系统版本号')
    criteria_set_identifier = Column(String(255, 'utf8mb4_bin'), comment='评审标准集合版本号')
    qa_round_criteria_set_identifier = Column(String(255, 'utf8mb4_bin'), comment='QA轮次标准集合版本号')
    dim_set_identifier = Column(String(255, 'utf8mb4_bin'), comment='评审维度集合版本号')
    qa_round_dim_set_identifier = Column(String(255, 'utf8mb4_bin'), comment='QA轮次维度集合版本号')
    system_version_info = Column(String(255, 'utf8mb4_bin'), comment='系统版本信息')
    question_set_identifier = Column(String(255, 'utf8mb4_bin'), comment='问题集合版本号')
    evaluation_state = Column(INTEGER(11), server_default=text("'0'"), comment='评审任务状态')
    evaluation_type = Column(String(100, 'utf8mb4_bin'), server_default=text("'art'"), comment='评测类型  ab测试: ab、人工评测: art')
    task_expire = Column(TIMESTAMP, comment='任务过期时间')
    task_state = Column(INTEGER(11), nullable=False, index=True, server_default=text("'0'"), comment='1: 未开始  2: 评测中 3: 评测完成')
    task_num = Column(INTEGER(11), server_default=text("'0'"), comment='任务数量')
    task_answer_num = Column(INTEGER(11), server_default=text("'0'"), comment='已获取答案的任务数量')
    task_evaluation_num = Column(INTEGER(11), server_default=text("'0'"), comment='已评测任务的数量')
    eval_product = Column(Text(collation='utf8mb4_bin'), comment='评测产品')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    plan_id = Column(INTEGER(11), server_default=text("'0'"), comment='计划id')
    created_user = Column(INTEGER(11), server_default=text("'0'"), comment='创建人id')
    created_user_name = Column(Text(collation='utf8mb4_bin'), comment='创建人')


class UserQuizTasksCriteriaInfo(Base):
    __tablename__ = 'user_quiz_tasks_criteria_info'

    id = Column(INTEGER(11), primary_key=True)
    task_id = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='任务ID')
    user_id = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='用户ID')
    question_id = Column(INTEGER(11), nullable=False, comment='问题ID')
    title = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='标题')
    rating_key = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='评审标准KEY')
    rating_type = Column(String(256, 'utf8mb4_bin'), server_default=text("'0'"), comment='评分标准类型')
    rating_int_value = Column(INTEGER(11), nullable=False, comment='评分')
    rating_string_value = Column(String(255, 'utf8mb4_bin'), nullable=False, comment='评分')
    system_type = Column(String(100, 'utf8mb4_bin'), nullable=False, server_default=text("'art'"), comment='评测方式  a系统: a、b系统: b、人工评测: art')
    feedback = Column(Text(collation='utf8mb4_bin'), comment='反馈')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class UserQuizTasksInfo(Base):
    __tablename__ = 'user_quiz_tasks_info'

    id = Column(INTEGER(11), primary_key=True)
    task_id = Column(String(100, 'utf8mb4_bin'), nullable=False, index=True, comment='任务ID')
    parent_task_id = Column(String(100, 'utf8mb4_bin'), nullable=False, comment='父任务ID')
    user_id = Column(String(100, 'utf8mb4_bin'), comment='用户ID')
    question_id = Column(INTEGER(11), nullable=False, comment='问题ID')
    evaluation_id = Column(String(100, 'utf8mb4_bin'), comment='评审ID')
    system_answer = Column(Text(collation='utf8mb4_bin'), comment='系统答案')
    rating = Column(INTEGER(11), server_default=text("'0'"), comment='评分')
    system_type = Column(String(100, 'utf8mb4_bin'), server_default=text("'art'"), comment='评测方式  a系统: a、b系统: b、人工评测: art')
    trace_id = Column(String(255, 'utf8mb4_bin'))
    feedback = Column(Text(collation='utf8mb4_bin'), comment='反馈')
    source = Column(Text(collation='utf8mb4_bin'), comment='召回数据')
    task_state = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='0: 未开始 1: 评分中  2: 评分完成')
    system_url = Column(String(255, 'utf8mb4_bin'), comment='系统url')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    plan_id = Column(INTEGER(11), server_default=text("'0'"), comment='计划id')
    question = Column(Text(collation='utf8mb4_bin'), comment='问题')
    created_user = Column(INTEGER(11), server_default=text("'0'"), comment='创建人id')
    created_user_name = Column(Text(collation='utf8mb4_bin'), comment='创建人')

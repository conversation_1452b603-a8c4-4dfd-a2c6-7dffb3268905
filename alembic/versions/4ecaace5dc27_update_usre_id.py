"""update_usre_id

Revision ID: 4ecaace5dc27
Revises: 72fba4638aa7
Create Date: 2024-10-16 14:59:58.177915

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4ecaace5dc27'
down_revision: Union[str, None] = '7ef40ca96aca'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    with open(
            "alembic/versions/4ecaace5dc27.sql", "r", encoding='utf-8'
    ) as file:
        for line in file.readlines():
            if sql_command := line.strip():
                op.execute(sql_command)


def downgrade() -> None:
    pass

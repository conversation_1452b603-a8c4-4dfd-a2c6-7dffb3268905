"""init

Revision ID: 626c546843e7
Revises: 
Create Date: 2024-10-11 19:19:37.309843

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

revision: str = '626c546843e7'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('inference_service',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('plan_id', sa.String(length=500), server_default=sa.text("'0'"), nullable=False, comment='计划id'),
                    sa.Column('plan_name', sa.Text(), nullable=True, comment='计划名称'),
                    sa.Column('model_id', mysql.INTEGER(display_width=11), nullable=False, comment='模型ID'),
                    sa.Column('model_name', sa.Text(), nullable=True, comment='模型名称'),
                    sa.Column('inference_service_id', mysql.INTEGER(display_width=11), nullable=True, comment='推理服务ID'),
                    sa.Column('inference_state', mysql.INTEGER(display_width=11), nullable=True, comment='推理服务状态1-更新中2-运行中3-下线中4-已下线5-异常'),
                    sa.Column('internal_host', sa.Text(), nullable=True, comment='内部服务Host'),
                    sa.Column('external_path', sa.Text(), nullable=True, comment='外部服务路径'),
                    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.Column('model_type', mysql.INTEGER(display_width=11), nullable=True, comment='模型类型'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('menu',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('name', mysql.TINYTEXT(), nullable=False, comment='菜单名称'),
                    sa.Column('parent_id', mysql.INTEGER(display_width=11), nullable=False, comment='父菜单ID'),
                    sa.Column('menu_order', mysql.INTEGER(display_width=11), nullable=False, comment='菜单排序'),
                    sa.Column('link', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='菜单链接'),
                    sa.Column('icon', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='菜单图标'),
                    sa.Column('permission', sa.JSON(), nullable=True, comment='菜单权限'),
                    sa.Column('state', mysql.INTEGER(display_width=11), nullable=False, comment='0: default, -1: deleted'),
                    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
                    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('model',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('model_id', sa.Text(), nullable=False, comment='模型ID'),
                    sa.Column('name', sa.Text(), nullable=False, comment='模型名称'),
                    sa.Column('description', sa.Text(), nullable=True, comment='模型描述'),
                    sa.Column('type', sa.Text(), nullable=False, comment='模型类型'),
                    sa.Column('version', sa.Text(), nullable=False, comment='模型版本'),
                    sa.Column('source', sa.Text(), nullable=False, comment='模型来源'),
                    sa.Column('parameters', sa.JSON(), nullable=True, comment='模型参数'),
                    sa.Column('inference_type', sa.Text(), nullable=False, comment='推理服务提供方类型'),
                    sa.Column('state', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('system_question_dislike',
                    sa.Column('id', mysql.BIGINT(display_width=20), nullable=False, comment='主键'),
                    sa.Column('evaluation_id', sa.String(length=500, collation='utf8mb4_bin'), nullable=False, comment='评审ID'),
                    sa.Column('question_id', mysql.BIGINT(display_width=20), nullable=False, comment='问题id'),
                    sa.Column('dislike_count', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='点踩数量'),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('system_question_dislike_user',
                    sa.Column('id', mysql.BIGINT(display_width=20), nullable=False, comment='主键'),
                    sa.Column('dislike_id', mysql.BIGINT(display_width=20), nullable=False, comment='点踩表主键'),
                    sa.Column('evaluation_id', sa.String(length=500, collation='utf8mb4_bin'), nullable=True, comment='评审ID'),
                    sa.Column('question_id', mysql.BIGINT(display_width=20), nullable=True, comment='问题ID'),
                    sa.Column('content', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='点踩内容'),
                    sa.Column('user_id', mysql.BIGINT(display_width=20), nullable=False, comment='用户ID'),
                    sa.Column('user_name', sa.String(length=500, collation='utf8mb4_bin'), nullable=True, comment='用户名'),
                    sa.Column('user_full_name', sa.String(length=500, collation='utf8mb4_bin'), nullable=True, comment='用户名'),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('system_quiz_criteria_set_relations',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('criteria_id', mysql.INTEGER(display_width=11), nullable=False, comment='评审标准ID'),
                    sa.Column('criteria_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='评审标准集合版本号'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('system_quiz_criteria_sets',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('criteria_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='评审标准集合版本号'),
                    sa.Column('quiz_type', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("'art'"), nullable=False, comment='评测类型  AB评测:ab, 人工评测:art, 自评测试:objective'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id'),
                    sa.UniqueConstraint('criteria_set_identifier')
                    )
    op.create_table('system_quiz_criterias',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('title', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='评分标准标题'),
                    sa.Column('value', sa.Text(collation='utf8mb4_bin'), nullable=False, comment='评分标准描述'),
                    sa.Column('rating_key', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='评审标准KEY'),
                    sa.Column('rating_type', sa.String(length=256, collation='utf8mb4_bin'), server_default=sa.text("'0'"), nullable=True, comment='评分标准类型'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('system_quiz_plan',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('plan_id', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='唯一的计划ID'),
                    sa.Column('plan_name', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='计划名称'),
                    sa.Column('quiz_type', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("'art'"), nullable=False, comment='评测类型  AB评测:ab, 人工评测:art, 自评测试:objective '),
                    sa.Column('desc', sa.Text(collation='utf8mb4_bin'), nullable=False, comment='计划描述'),
                    sa.Column('eval_product', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='评测产品'),
                    sa.Column('dim_set_id', mysql.INTEGER(display_width=11), nullable=False, comment='评测维度集合版本ID'),
                    sa.Column('qa_round_dim_set_id', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='QA轮次维度集合版本ID'),
                    sa.Column('question_set_id', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='问题集合版本ID'),
                    sa.Column('user_set_id', mysql.INTEGER(display_width=11), nullable=False, comment='评测人员集合版本ID'),
                    sa.Column('created_user', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='创建人'),
                    sa.Column('updated_user', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='更新人'),
                    sa.Column('trigger', sa.String(length=150, collation='utf8mb4_bin'), nullable=True, comment='触发条件'),
                    sa.Column('expire', mysql.BIGINT(display_width=20), nullable=True, comment='过期时间'),
                    sa.Column('state', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='0: 未发布 1: 排队中 2: 模型推理中 3: 模型推理失败 4: 待评测 5: 评测中 6: 结果生成中 7: 任务结束'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.Column('model_id', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='模型ID'),
                    sa.Column('model_name', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='模型名称'),
                    sa.Column('replica_num', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='副本数量'),
                    sa.Column('replica_gpu_num', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='副本gpu数量'),
                    sa.Column('resource_group', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='资源组'),
                    sa.Column('resource_pool', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='资源池'),
                    sa.Column('deleted', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='1-已删除0-未删除'),
                    sa.Column('gpu_type', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='gpu类型'),
                    sa.Column('model_parameters', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='模型参数'),
                    sa.Column('create_user_id', mysql.INTEGER(display_width=11), nullable=True, comment='创建人'),
                    sa.Column('update_user_id', mysql.INTEGER(display_width=11), nullable=True, comment='更新人'),
                    sa.Column('error_msg', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='错误信息'),
                    sa.Column('model_type', mysql.INTEGER(display_width=11), nullable=True, comment='模型类型1-base2-chat3-embedding4-reranker'),
                    sa.PrimaryKeyConstraint('id'),
                    sa.UniqueConstraint('plan_id')
                    )
    op.create_table('system_quiz_plan_task',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('evaluation_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='唯一的评审ID'),
                    sa.Column('task_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='任务ID'),
                    sa.Column('user_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='用户ID'),
                    sa.Column('task_name', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='任务名称'),
                    sa.Column('task_desc', sa.Text(collation='utf8mb4_bin'), nullable=False, comment='任务描述'),
                    sa.Column('system_version', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='系统版本号'),
                    sa.Column('criteria_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='评审标准集合版本号'),
                    sa.Column('qa_round_criteria_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='QA轮次标准集合版本号'),
                    sa.Column('dim_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='评审维度集合版本号'),
                    sa.Column('qa_round_dim_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='QA轮次维度集合版本号'),
                    sa.Column('system_version_info', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='系统版本信息'),
                    sa.Column('question_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='问题集合版本号'),
                    sa.Column('evaluation_state', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='评审任务状态'),
                    sa.Column('evaluation_type', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("'art'"), nullable=True, comment='评测类型  ab测试: ab、人工评测: art'),
                    sa.Column('task_expire', sa.TIMESTAMP(), nullable=True, comment='任务过期时间'),
                    sa.Column('task_state', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5: 评测中  6: 结果生成中 7: 任务结束'),
                    sa.Column('task_num', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='任务数量'),
                    sa.Column('task_answer_num', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='已获取答案的任务数量'),
                    sa.Column('task_evaluation_num', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='已评测任务的数量'),
                    sa.Column('eval_product', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='评测产品'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.Column('created_user', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='创建人id'),
                    sa.Column('created_user_name', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='创建人'),
                    sa.Column('error_msg', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='错误信息'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_index(op.f('ix_system_quiz_plan_task_task_id'), 'system_quiz_plan_task', ['task_id'], unique=False)
    op.create_index(op.f('ix_system_quiz_plan_task_task_state'), 'system_quiz_plan_task', ['task_state'], unique=False)
    op.create_table('system_quiz_plan_task_info',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('task_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='任务ID'),
                    sa.Column('parent_task_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='父任务ID'),
                    sa.Column('user_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='用户ID'),
                    sa.Column('question_id', mysql.INTEGER(display_width=11), nullable=False, comment='问题ID'),
                    sa.Column('evaluation_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='评审ID'),
                    sa.Column('system_answer', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='系统答案'),
                    sa.Column('rating', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='评分'),
                    sa.Column('system_type', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("'art'"), nullable=True, comment='评测方式  ab评测: ab 人工评测: art'),
                    sa.Column('trace_id', sa.String(length=255, collation='utf8mb4_bin'), nullable=True),
                    sa.Column('feedback', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='反馈'),
                    sa.Column('source', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='召回数据'),
                    sa.Column('task_state', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='0: 未开始 1: 生成中  2: 生成完成 3: 生成失败 4:答题进行中 5: 答题完成  6: 答题过期'),
                    sa.Column('system_url', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='系统url'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.Column('question', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='问题'),
                    sa.Column('created_user', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='创建人id'),
                    sa.Column('created_user_name', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='创建人'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_index(op.f('ix_system_quiz_plan_task_info_task_id'), 'system_quiz_plan_task_info', ['task_id'], unique=False)
    op.create_table('system_quiz_question_set_relations',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('question_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='问题集合版本号'),
                    sa.Column('question_id', mysql.INTEGER(display_width=11), nullable=False, comment='问题ID'),
                    sa.Column('created_at', sa.TIMESTAMP(), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('system_quiz_question_set_relations_v2',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('question_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='问题集合版本号'),
                    sa.Column('question_id', sa.String(length=50, collation='utf8mb4_bin'), nullable=False, comment='问题ID'),
                    sa.Column('created_at', sa.TIMESTAMP(), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('system_quiz_question_sets',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('question_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='问题集合版本号'),
                    sa.Column('name', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='问题集合名称'),
                    sa.Column('desc', sa.Text(collation='utf8mb4_bin'), nullable=False, comment='问题集合描述'),
                    sa.Column('questions_number', mysql.INTEGER(display_width=11), nullable=False, comment='问题数量'),
                    sa.Column('version', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='版本号'),
                    sa.Column('created_user_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='创建人'),
                    sa.Column('updated_user_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='更新人'),
                    sa.Column('state', mysql.INTEGER(display_width=11), server_default=sa.text("'1'"), nullable=False, comment='状态 0: 未发布 1:  可用 2: 不可用 3、删除态'),
                    sa.Column('created_at', sa.TIMESTAMP(), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('system_quiz_question_types',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('question_type', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("'qa'"), nullable=False, comment='题集类型 问答: qa单选题: single_select多选题: multiple_select填空题: fill_in_the_blank判断题: true_or_false多轮对话: multi_round_qa'),
                    sa.Column('question_type_desc', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("''"), nullable=False, comment='题型描述'),
                    sa.Column('created_at', sa.TIMESTAMP(), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('system_quiz_questions',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False, comment='问题ID'),
                    sa.Column('question', sa.String(length=512, collation='utf8mb4_bin'), nullable=False, comment='问题'),
                    sa.Column('question_desc', sa.Text(collation='utf8mb4_bin'), nullable=False, comment='问题描述'),
                    sa.Column('question_type', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("'qa'"), nullable=False, comment='题集类型 问答: qa单选题: single_select多选题: multiple_select填空题: fill_in_the_blank判断题: true_or_false'),
                    sa.Column('llm_q_list', sa.Text(collation='utf8mb4_bin'), nullable=False, comment='填空题问题集'),
                    sa.Column('options', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='选择题选项'),
                    sa.Column('reference_answer', sa.Text(collation='utf8mb4_bin'), nullable=False, comment='参考答案'),
                    sa.Column('created_at', sa.TIMESTAMP(), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('system_quiz_questions_v2',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False, comment='问题ID'),
                    sa.Column('question_id', sa.String(length=50, collation='utf8mb4_bin'), nullable=False),
                    sa.Column('question', sa.String(length=512, collation='utf8mb4_bin'), nullable=False, comment='问题'),
                    sa.Column('question_desc', sa.Text(collation='utf8mb4_bin'), nullable=False, comment='问题描述'),
                    sa.Column('question_type_id', mysql.INTEGER(display_width=11), nullable=False, comment='题型ID'),
                    sa.Column('question_type', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("'qa'"), nullable=False, comment='题集类型 问答: qa单选题: single_select多选题: multiple_select填空题: fill_in_the_blank判断题: true_or_false多轮对话: multi_round_qa'),
                    sa.Column('difficulty', mysql.INTEGER(display_width=11), nullable=True, comment='难度等级'),
                    sa.Column('category', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='题目分类'),
                    sa.Column('explanation', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='题目解释'),
                    sa.Column('version', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='版本号'),
                    sa.Column('details', sa.JSON(), nullable=True, comment='题目详情'),
                    sa.Column('is_deleted', mysql.TINYINT(display_width=1), server_default=sa.text("'0'"), nullable=False, comment='是否删除 0: 未删除 1: 已删除'),
                    sa.Column('created_user_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='创建人'),
                    sa.Column('updated_user_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='更新人'),
                    sa.Column('state', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='状态 0: 未发布 1: 可用 2: 不可用 3: 删除态'),
                    sa.Column('created_at', sa.TIMESTAMP(), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_index('ix_question_id_version', 'system_quiz_questions_v2', ['question_id', 'version'], unique=True)
    op.create_table('system_quiz_tasks',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('evaluation_id', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='唯一的评审ID'),
                    sa.Column('system_version', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='系统版本号'),
                    sa.Column('system_version_info', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='系统版本信息'),
                    sa.Column('criteria_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='评审标准集合版本号'),
                    sa.Column('question_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='问题集合版本号'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id'),
                    sa.UniqueConstraint('evaluation_id')
                    )
    op.create_table('system_quiz_user_set_relations',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('user_id', sa.String(length=256, collation='utf8mb4_bin'), nullable=False, comment='评测人员ID'),
                    sa.Column('user_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='评测人员集合版本号'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.Column('version', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='版本号'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_index('_user_set_identifier_version_user_id_uc', 'system_quiz_user_set_relations', ['user_set_identifier', 'version', 'user_id'], unique=True)
    op.create_table('system_quiz_user_sets',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('user_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='评测人员集合版本号'),
                    sa.Column('name', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='评测人员集合名称'),
                    sa.Column('quiz_type', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("'art'"), nullable=False, comment='评测类型  AB评测:ab, 人工评测:art, 自评测试:objective'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.Column('created_user', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='创建人'),
                    sa.Column('updated_user', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='更新人'),
                    sa.Column('version', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='版本号'),
                    sa.Column('description', sa.Text(collation='utf8mb4_bin'), nullable=False, comment='描述'),
                    sa.Column('state', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='状态 0: 未发布 1:  可用 2: 不可用 3: 删除态'),
                    sa.PrimaryKeyConstraint('id'),
                    sa.UniqueConstraint('user_set_identifier')
                    )
    op.create_index('_user_set_identifier_version_uc', 'system_quiz_user_sets', ['user_set_identifier', 'version'], unique=True)
    op.create_table('user',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('user_id', mysql.TINYTEXT(), nullable=False),
                    sa.Column('name', mysql.TINYTEXT(), nullable=False, comment='系统用户名，例如邮箱前缀等'),
                    sa.Column('full_name', mysql.TINYTEXT(), nullable=False, comment='用户姓名'),
                    sa.Column('department', mysql.TINYTEXT(), nullable=False, comment='用户部门'),
                    sa.Column('permission', sa.JSON(), nullable=True, comment='用户权限'),
                    sa.Column('state', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
                    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('user_group',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('group_id', mysql.TINYTEXT(), nullable=False),
                    sa.Column('name', mysql.TINYTEXT(), nullable=False, comment='用户组名称'),
                    sa.Column('parent_id', mysql.INTEGER(display_width=11), nullable=False, comment='父用户组ID'),
                    sa.Column('permission', sa.JSON(), nullable=True, comment='用户组权限'),
                    sa.Column('state', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
                    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('user_quiz_infos',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('evaluation_id', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='唯一的评审ID'),
                    sa.Column('user_id', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='用户ID'),
                    sa.Column('system_version', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='系统版本号'),
                    sa.Column('system_version_info', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='系统版本信息'),
                    sa.Column('criteria_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='评审标准集合版本号'),
                    sa.Column('question_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='问题集合版本号'),
                    sa.Column('evaluation_state', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='评审任务状态'),
                    sa.Column('task_count', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='评审任务总数'),
                    sa.Column('completed_count', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='已完成任务数量'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id'),
                    sa.UniqueConstraint('evaluation_id')
                    )
    op.create_table('user_quiz_tasks',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('evaluation_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='唯一的评审ID'),
                    sa.Column('task_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='任务ID'),
                    sa.Column('user_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='用户ID'),
                    sa.Column('task_name', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='任务名称'),
                    sa.Column('task_desc', sa.Text(collation='utf8mb4_bin'), nullable=False, comment='任务描述'),
                    sa.Column('system_version', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='系统版本号'),
                    sa.Column('criteria_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='评审标准集合版本号'),
                    sa.Column('qa_round_criteria_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='QA轮次标准集合版本号'),
                    sa.Column('dim_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='评审维度集合版本号'),
                    sa.Column('qa_round_dim_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='QA轮次维度集合版本号'),
                    sa.Column('system_version_info', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='系统版本信息'),
                    sa.Column('question_set_identifier', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='问题集合版本号'),
                    sa.Column('evaluation_state', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='评审任务状态'),
                    sa.Column('evaluation_type', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("'art'"), nullable=True, comment='评测类型  ab测试: ab、人工评测: art'),
                    sa.Column('task_expire', sa.TIMESTAMP(), nullable=True, comment='任务过期时间'),
                    sa.Column('task_state', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='1: 未开始  2: 评测中 3: 评测完成'),
                    sa.Column('task_num', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='任务数量'),
                    sa.Column('task_answer_num', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='已获取答案的任务数量'),
                    sa.Column('task_evaluation_num', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='已评测任务的数量'),
                    sa.Column('eval_product', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='评测产品'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.Column('plan_id', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='计划id'),
                    sa.Column('created_user', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='创建人id'),
                    sa.Column('created_user_name', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='创建人'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_index(op.f('ix_user_quiz_tasks_task_id'), 'user_quiz_tasks', ['task_id'], unique=False)
    op.create_index(op.f('ix_user_quiz_tasks_task_state'), 'user_quiz_tasks', ['task_state'], unique=False)
    op.create_table('user_quiz_tasks_criteria_info',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('task_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='任务ID'),
                    sa.Column('user_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='用户ID'),
                    sa.Column('question_id', mysql.INTEGER(display_width=11), nullable=False, comment='问题ID'),
                    sa.Column('title', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='标题'),
                    sa.Column('rating_key', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='评审标准KEY'),
                    sa.Column('rating_type', sa.String(length=256, collation='utf8mb4_bin'), server_default=sa.text("'0'"), nullable=True, comment='评分标准类型'),
                    sa.Column('rating_int_value', mysql.INTEGER(display_width=11), nullable=False, comment='评分'),
                    sa.Column('rating_string_value', sa.String(length=255, collation='utf8mb4_bin'), nullable=False, comment='评分'),
                    sa.Column('system_type', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("'art'"), nullable=False, comment='评测方式  ab测试: ab、人工评测: art'),
                    sa.Column('feedback', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='反馈'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('user_quiz_tasks_info',
                    sa.Column('id', mysql.INTEGER(display_width=11), nullable=False),
                    sa.Column('task_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='任务ID'),
                    sa.Column('parent_task_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=False, comment='父任务ID'),
                    sa.Column('user_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='用户ID'),
                    sa.Column('question_id', mysql.INTEGER(display_width=11), nullable=False, comment='问题ID'),
                    sa.Column('evaluation_id', sa.String(length=100, collation='utf8mb4_bin'), nullable=True, comment='评审ID'),
                    sa.Column('system_answer', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='系统答案'),
                    sa.Column('rating', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='评分'),
                    sa.Column('system_type', sa.String(length=100, collation='utf8mb4_bin'), server_default=sa.text("'art'"), nullable=True, comment='评测方式  ab测试: ab、人工评测: art'),
                    sa.Column('trace_id', sa.String(length=255, collation='utf8mb4_bin'), nullable=True),
                    sa.Column('feedback', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='反馈'),
                    sa.Column('source', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='召回数据'),
                    sa.Column('task_state', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=False, comment='0: 未开始 1: 评分中  2: 评分完成'),
                    sa.Column('system_url', sa.String(length=255, collation='utf8mb4_bin'), nullable=True, comment='系统url'),
                    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True, comment='创建时间'),
                    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True, comment='更新时间'),
                    sa.Column('plan_id', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='计划id'),
                    sa.Column('question', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='问题'),
                    sa.Column('created_user', mysql.INTEGER(display_width=11), server_default=sa.text("'0'"), nullable=True, comment='创建人id'),
                    sa.Column('created_user_name', sa.Text(collation='utf8mb4_bin'), nullable=True, comment='创建人'),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_index(op.f('ix_user_quiz_tasks_info_task_id'), 'user_quiz_tasks_info', ['task_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_quiz_tasks_info_task_id'), table_name='user_quiz_tasks_info')
    op.drop_table('user_quiz_tasks_info')
    op.drop_table('user_quiz_tasks_criteria_info')
    op.drop_index(op.f('ix_user_quiz_tasks_task_state'), table_name='user_quiz_tasks')
    op.drop_index(op.f('ix_user_quiz_tasks_task_id'), table_name='user_quiz_tasks')
    op.drop_table('user_quiz_tasks')
    op.drop_table('user_quiz_infos')
    op.drop_table('user_group')
    op.drop_table('user')
    op.drop_index('_user_set_identifier_version_uc', table_name='system_quiz_user_sets')
    op.drop_table('system_quiz_user_sets')
    op.drop_index('_user_set_identifier_version_user_id_uc', table_name='system_quiz_user_set_relations')
    op.drop_table('system_quiz_user_set_relations')
    op.drop_table('system_quiz_tasks')
    op.drop_index('ix_question_id_version', table_name='system_quiz_questions_v2')
    op.drop_table('system_quiz_questions_v2')
    op.drop_table('system_quiz_questions')
    op.drop_table('system_quiz_question_types')
    op.drop_table('system_quiz_question_sets')
    op.drop_table('system_quiz_question_set_relations_v2')
    op.drop_table('system_quiz_question_set_relations')
    op.drop_index(op.f('ix_system_quiz_plan_task_info_task_id'), table_name='system_quiz_plan_task_info')
    op.drop_table('system_quiz_plan_task_info')
    op.drop_index(op.f('ix_system_quiz_plan_task_task_state'), table_name='system_quiz_plan_task')
    op.drop_index(op.f('ix_system_quiz_plan_task_task_id'), table_name='system_quiz_plan_task')
    op.drop_table('system_quiz_plan_task')
    op.drop_table('system_quiz_plan')
    op.drop_table('system_quiz_criterias')
    op.drop_table('system_quiz_criteria_sets')
    op.drop_table('system_quiz_criteria_set_relations')
    op.drop_table('system_question_dislike_user')
    op.drop_table('system_question_dislike')
    op.drop_table('model')
    op.drop_table('menu')
    op.drop_table('inference_service')
    # ### end Alembic commands ###


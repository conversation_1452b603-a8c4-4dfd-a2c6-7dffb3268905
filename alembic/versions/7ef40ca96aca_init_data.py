"""init-data

Revision ID: 7ef40ca96aca
Revises: 626c546843e7
Create Date: 2024-10-12 16:22:33.881958

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision: str = '7ef40ca96aca'
down_revision: Union[str, None] = '626c546843e7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    with open(
            "alembic/versions/7ef40ca96aca.sql", "r", encoding='utf-8'
    ) as file:
        for line in file.readlines():
            if sql_command := line.strip():
                op.execute(sql_command)


def downgrade() -> None:
    pass

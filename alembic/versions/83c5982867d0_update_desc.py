"""update_desc

Revision ID: 83c5982867d0
Revises: 4ecaace5dc27
Create Date: 2024-10-17 15:36:19.883368

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '83c5982867d0'
down_revision: Union[str, None] = '4ecaace5dc27'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    with open(
            "alembic/versions/83c5982867d0.sql", "r", encoding='utf-8'
    ) as file:
        for line in file.readlines():
            if sql_command := line.strip():
                op.execute(sql_command)


def downgrade() -> None:
    pass

"""update_system_quiz_plan_snapshot

Revision ID: d8b973176a84
Revises: f545dd78e9e7
Create Date: 2024-10-30 11:33:39.421445

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd8b973176a84'
down_revision: Union[str, None] = '83c5982867d0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("DROP TABLE IF EXISTS `system_quiz_plan_snapshot`;")
    op.create_table(
        'system_quiz_plan_snapshot',
        sa.Column('id', sa.Integer(), nullable=False, autoincrement=True),
        sa.Column('evaluation_id', sa.String(length=500, collation='utf8mb4_general_ci'), nullable=False,  comment='任务id'),
        sa.Column('question_set_config', sa.Text(collation='utf8mb4_general_ci'), nullable=True, comment='题集配置'),
        sa.Column('question_set_identifier', sa.String(length=500, collation='utf8mb4_general_ci'), nullable=True, comment='题集UUid'),
        sa.Column('user_set_config', sa.Text(collation='utf8mb4_general_ci'), nullable=True, comment='评测组配置'),
        sa.Column('user_set_identifier', sa.String(length=500, collation='utf8mb4_general_ci'), nullable=True, comment='用户集uuid'),
        sa.Column('inference_config', sa.Text(collation='utf8mb4_general_ci'), nullable=True, comment='推理配置'),
        sa.Column('create_time', sa.DateTime(), nullable=True, server_default=sa.func.current_timestamp(),comment='创建时间'),
        sa.PrimaryKeyConstraint('id', name='pk_system_quiz_plan_snapshot'),
        mysql_engine='InnoDB',
        mysql_charset='utf8mb4',
        mysql_collate='utf8mb4_general_ci',
        mysql_row_format='DYNAMIC'
    )


def downgrade() -> None:
    pass
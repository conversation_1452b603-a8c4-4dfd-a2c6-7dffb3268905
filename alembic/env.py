import os
from logging.config import fileConfig

from alembic import context
from sqlalchemy import engine_from_config
from sqlalchemy import pool

from evaluation.share.orm import model
from evaluation.share.util import aes_utils
from evaluation.share.util.app_config import config_instance, CONFIG
from urllib.parse import quote_plus

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

config_instance.load_config(prefix="config", env=os.environ.get("APP_ENV", "local"))
# alembic/env.py 配置
db_username = CONFIG["mysql"]["db_username"]
db_password = CONFIG["mysql"]["db_password"]
db_host = CONFIG["mysql"]["db_host"]
db_port = CONFIG["mysql"]["db_port"]
db_name = CONFIG["mysql"]["db_name"]
decrypt_aes_key = CONFIG["mysql"]["decrypt_aes_key"]
print(f"db_username={db_username}, db_password={db_password}, db_host={db_host}, db_port={db_port}, db_name={db_name}")
db_password = quote_plus(aes_utils.decrypt_aes(decrypt_aes_key, db_password)).replace("%", "%%")
# sync_dsn = mysql+pymysql://${db_username}:${db_password}@${db_host}:${db_port}/${db_name}?charset=utf8mb4
# sync_dsn = f"mysql+pymysql://{db_username}:{db_password}@{db_host}:{db_port}/{db_name}?charset=utf8mb4"
sync_dsn = f"mysql+pymysql://{db_username}:{db_password}@{db_host}:{db_port}/{db_name}?charset=utf8mb4"
config.set_main_option("sqlalchemy.url", sync_dsn)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata


target_metadata = [model.Base.metadata]


# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()

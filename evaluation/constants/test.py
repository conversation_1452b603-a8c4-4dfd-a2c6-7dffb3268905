from enum import Enum

class QuizPlanDelEnum(Enum):
    DELETED = (1, "已删除")
    UNDELETED = (0, "未删除")

    def __init__(self, code, description):
        self.code = code
        self.description = description

    @classmethod
    def get_enum_code(cls, enum_member):
        return enum_member.code

    @staticmethod
    def get_description_by_code(code) -> str:
        for del_status in QuizPlanDelEnum:
            if del_status.code == code:
                return del_status.description
        return "unknown"

# 使用示例
print(QuizPlanDelEnum.get_enum_code(QuizPlanDelEnum.DELETED))  # 输出: 1
from enum import Enum


# system_quiz_plan
# 状态 0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束
class QuizPlanStatusEnum(Enum):
    UNPUBLISHED = (0, "未发布")
    QUEUING = (1, "排队中")
    MODEL_INFERENCE = (2, "模型推理中")
    MODEL_INFERENCE_FAILED = (3, "模型推理失败")
    WAITING_FOR_EVALUATION = (4, "待评测")
    EVALUATING = (5, "评测中")
    RESULT_GENERATING = (6, "结果生成中")
    FINISHED = (7, "任务结束")

    def __init__(self, code, description):
        self.code = code
        self.description = description

    @classmethod
    def get_enum_code(cls, enum_member):
        return enum_member.code

    @staticmethod
    def get_description_by_code(code) -> str:
        for status in QuizPlanStatusEnum:
            if status.code == code:
                return status.description
        return "unknown"


# 评测类型
# AB评测:ab, 人工评测:art, 自评测试:objective
class QuizTypeEnum(Enum):
    AB = ("ab", "AB评测")
    ART = ("art", "人工评测")
    OBJECTIVE = ("objective", "自评测试")

    def __init__(self, code, description):
        self.code = code
        self.description = description

    @classmethod
    def get_enum_code(cls, enum_member):
        return enum_member.code

    @staticmethod
    def get_description_by_code(code) -> str:
        for quiz_type in QuizTypeEnum:
            if quiz_type.code == code:
                return quiz_type.description
        return "unknown"


class QuizPlanDelEnum(Enum):
    DELETED = (1, "已删除")
    UNDELETED = (0, "未删除")

    def __init__(self, code, description):
        self.code = code
        self.description = description

    @classmethod
    def get_enum_code(cls, enum_member):
        return enum_member.code

    @staticmethod
    def get_description_by_code(code) -> str:
        for del_status in QuizPlanDelEnum:
            if del_status.code == code:
                return del_status.description
        return "unknown"


# 问题状态 状态 0: 未发布 1:  可用 2: 不可用 3、删除态
class QuestionStatusEnum(Enum):
    UNPUBLISHED = (0, "未发布")
    AVAILABLE = (1, "可用")
    UNAVAILABLE = (2, "不可用")
    DELETED = (3, "已删除")

    def __init__(self, code, description):
        self.code = code
        self.description = description

    @classmethod
    def get_enum_code(cls, enum_member):
        return enum_member.code

    @staticmethod
    def get_description_by_code(code) -> str:
        for del_status in QuizPlanDelEnum:
            if del_status.code == code:
                return del_status.description
        return "unknown"


# 问题集状态 状态 0: 未发布 1:  可用 2: 不可用 3、删除态
class QuestionSetStatusEnum(Enum):
    UNPUBLISHED = (0, "未发布")
    AVAILABLE = (1, "可用")
    UNAVAILABLE = (2, "不可用")
    DELETED = (3, "已删除")

    def __init__(self, code, description):
        self.code = code
        self.description = description

    @classmethod
    def get_enum_code(cls, enum_member):
        return enum_member.code

    @staticmethod
    def get_description_by_code(code) -> str:
        for del_status in QuizPlanDelEnum:
            if del_status.code == code:
                return del_status.description
        return "unknown"


# 评测任务生成状态
# 任务状态 0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束
class EvaluationTaskStatusEnum(Enum):
    UNPUBLISHED = 0
    QUEUING = 1
    MODEL_INFERENCE = 2
    MODEL_INFERENCE_FAILED = 3
    WAITING_FOR_EVALUATION = 4
    EVALUATING = 5
    RESULT_GENERATING = 6
    FINISHED = 7


# 评测子任务状态
# 任务状态 0: 未开始 1: 生成中  2: 生成完成 3: 生成失败 4:答题进行中 5 答题完成  6 答题过期
class EvaluationSubTaskStatusEnum(Enum):
    NOT_STARTED = 0
    GENERATING = 1
    GENERATED = 2
    GENERATE_FAILED = 3
    ANSWERING = 4
    ANSWERED = 5
    EXPIRED = 6

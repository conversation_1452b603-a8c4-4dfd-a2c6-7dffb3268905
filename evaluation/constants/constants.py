
SURE_DELETE = "确定废弃吗？"
QUESTION_SET_DISCARD_TIP = "任务已选择该题集，若废弃此题集，该任务中题集不受影响。"
QUESTION_DISCARD_TIP = "题集已选择该题目，若废弃此题目，则题目将从该题集中同步去掉。"

# UPDATING（更新中）、 RUNNING（运行中）、OFFLINE_IN_PROGRESS（下线中）、OFFLINE（已下线）、ERROR（异常）
# state 推理服务状态 1-更新中 2-运行中 3-下线中 4-已下线 5-异常
inference_service_status = {
    "UPDATING": 1,
    "RUNNING": 2,
    "OFFLINE_IN_PROGRESS": 3,
    "OFFLINE": 4,
    "ERROR": 5
}

# 评测任务已存在
# EVALUATION_TASK_ALREADY_EXISTS = 10001
# # 分配评测任务失败
# ASSIGN_EVALUATION_TASK_FAILED = 10002

# 系统评测信息不存在
# SYSTEM_EVALUATION_INFO_NOT_EXISTS = ErrorRes(errorCode=10003, errorMessage="系统评测信息不存在")
# # 无评测任务
# USER_EVALUATION_INFO_NOT_EXISTS = ErrorRes(errorCode=10004, errorMessage="无评测任务")
# # 系统答案为空
# SYSTEM_ANSWER_IS_NONE = ErrorRes(errorCode=10005, errorMessage="获取系统答案为空")
#
# # AB测试本轮评测已经统计完成
# ABTEST_RS_ALREADY_EXISTS = ErrorRes(errorCode=10006, errorMessage="本轮评测已经统计完成, 不能重复统计")
#
# # AB测试AB测试本轮评测有人员未完成评测
# ABTEST_RS_USER_UN_FINISH = ErrorRes(errorCode=10007, errorMessage="本轮评测有人员未完成评测, 不能统计")
#
# # 客观题集评测试 未查询到相关评测结果
# OBJECTIVE_NODATA = ErrorRes(errorCode=10008, errorMessage="未查询到客观题集评测结果")
#
# # 系统问题未查到
# SYSTEM_QUESTION_NOT_EXISTS = ErrorRes(errorCode=10009, errorMessage="系统问题未查到")
#
# # 数据为空
# DATA_IS_NONE = ErrorRes(errorCode=10010, errorMessage="数据为空")
#
# # 题集中的问题不存在
# QUESTION_NOT_EXISTS = ErrorRes(errorCode=10011, errorMessage="题集中的问题不存在")
# # 题集不存在
# QUESTION_SET_NOT_EXISTS = ErrorRes(errorCode=10012, errorMessage="题集不存在")
#
# # 他人评测任务不能修改
# OTHER_EVALUATION_TASK_CANNOT_MODIFY = ErrorRes(errorCode=10013, errorMessage="他人评测任务不能修改")
# # 不支持此类型计划发布
# UNSUPPORTED_PLAN_TYPE = ErrorRes(errorCode=10014, errorMessage="不支持此类型计划发布")
# # 他人评测任务不能查看
# OTHER_EVALUATION_TASK_CANNOT_VIEW = ErrorRes(errorCode=10015, errorMessage="他人评测任务不能查看")
# # 任务不能提交
# TASK_CANNOT_SUBMIT = ErrorRes(errorCode=10016, errorMessage="任务不能提交")
#
# # 用户没有权限
# GET_USER_PERMISSION_FAILED = ErrorRes(errorCode=10403, errorMessage="User Permission Is Invalid")
# # 参数有误
# GET_INVALID_PARAMS = ErrorRes(errorCode=10400, errorMessage="Invalid Params")
# # 未找到此评测计划
# GET_NO_EVALUATION_PLAN = ErrorRes(errorCode=10002, errorMessage="未找到此评测计划")
# # 评测计划已经发布
# GET_EVALUATION_PLAN_PUBLISHED = ErrorRes(errorCode=10003, errorMessage="评测计划已经发布")
# # 当前评测任务不能重试
# GET_EVALUATION_TASK_CANNOT_RETRY = ErrorRes(errorCode=10004, errorMessage="当前评测任务不能重试")
# # 评测任务不存在
# GET_EVALUATION_TASK_NOT_EXISTS = ErrorRes(errorCode=10005, errorMessage="评测任务不存在")
# # 获取评测任务失败
# GET_EVALUATION_TASK_FAILED = ErrorRes(errorCode=10006, errorMessage="获取评测任务失败")
# # 任务已经发布不能修改
# GET_EVALUATION_TASK_ALREADY_PUBLISHED = ErrorRes(errorCode=10007, errorMessage="任务已经发布不能修改")
# # 任务已经过期不能修改
# GET_EVALUATION_TASK_EXPIRED = ErrorRes(errorCode=10008, errorMessage="任务已经过期不能修改")
# # 评测计划已经发布
# GET_EVALUATION_PLAN_ALREADY_PUBLISHED = ErrorRes(errorCode=10009, errorMessage="评测计划已经发布")
#
#
#
# # 题集已经废弃
# QUESTION_SET_DISCARDED = ErrorRes(errorCode=10017, errorMessage="题集已经废弃")

from pydantic import BaseModel
from typing import Optional, List


# 评分汇总
class SummaryReportModel(BaseModel):
    # 准确性
    accuracy: Optional[float] = 0.0

    # 专业性
    professionalism: Optional[float] = 0.0

    # 幻觉
    illusion: Optional[float] = 0.0


# 评分报告按照问题汇总
class SummaryReportGroupByQuestionModel(BaseModel):
    # 问题id
    question_id: int

    # 问题
    question: Optional[str] = ""

    # 准确性
    accuracy: Optional[float] = 0.0

    # 专业性
    professionalism: Optional[float] = 0.0

    # 幻觉
    illusion: Optional[float] = 0.0


# 评分报告按照用户汇总
class SummaryReportGroupByUserModel(BaseModel):
    # 用户id
    user_id: str

    # 用户中文名
    user_name: Optional[str] = ""

    # 用户英文名
    user_name_en: Optional[str] = ""

    # 准确性
    accuracy: Optional[float] = 0.0

    # 专业性
    professionalism: Optional[float] = 0.0

    # 幻觉
    illusion: Optional[float] = 0.0

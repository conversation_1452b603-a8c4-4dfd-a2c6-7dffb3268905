from typing import Optional

from pydantic import BaseModel


class CommonRes(BaseModel):
    code: int
    error_msg: Optional[str] = None
    data: Optional[dict] = None


def success(data: dict) -> CommonRes:
    return CommonRes(code=200, data=data)


def fail(error_code: int, error: str) -> CommonRes:
    return CommonRes(code=error_code, error_msg=error)


def common_fail(error: str) -> CommonRes:
    return CommonRes(code=500, error_msg=error)

from pydantic import BaseModel

from typing import Optional, List


class DislikeModel(BaseModel):
    dislike_id: Optional[int] = 0

    # 问题id
    question_id: Optional[int] = 0

    # 问题
    question: Optional[str]

    # 点踩次数
    dislike_count: Optional[int] = 0

    # 用户列表
    user_names: Optional[list[str]]


class DislikeQueryModel(BaseModel):
    evaluation_id: Optional[str] = None

    dislike_id: Optional[int] = None

    # 问题id
    question_id: Optional[int] = None

    user_id: Optional[str] = None

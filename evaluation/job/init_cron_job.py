import logging
import threading

import httpx
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.schedulers.background import BackgroundScheduler

from evaluation.job import cron_job
from evaluation.services import inference_service
import asyncio

logger = logging.getLogger("plaintext")


# 定义异步任务
async def test_async_task():
    await asyncio.sleep(1)
    logger.info("test async task is running.")


# # 创建一个全局事件循环
loop = asyncio.new_event_loop()
asyncio.set_event_loop(loop)


# 包装器函数，用于在后台线程中运行异步函数
def run_asyncio_job(async_func, *args, **kwargs):
    try:
        loop.run_until_complete(async_func(*args, **kwargs))
    except Exception as e:
        logger.error(f"occur error:{str(e)}")


async def run_update_inference_task():
    async with httpx.AsyncClient() as client:
        await client.get("http://localhost:8000/api/v1/inner/job/update_inference_task")



def init_job():
    scheduler = BackgroundScheduler()

    # cron minute='*/5' 每5分钟
    # cron second='*/5' 每5秒
    # interval seconds=10 每10秒
    # # 更新推理服务状态
    # scheduler.add_job(run_update_inference_task, 'interval', seconds=5)
    scheduler.add_job(run_asyncio_job, 'interval', seconds=10, args=[run_update_inference_task],
                      max_instances=1)

    scheduler.start()

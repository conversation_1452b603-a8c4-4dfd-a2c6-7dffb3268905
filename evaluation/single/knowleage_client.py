import http.client

from evaluation.share.util.app_config import CONFIG
from evaluation.share.util.plaintext_log import logger


class KnowledgeClientSingleton:
    _instance = None
    knowledge_client = None
    knowledge_base_url = None
    knowledge_base_port = None
    knowledge_base_path = None
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(KnowledgeClientSingleton, cls).__new__(cls)
            cls._instance.init_knowledge_client()
        return cls._instance

    def init_knowledge_client(self):
        self.knowledge_base_url = CONFIG["api"]["knowledge_base_url"]
        self.knowledge_base_path = CONFIG["api"]["knowledge_chat_path"]
        self.knowledge_base_port = CONFIG["api"]["knowledge_base_port"]
        self.knowledge_client = http.client.HTTPConnection(
            self.knowledge_base_url,
            int(self.knowledge_base_port),
        )
        logger.info(f"knowledge_base_url={self.knowledge_base_url}, knowledge_base_path={self.knowledge_base_path}")


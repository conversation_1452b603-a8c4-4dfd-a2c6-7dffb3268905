class DifyChatMessageModel:
    def __init__(self):
        self.messages = []
        self.current_message_parts = []
        self.conversation_id = None
        self.message_id = None

    def add_message_part(self, message_part):
        self.current_message_parts.append(message_part)

    def compile_message(self):
        # Join all the parts to form the full message and add it to the list of messages
        full_message = ''.join(self.current_message_parts)
        self.messages.append(full_message)
        # Reset the current message parts for the next message
        self.current_message_parts = []

    def set_conversation(self, conversation_id, message_id):
        self.conversation_id = conversation_id
        self.message_id = message_id
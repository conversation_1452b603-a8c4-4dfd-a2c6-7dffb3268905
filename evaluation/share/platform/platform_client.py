import asyncio
import json

from evaluation.share.platform import dify_chat_message_model
from evaluation.share.util.plaintext_log import logger

import httpx
import pytest



class AppPlatFormClient:
    Host: str = "127.0.0.1"
    Port: int = 5001
    API_KEY: str = ""
    BearToken: str

    def __init__(self, host: str, port: int, api_key: str):
        self.Host = host
        self.Port = port
        self.API_KEY = api_key
        self.BearToken = f"Bearer {self.API_KEY}"
        logger.info(f"AppPlatFormClient init: {self.Host}:{self.Port}")

    @staticmethod
    async def __parse_sse_events_v2(response):
        chat_model = dify_chat_message_model.DifyChatMessageModel()
        current_event = None
        try:
            async for line in response.aiter_lines():
                if not line:
                    continue  # Ignore empty lines

                line = line.strip()
                if line.startswith("event:"):
                    current_event = line.split(":", 1)[1].strip()
                    if current_event == "ping":
                        continue
                    continue

                if line.startswith("data:") and current_event:
                    data_content = line.split(":", 1)[1].strip()
                    try:
                        data_json = json.loads(data_content)
                        event_type = data_json.get("event")

                        if event_type == "message":
                            chat_model.add_message_part(data_json.get("answer", ""))
                        elif event_type == "message_end":
                            chat_model.compile_message()
                            chat_model.set_conversation(
                                data_json.get("conversation_id",""),
                                data_json.get("message_id",""),
                            )

                    except json.JSONDecodeError as e:
                        logger.error(f"parse_sse_events error: {e}")
                    except Exception as e:
                        logger.error(
                            f"parse_sse_events processing event '{current_event}': {e}"
                        )
        except httpx.RemoteProtocolError as e:
            logger.error(f"Remote protocol error occurred: {e}")
        except Exception as e:
            logger.error(f"Error: {e}")
        finally:
            return chat_model

    async def __parse_sse_events(self, response):
        data_json = {}
        try:
            async for line in response.aiter_lines():
                if not line:
                    continue
                line = line.strip()
                if line.startswith("data:"):
                    data_content = line.split(":", 1)[1].strip()
                    try:
                        data_json = json.loads(data_content)
                        if data_json.get("event") == "message_end":
                            yield data_json
                        elif data_json.get("event") == "message":
                            yield data_json
                    except json.JSONDecodeError as e:
                        logger.error(f"parse_sse_events error: {e}")
                    except Exception:
                        logger.error(
                            f"parse_sse_events processing event '{data_json.get('event')}"
                        )
        except httpx.RemoteProtocolError as e:
            logger.error(f"Remote protocol error occurred: {e}")
        except Exception as e:
            logger.error(f"parse_sse_events error: {e}")

    # 发送消息
    async def send_message(
        self,
        query: str,
        user_id: str,
        conversation_id: str,
        max_retries: int = 3,
        retry_delay=3,
    ):
        action = "v1/chat-messages"
        url = f"http://{self.Host}:{self.Port}/{action}"
        payload = {
            "inputs": {},
            "query": query,
            "response_mode": "streaming",
            "conversation_id": conversation_id,
            "user": user_id,
        }
        retry_count = 0
        while retry_count < max_retries:
            try:
                timeout_config = httpx.Timeout(100.0, connect=60.0, read=60.0)
                async with httpx.AsyncClient(timeout=timeout_config) as client:
                    async with client.stream(
                        "POST", url, json=payload, headers=self.__common_headers()
                    ) as response:
                        response.raise_for_status()
                        async for message in self.__parse_sse_events(response):
                            yield message
                        break
            except (httpx.RequestError, Exception) as e:
                logger.error(f"Request error occurred: {e}")
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                else:
                    logger.error("Max retry count reached, aborting.")
                    break
            except Exception as e:
                logger.error(f"Error: {e}")
                break

    # 会话列表
    async def list_conversation(
        self, user_id: str, limit: int = 100, pinned: bool = False,last_id: str = None
    ):
        action = "v1/conversations"
        url = f"http://{self.Host}:{self.Port}/{action}"
        params = {"user": user_id, "limit": limit, "pinned": pinned,"last_id": last_id}
        data = {"limit": limit, "has_more": False, "data": []}
        try:
            timeout_config = httpx.Timeout(100.0, connect=60.0, read=60.0)
            async with httpx.AsyncClient(timeout=timeout_config) as client:
                response = await client.get(
                    url, params=params, headers=self.__common_headers()
                )
                response.raise_for_status()
                logger.info(f"list_conversation response: {response.text}")
                return response.json()
        except (httpx.RequestError, Exception) as e:
            logger.error(f"Request error occurred: {e}")
            return data

    # 消息列表
    async def list_message(self, conversation_id: str, user_id: str, limit: int,first_id: str = None):
        action = "v1/messages"
        url = f"http://{self.Host}:{self.Port}/{action}"
        params = {"conversation_id": conversation_id, "user": user_id, "limit": limit, "first_id": first_id}
        try:
            timeout_config = httpx.Timeout(100.0, connect=60.0, read=60.0)
            async with httpx.AsyncClient(timeout=timeout_config) as client:
                response = await client.get(
                    url, params=params, headers=self.__common_headers()
                )
                response.raise_for_status()
                logger.info(f"list_message response: {response.text}")
                return response.json()
        except (httpx.RequestError, Exception) as e:
            logger.error(f"Request error occurred: {e}")
            return None

    def __common_headers(self):
        return {"Authorization": self.BearToken}

    # 重命名会话
    async def rename_conversation(self, user_id: str, conversation_id: str, name: str):
        uri = f"v1/conversations/{conversation_id}/name"
        url = f"http://{self.Host}:{self.Port}/{uri}"
        payload = {
            "name": name,
            "user": user_id,
            "auto_generate": False,
        }
        try:
            timeout_config = httpx.Timeout(100.0, connect=60.0, read=60.0)
            async with httpx.AsyncClient(timeout=timeout_config) as client:
                response = await client.post(
                    url, json=payload, headers=self.__common_headers()
                )
                response.raise_for_status()
                logger.info(f"rename_conversation response: {response.text}")
                return response.json()
        except (httpx.RequestError, Exception) as e:
            logger.error(f"rename_conversation error: {e}")
            return None

    # 删除会话
    async def delete_conversation(self, user_id: str, conversation_id: str):
        uri = f"v1/conversations/{conversation_id}"
        url = f"http://{self.Host}:{self.Port}/{uri}"
        payload = {"user": user_id}
        try:
            timeout_config = httpx.Timeout(100.0, connect=60.0, read=60.0)
            async with httpx.AsyncClient(timeout=timeout_config) as client:
                response = await client.request(
                    "DELETE", url, json=payload, headers=self.__common_headers()
                )
                response.raise_for_status()
                logger.info(f"rename_conversation response: {response.text}")
                return True
        except (httpx.RequestError, Exception) as e:
            logger.error(f"rename_conversation error: {e}")
            return False

    # 停止响应 /chat-messages/:task_id/stop
    async def stop_response(self, user_id: str,task_id: str):
        uri = f"v1/chat-messages/{task_id}/stop"
        url = f"http://{self.Host}:{self.Port}/{uri}"
        payload = {"user": user_id}
        try:
            timeout_config = httpx.Timeout(100.0, connect=60.0, read=60.0)
            async with httpx.AsyncClient(timeout=timeout_config) as client:
                response = await client.post(url, json=payload, headers=self.__common_headers())
                response.raise_for_status()
                logger.info(f"stop_response response: {response.text}")
                return response.json()
        except (httpx.RequestError, Exception) as e:
            logger.error(f"stop_response error: {e}")
            return None



platform_client = AppPlatFormClient("127.0.0.1", 5001, "app-tDdGPl3ppJx8GHhhkhmhDn80")
local_conversation_id = "d27d19d8-3393-47b0-8ca5-cec349f4cc30"
local_user = "23533"
@pytest.mark.asyncio
async def test_list_conversation():
    response = await platform_client.list_conversation(
        user_id=local_user, limit=100, pinned=False
    )
    assert response is not None
    print(response)


@pytest.mark.asyncio
async def test_list_message():
    response = await platform_client.list_message(
        local_conversation_id, local_user, 100
    )
    assert response is not None
    print(response)


@pytest.mark.asyncio
async def test_rename_conversation():
    response = await platform_client.rename_conversation(
        local_user, local_conversation_id, "测试会话"
    )
    assert response is not None
    print(response)


@pytest.mark.asyncio
async def test_delete_conversation():
    response = await platform_client.delete_conversation(
        local_user, local_conversation_id
    )
    assert response is not None
    print(response)

@pytest.mark.asyncio
async def test_stop_response():
    response = await platform_client.stop_response(local_user, "49463ede-5a28-42ed-a17b-6448e0f58232")
    assert response is not None
    print(response)

@pytest.mark.asyncio
async def test_send_message():
    async for message in platform_client.send_message("你好", local_user, local_conversation_id):
        print(message)

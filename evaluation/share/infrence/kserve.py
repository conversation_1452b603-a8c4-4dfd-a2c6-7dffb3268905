import logging
from typing import Dict
import httpx

from evaluation.share.infrence import kserve_help
from evaluation.share.infrence.kserver_resp_model import KServeRespModel
from evaluation.share.util.app_config import CONFIG
from evaluation.share.util.json_utils import JsonUtils

logger = logging.getLogger("plaintext")

OpenAIBasePath = ["/v1/completions", "/v1/chat/completions"]

class KServeClient:
    def __init__(self,
                 path: str,
                 header: Dict[str, str],
                 model_name: str,
                 model_type: int,
                 data: list[str]):
        self.path = path
        self.header = header
        self.data = data
        self.model_name = model_name
        self.model_type = model_type
        if any(base_path in path for base_path in OpenAIBasePath):
            self.api_type = 'openai'
            # authorization: Bearer test-openai-base.hanhai-inference.svc.cluster.local
            if 'Custom-Host' in self.header:
                host = self.header.pop('Custom-Host')
                openai_header = {'Authorization': f'Bearer {host}'}
                self.header.update(openai_header)
        else:
            self.api_type = 'kserve'

    async def generate(self) -> list[KServeRespModel]:
        url = f"{CONFIG['inference_eval']['platform']}{self.path}"
        header_ = self.header
        logger.info(f"chat with model url:{url},head:{header_},data:{self.data}")

        result = []
        index = 0
        for c in self.data:
            req_input = kserve_help.gen_input(c, self.model_type, self.model_name.lower(), self.api_type)
            logger.info(f"begin chat model_type：{self.model_type},input:{JsonUtils.to_json(req_input)}")

            try:
                async with httpx.AsyncClient() as client:
                    response = await client.post(url, headers=header_, json=req_input, timeout=300)
                if response.status_code != 200:
                    logger.error(f"failed chat with model, input: {JsonUtils.to_json(req_input)}, "
                                 f"status_code: {response.status_code}, text: {response.text}")
                    raise Exception(f"chat with model occur error：{response.text}")

                response_json = response.json()
                answer_ = None
                if "openai" in self.api_type:
                    if "choices" not in response_json or len(response_json["choices"]) == 0:
                        raise Exception(f"chat with model occur error: {response_json}")
                    if self.model_type == 1:
                        answer_ = response_json["choices"][0]["text"]
                    else:
                        answer_ = response_json["choices"][0]["message"]["content"]
                else:
                    if self.model_type == 1:
                        answer_ = response_json["outputs"][0]["data"][0]
                    else:
                        answer_ = response_json["outputs"][0]["data"][0]["content"]
                track_id_ = response_json["id"]
                result.append(KServeRespModel(question=c,
                                              answer=answer_,
                                              track_id=track_id_,
                                              round_index=index))
                index += 1
            except httpx.TimeoutException as e:
                logger.error(f"chat with model occur timeout error:{str(e)}")
                raise Exception(f"chat with model occur timeout error:{str(e)}")
            except Exception as e:
                logger.error(f"chat with model occur error:{str(e)}")
                raise e
        return result



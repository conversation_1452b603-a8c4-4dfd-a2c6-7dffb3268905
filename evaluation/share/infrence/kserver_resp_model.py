from dataclasses import dataclass
from typing import Optional, Any
from pydantic import BaseModel


class KServeRespModel(BaseModel):
    question: Optional[str] = None
    answer: Optional[str] = None
    sys_type: Optional[str] = 'art'
    track_id: Optional[str] = None
    reference_str: Optional[str] = None
    round_index: Optional[int] = 0

    def __init__(__pydantic_self__, **data: Any) -> None:
        super().__init__(**data)

    def to_dict(self):
        # 将对象转换为字典
        return {
            'question': self.question,
            'answer': self.answer,
            'sys_type': self.sys_type,
            'track_id': self.track_id,
            'reference_str': self.reference_str,
            'round_index': self.round_index
        }


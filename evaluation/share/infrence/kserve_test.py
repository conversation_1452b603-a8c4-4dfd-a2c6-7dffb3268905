import asyncio
import json
import logging
from typing import List

import pytest

from evaluation.share.infrence import kserve
from evaluation.share.infrence.kserve import KServeClient
from evaluation.share.infrence.kserver_resp_model import KServeRespModel
from evaluation.share.util.json_utils import JsonUtils

logger = logging.getLogger("plaintext")

# 示例数据
data = ["你好", "你是谁", "你会什么"]
header = {
    "Content-Type": "application/json",
    "Custom-Host": "f442aab0-38c2-456d-919d-c2bec2d1665b-1728377235964.hanhai-inference.svc.cluster.local",
}
path = "/v2/models/qwen1.5_7b-chat/infer"  # 替换your_model为实际模型名

# 创建KServeClient实例
client = KServeClient(path=path, header=header, data=data)


async def run_kserve_client():
    try:
        # 调用generate方法
        responses = await client.generate()

        all_resp = []
        # 打印响应
        for p in responses:
            all_resp.append(p)
            # print(json.dumps(p.dict(), ensure_ascii=False))

        # to json
        print(JsonUtils.to_json(all_resp))
    except Exception as e:
        logger.error(f"An error occurred: {e}")


# 运行异步函数
if __name__ == "__main__":
    asyncio.run(run_kserve_client())

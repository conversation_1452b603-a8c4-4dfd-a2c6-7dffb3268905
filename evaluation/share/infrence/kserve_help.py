# 模型类型1-base2-chat3-embedding4-reranker
def gen_input(content: str, model_type: int, model_name: str, api_type: str):
    if api_type == 'openai':
        if model_type == 1:
            return gen_base_input_openai(content, model_name)
        else:
            return gen_chat_input_openai(content, model_name)
    else:
        if model_type == 1:
            return gen_base_input(content)
        if model_type == 2:
            return gen_chat_input(content)
        if model_type == 3:
            return gen_embedding_input(content)
        if model_type == 4:
            return gen_reranker_input(content)

    return gen_base_input(content)


def gen_base_input(content: str):
    req_data = {
        "inputs": [
            {
                "name": "input0",
                "shape": [1],
                "datatype": "BYTES",
                "data": [content]
            }
        ],
        "parameters": {
            "output_scores": False,
            "temperature": 1,
            "max_new_tokens": 256,
            "do_sample": True,
            "top_p": 1,
            "top_k": 50,
            "repetition_penalty": 1.1,
            "stream": False
        }
    }
    return req_data

def gen_base_input_openai(content: str, model_name: str):
    req_data = {
        "model": model_name,
        "prompt": [content],
        "temperature": 1,
        "max_tokens": 256,
        "do_sample": True,
        "top_p": 1,
        "top_k": 50,
        "repetition_penalty": 1.1,
        "stream": False
    }
    return req_data


def gen_chat_input(content: str):
    req_data = {
        "inputs": [
            {
                "name": "input0",
                "shape": [1],
                "datatype": "BYTES",
                "data": [{"role": "user", "content": content}]
            }
        ],
        "parameters": {
            "output_scores": False,
            "temperature": 1,
            "max_new_tokens": 256,
            "do_sample": True,
            "top_p": 1,
            "top_k": 50,
            "repetition_penalty": 1.1,
            "stream": False
        }
    }
    return req_data

def gen_chat_input_openai(content: str, model_name: str):
    req_data = {
        "model": model_name,
        "messages": [{"role": "user", "content": content}],
        "temperature": 1,
        "max_tokens": 256,
        "do_sample": True,
        "top_p": 1,
        "top_k": 50,
        "repetition_penalty": 1.1,
        "stream": False
    }
    return req_data


def gen_embedding_input(content: str):
    req_data = {
        "inputs": [
            {
                "name": "input1",
                "shape": [1],
                "datatype": "BYTES",
                "data": [content]
            }
        ],
        "parameters": {
            "max_new_tokens": 256
        }
    }
    return req_data


def gen_reranker_input(content: str):
    req_data = {
        "inputs": [
            {
                "name": "input1",
                "shape": [1],
                "datatype": "BYTES",
                "data": [[content]]
            }
        ],
        "parameters": {
            "max_new_tokens": 256
        }
    }
    return req_data

from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import select
from evaluation.share.orm.model import SystemQuizPlanSnapshot


async def add_system_quiz_plan_snapshot(db: AsyncSession, model: SystemQuizPlanSnapshot):
    try:
        db.add(model)
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise e
    return model


# 获取评测计划快照
async def get_system_quiz_plan_snapshot(db: AsyncSession, plan_id: Optional[str],
                                        question_set_identifier: Optional[str],
                                        user_set_identifier: Optional[str]) -> list[SystemQuizPlanSnapshot]:
    query = select(SystemQuizPlanSnapshot)
    if plan_id:
        query = query.where(SystemQuizPlanSnapshot.evaluation_id == plan_id)
    if question_set_identifier:
        query = query.where(SystemQuizPlanSnapshot.question_set_identifier == question_set_identifier)
    if user_set_identifier:
        query = query.where(SystemQuizPlanSnapshot.user_set_identifier == user_set_identifier)

    execute = await db.execute(query)
    return execute.scalars().all()


async def get_system_quiz_plan_snapshot_by_plan_ids(db: AsyncSession, plan_ids: list[str]) -> list[
    SystemQuizPlanSnapshot]:
    query = select(SystemQuizPlanSnapshot).where(SystemQuizPlanSnapshot.evaluation_id.in_(plan_ids))
    execute = await db.execute(query)
    return execute.scalars().all()

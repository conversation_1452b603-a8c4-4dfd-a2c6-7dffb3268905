import logging
from typing import Optional

from certifi import where
from sqlalchemy import text, func, desc, bindparam, case
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import select, update

from evaluation.share.orm.model import UserQuizTasksCriteriaInfo, UserQuizTasksInfo, UserQuizTasks, \
    SystemQuestionDislikeUser, SystemQuestionDislike
from evaluation.vo.criteria_report_vo import SummaryReportGroupByQuestionModel, SummaryReportGroupByUserModel
from evaluation.vo.dislike_model import DislikeModel, DislikeQueryModel

logger = logging.getLogger("plaintext")


async def list_done_dislike(db: AsyncSession, evaluation_id: str, question_id: int) -> SystemQuestionDislike:
    query = select(SystemQuestionDislike).where(SystemQuestionDislike.evaluation_id == evaluation_id).where(
        SystemQuestionDislike.question_id == question_id)
    query_exec = await db.execute(query)
    return query_exec.scalars().first()


# 已点赞记录
async def list_done_dislike_user(db: AsyncSession, evaluation_id: str, question_id: int,
                                 user_id: str) -> SystemQuestionDislikeUser:
    query = select(SystemQuestionDislikeUser).where(SystemQuestionDislikeUser.evaluation_id == evaluation_id).where(
        SystemQuestionDislikeUser.question_id == question_id).where(SystemQuestionDislikeUser.user_id == user_id)
    query_exec = await db.execute(query)
    return query_exec.scalars().first()


# 查询已点踩的用户列表
async def list_dislike_user_by_condition(db: AsyncSession, condition: DislikeQueryModel) -> list[
    SystemQuestionDislikeUser]:
    query = select(SystemQuestionDislikeUser)
    if condition.evaluation_id:
        query = query.where(SystemQuestionDislikeUser.evaluation_id == condition.evaluation_id)
    if condition.dislike_id:
        query = query.where(SystemQuestionDislikeUser.dislike_id == condition.dislike_id)
    if condition.question_id:
        query = query.where(SystemQuestionDislikeUser.question_id == condition.question_id)
    if condition.user_id:
        query = query.where(SystemQuestionDislikeUser.user_id == condition.user_id)

    query_exec = await db.execute(query)
    return query_exec.scalars().all()


# 已点赞的用户列表
async def list_dislike_users(db: AsyncSession, dislike_id: int, question_id: Optional[int]) -> \
        list[
            SystemQuestionDislikeUser]:
    query = select(SystemQuestionDislikeUser).where(SystemQuestionDislikeUser.dislike_id == dislike_id)
    if question_id:
        query = query.where(SystemQuestionDislikeUser.question_id == question_id)
    query_exec = await db.execute(query)
    return query_exec.scalars().all()


async def count_dislike(db: AsyncSession, plan_id: str) -> int:
    raw_sql = """
    SELECT count(1) FROM system_question_dislike 
    WHERE evaluation_id =:plan_id
     """

    query_exec = await db.execute(text(raw_sql),
                                  {"plan_id": plan_id})
    return query_exec.scalars().first()


async def list_dislike_by_page(db: AsyncSession, plan_id: str, page: int, page_size: int) -> list[
    DislikeModel]:
    raw_sql = """
    SELECT t1.id as dislike_id,t1.question_id as question_id,t2.question as question,t1.dislike_count as dislike_count 
    FROM system_question_dislike t1 
    LEFT JOIN system_quiz_questions_v2 t2  on t2.id=t1.question_id
    WHERE t1.evaluation_id = :plan_id
    LIMIT :offset , :page_size
     """

    # Execute the raw SQL
    sql = text(raw_sql)
    query_exec = await db.execute(sql,
                                  {"plan_id": plan_id, "offset": (page - 1) * page_size, "page_size": page_size})

    # Execute the raw SQL
    results = [
        DislikeModel(
            dislike_id=row.dislike_id,
            question_id=row.question_id,
            question=row.question,
            dislike_count=row.dislike_count
        )
        for row in query_exec
    ]
    return results


# 保存点踩
async def add_system_question_dislike(db: AsyncSession, model: SystemQuestionDislike) -> SystemQuestionDislike:
    try:
        db.add(model)
        await db.commit()
        await db.refresh(model)
        return model
    except SQLAlchemyError as e:
        logger.error(f"db Error: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"db Error: {str(e)}")
        raise e


# 保存点踩明细
async def add_system_question_dislike_user(db: AsyncSession,
                                           model: SystemQuestionDislikeUser) -> SystemQuestionDislikeUser:
    try:
        db.add(model)
        await db.commit()
        await db.refresh(model)
        return model
    except SQLAlchemyError as e:
        logger.error(f"db Error: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"db Error: {str(e)}")
        raise e


async def update_dislike_count(db: AsyncSession, dislike_id: int, dis_count: int) -> SystemQuestionDislike:
    try:
        query = select(SystemQuestionDislike).filter_by(id=dislike_id)
        query_exec = await db.execute(query)
        dislike = query_exec.scalars().first()

        if not dislike:
            raise ValueError("Dislike record not found")

        dislike.dislike_count = dis_count
        db.add(dislike)
        await db.commit()
        await db.refresh(dislike)
        return dislike
    except SQLAlchemyError as e:
        logger.error(f"db Error: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"db Error: {str(e)}")
        raise e

import logging
from typing import List

from certifi import where
from sqlalchemy import text, func, desc, bindparam, case, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import select, update

from evaluation.share.orm.model import UserQuizTasksCriteriaInfo, UserQuizTasksInfo, UserQuizTasks
from evaluation.vo.criteria_report_vo import SummaryReportGroupByQuestionModel, SummaryReportGroupByUserModel

logger = logging.getLogger("plaintext")


# 评分明细
async def list_user_quiz_tasks_criteria_info(db: AsyncSession, user_task_info_task_id: str) -> list[
    UserQuizTasksCriteriaInfo]:
    query = select(UserQuizTasksCriteriaInfo).filter_by(task_id=user_task_info_task_id)
    query_exec = await db.execute(query)
    return query_exec.scalars().all()


# 评分明细
async def list_user_quiz_tasks_criteria_info_by_plan_id(db: AsyncSession, plan_id: str) -> list[
    UserQuizTasksCriteriaInfo]:
    query = select(UserQuizTasksCriteriaInfo).join(
        UserQuizTasksInfo,
        UserQuizTasksInfo.task_id == UserQuizTasksCriteriaInfo.task_id).join(
        UserQuizTasks,
        UserQuizTasks.task_id == UserQuizTasksInfo.parent_task_id).where(UserQuizTasks.evaluation_id == plan_id)
    query_exec = await db.execute(query)
    return query_exec.scalars().all()


# 按照问题分组
'''
# SELECT 
# 	t1.question_id,
# 	SUM(CASE WHEN t1.rating_key = 'a' AND t1.rating_int_value !=-1  THEN t1.rating_int_value ELSE 0 END) AS v1,
# 	SUM(CASE WHEN t1.rating_key = 'c' AND t1.rating_int_value !=-1  THEN t1.rating_int_value ELSE 0 END) AS v2,
# 	SUM(CASE WHEN t1.rating_key = 'b' AND t1.rating_int_value !=-1 THEN t1.rating_int_value ELSE 0 END) AS v3
# 	
#  from user_quiz_tasks_criteria_info t1
# LEFT JOIN user_quiz_tasks_info t2 ON t2.task_id=t1.task_id
# WHERE t2.evaluation_id="63b8b856-0cbd-440f-ad23-35e4e275f562" 
# AND t1.rating_key = 'c'
# GROUP BY question_id

 (users_table.c.name == 'wendy', 'W'),
'''


async def count_group_by_question(db: AsyncSession, plan_id: str) -> list[SummaryReportGroupByQuestionModel]:
    raw_sql = """
       SELECT
	    count( 1 ) 
        FROM
            (
            SELECT
                t1.question_id 
            FROM
                user_quiz_tasks_criteria_info t1
                LEFT JOIN user_quiz_tasks_info t2 ON t2.task_id = t1.task_id 
            WHERE
                t2.evaluation_id =:plan_id 
            GROUP BY
                question_id 
        ) t
        """
    query_exec = await db.execute(text(raw_sql), {"plan_id": plan_id})
    return query_exec.scalars().first()


async def report_group_by_question(db: AsyncSession, plan_id: str, page: int, page_size: int) -> list[
    SummaryReportGroupByQuestionModel]:
    # Construct the raw SQL query
    raw_sql = """
     SELECT 
         t1.question_id,
         SUM(CASE 
             WHEN t1.rating_key = 'a' AND t1.rating_int_value != -1 THEN t1.rating_int_value 
             ELSE 0 
         END) AS accuracy,
         SUM(CASE 
             WHEN t1.rating_key = 'c' AND t1.rating_int_value != -1 THEN t1.rating_int_value 
             ELSE 0 
         END) AS professionalism,
         SUM(CASE 
             WHEN t1.rating_key = 'b' AND t1.rating_int_value != -1 THEN t1.rating_int_value 
             ELSE 0 
         END) AS illusion
     FROM user_quiz_tasks_criteria_info t1
     LEFT JOIN user_quiz_tasks_info t2 ON t2.task_id = t1.task_id
     WHERE t2.evaluation_id = :plan_id 
     GROUP BY t1.question_id
     order by t1.question_id
     LIMIT :offset , :page_size
     """

    query_exec = await db.execute(text(raw_sql),
                                  {"plan_id": plan_id, "offset": (page - 1) * page_size, "page_size": page_size})

    # Execute the raw SQL
    results = [
        SummaryReportGroupByQuestionModel(
            question_id=row.question_id,
            accuracy=row.accuracy,
            professionalism=row.professionalism,
            illusion=row.illusion
        )
        for row in query_exec
    ]

    return results


async def count_group_by_user(db: AsyncSession, plan_id: str) -> int:
    raw_sql = """
    SELECT
	count( 1 ) 
    FROM
        (
        SELECT
            t1.user_id AS user_id 
        FROM
            user_quiz_tasks_info t1
            LEFT JOIN user_quiz_tasks_criteria_info t2 ON t2.task_id = t1.task_id 
        WHERE
            t1.evaluation_id = :plan_id
        GROUP BY
            user_id 
        ) t
     """

    query_exec = await db.execute(text(raw_sql),
                                  {"plan_id": plan_id})
    return query_exec.scalars().first()


async def report_group_by_user(db: AsyncSession, plan_id: str, page: int, page_size: int) -> list[
    SummaryReportGroupByUserModel]:
    raw_sql = """
    SELECT 
	t1.user_id as user_id,
	SUM(CASE WHEN t2.rating_key = 'a' AND t2.rating_int_value !=-1  THEN t2.rating_int_value ELSE 0 END) AS accuracy,
	SUM(CASE WHEN t2.rating_key = 'c' AND t2.rating_int_value !=-1  THEN t2.rating_int_value ELSE 0 END) AS professionalism,
	SUM(CASE WHEN t2.rating_key = 'b' AND t2.rating_int_value !=-1 THEN t2.rating_int_value ELSE 0 END) AS illusion
    from user_quiz_tasks_info t1
    LEFT JOIN user_quiz_tasks_criteria_info t2 ON t2.task_id=t1.task_id
    WHERE t1.evaluation_id= :plan_id 
    GROUP BY user_id
    order by t1.user_id
    LIMIT :offset , :page_size
     """

    query_exec = await db.execute(text(raw_sql),
                                  {"plan_id": plan_id, "offset": (page - 1) * page_size, "page_size": page_size})

    # Execute the raw SQL
    results = [
        SummaryReportGroupByUserModel(
            user_id=row.user_id,
            accuracy=row.accuracy,
            professionalism=row.professionalism,
            illusion=row.illusion
        )
        for row in query_exec
    ]
    return results


async def delete_tasks_criteria_info_by_task_info_id(db: AsyncSession, task_info_ids: List[str]):
    query = delete(UserQuizTasksCriteriaInfo).where(UserQuizTasksCriteriaInfo.task_id.in_(task_info_ids))
    try:
        await db.execute(query)
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise e

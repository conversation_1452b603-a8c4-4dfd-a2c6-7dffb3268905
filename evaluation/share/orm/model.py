import json
from datetime import datetime

from sqlalchemy.orm import class_mapper

from sqlalchemy import (
    Column,
    DateTime,
    FetchedValue,
    Integer,
    String,
    TIMESTAMP,
    text,
    TEXT,
    func,
    Boolean,
    VARCHAR,
    JSON,
    Index,
    BigInteger,
    inspect, Text,
)
from sqlalchemy.dialects.mysql import TINYTEXT
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.schema import UniqueConstraint
from evaluation.api.evaluate_schema import EvaluationTaskType

Base = declarative_base()


# -- 评审任务表
class SystemQuizTasks(Base):
    __tablename__ = "system_quiz_tasks"

    id = Column(Integer, primary_key=True, autoincrement=True)
    evaluation_id = Column(
        String(255), nullable=False, unique=True, comment="唯一的评审ID"
    )
    system_version = Column(String(255), nullable=False, comment="系统版本号")
    system_version_info = Column(String(255), nullable=False, comment="系统版本信息")
    criteria_set_identifier = Column(
        String(255), nullable=False, comment="评审标准集合版本号"
    )
    question_set_identifier = Column(
        String(255), nullable=False, comment="问题集合版本号"
    )
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )


class SystemQuizCriterias(Base):
    __tablename__ = "system_quiz_criterias"

    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(255), nullable=False, comment="评分标准标题")
    value = Column(TEXT, nullable=False, comment="评分标准描述")
    rating_key = Column(String(100), nullable=False, comment="评审标准KEY")
    rating_type = Column(String(256), server_default=text("0"), comment="评分标准类型")
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )


class SystemQuizCriteriaSetRelations(Base):
    __tablename__ = "system_quiz_criteria_set_relations"

    id = Column(Integer, primary_key=True, autoincrement=True)
    criteria_id = Column(Integer, nullable=False, comment="评审标准ID")
    criteria_set_identifier = Column(
        String(255), nullable=False, comment="评审标准集合版本号"
    )
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )


class SystemQuizCriteriaSets(Base):
    __tablename__ = "system_quiz_criteria_sets"

    id = Column(Integer, primary_key=True, autoincrement=True)
    criteria_set_identifier = Column(
        String(255), nullable=False, unique=True, comment="评审标准集合版本号"
    )
    quiz_type = Column(
        String(100),
        nullable=False,
        server_default="art",
        comment="评测类型  AB评测:ab, 人工评测:art, 自评测试:objective",
    )
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )


class SystemQuizQuestionSets(Base):
    __tablename__ = "system_quiz_question_sets"

    id = Column(Integer, primary_key=True, autoincrement=True)
    question_set_identifier = Column(
        String(255), nullable=False, comment="问题集合版本号"
    )
    name = Column(String(255), nullable=False, comment="问题集合名称")
    desc = Column(TEXT, nullable=False, comment="问题集合描述")
    questions_number = Column(Integer, nullable=False, comment="问题数量", default=0)
    version = Column(
        Integer, nullable=False, comment="版本号", server_default=text("0")
    )
    state = Column(
        Integer,
        nullable=False,
        server_default=text("1"),
        comment="状态 0: 未发布 1:  可用 2: 不可用 3、删除态",
    )
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间",
    )
    created_user_id = Column(String(100), nullable=True, comment="创建人")
    updated_user_id = Column(String(100), nullable=True, comment="更新人")


class SystemQuizQuestionSetRelations(Base):
    __tablename__ = "system_quiz_question_set_relations"

    id = Column(Integer, primary_key=True, autoincrement=True)
    question_set_identifier = Column(
        String(255), nullable=False, comment="问题集合版本号"
    )

    question_id = Column(Integer, nullable=False, comment="问题ID")
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间",
    )


class SystemQuizQuestionSetRelationsV2(Base):
    __tablename__ = "system_quiz_question_set_relations_v2"

    id = Column(Integer, primary_key=True, autoincrement=True)
    question_set_identifier = Column(
        String(255), nullable=False, comment="问题集合版本号"
    )

    question_id = Column(String(50), nullable=False, comment="问题ID")
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间",
    )


# 题型表
class SystemQuizQuestionTypes(Base):
    __tablename__ = "system_quiz_question_types"

    id = Column(Integer, primary_key=True, autoincrement=True)
    question_type = Column(
        String(100),
        nullable=False,
        server_default="qa",
        comment="题集类型 问答: qa"
                "单选题: single_select"
                "多选题: multiple_select"
                "填空题: fill_in_the_blank"
                "判断题: true_or_false"
                "多轮对话: multi_round_qa",
    )
    question_type_desc = Column(
        String(100), nullable=False, server_default="", comment="题型描述"
    )
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间",
    )


class SystemQuizQuestions(Base):
    __tablename__ = "system_quiz_questions"

    id = Column(Integer, primary_key=True, nullable=False, comment="问题ID")
    question = Column(String(512), nullable=False, comment="问题")
    question_desc = Column(TEXT, nullable=False, comment="问题描述")
    question_type = Column(
        String(100),
        nullable=False,
        server_default="qa",
        comment="题集类型 问答: qa"
                "单选题: single_select"
                "多选题: multiple_select"
                "填空题: fill_in_the_blank"
                "判断题: true_or_false",
    )
    llm_q_list = Column(TEXT, nullable=False, comment="填空题问题集")
    options = Column(TEXT, nullable=True, comment="选择题选项", default="")
    reference_answer = Column(TEXT, nullable=False, comment="参考答案")
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间",
    )


class SystemQuizQuestions_V2(Base):
    __tablename__ = "system_quiz_questions_v2"

    id = Column(Integer, primary_key=True, nullable=False, comment="问题ID")
    question_id = Column(String(50), nullable=False)
    question = Column(String(512), nullable=False, comment="问题")
    question_desc = Column(TEXT, nullable=False, comment="问题描述")
    question_type_id = Column(Integer, nullable=False, comment="题型ID")
    question_type = Column(
        String(100),
        nullable=False,
        server_default="qa",
        comment="题集类型 问答: qa"
                "单选题: single_select"
                "多选题: multiple_select"
                "填空题: fill_in_the_blank"
                "判断题: true_or_false"
                "多轮对话: multi_round_qa",
    )
    difficulty = Column(Integer, nullable=True, comment="难度等级", default=0)
    category = Column(String(100), nullable=True, comment="题目分类", default="")
    explanation = Column(TEXT, nullable=True, comment="题目解释", default="")
    version = Column(
        Integer, nullable=False, comment="版本号", server_default=text("0")
    )
    created_at = Column(TIMESTAMP, default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间",
    )
    state = Column(
        Integer,
        nullable=False,
        server_default=text("0"),
        comment="状态 0: 未发布 1:  可用 2: 不可用 3、删除态",
    )
    details = Column(JSON, nullable=True, comment="题目详情")
    is_deleted = Column(
        Boolean,
        nullable=False,
        server_default=text("0"),
        comment="是否删除 0: 未删除 1: 已删除",
    )
    created_user_id = Column(String(100), nullable=True, comment="创建人")
    updated_user_id = Column(String(100), nullable=True, comment="更新人")

    __table_args__ = (
        Index("ix_question_id_version", "question_id", "version", unique=True),
    )

    def is_multi_round_qa(self):
        return self.question_type == EvaluationTaskType.MULTI_ROUND_QA.value


class UserQuizInfos(Base):
    __tablename__ = "user_quiz_infos"

    id = Column(Integer, primary_key=True, autoincrement=True)
    evaluation_id = Column(
        String(255), nullable=False, unique=True, comment="唯一的评审ID"
    )
    user_id = Column(String(255), nullable=False, comment="用户ID")
    system_version = Column(String(255), nullable=False, comment="系统版本号")
    system_version_info = Column(String(255), nullable=False, comment="系统版本信息")
    criteria_set_identifier = Column(
        String(255), nullable=False, comment="评审标准集合版本号"
    )
    question_set_identifier = Column(
        String(255), nullable=False, comment="问题集合版本号"
    )
    evaluation_state = Column(
        Integer, nullable=False, server_default=text("0"), comment="评审任务状态"
    )
    task_count = Column(
        Integer, nullable=False, server_default=text("0"), comment="评审任务总数"
    )
    completed_count = Column(
        Integer, nullable=False, server_default=text("0"), comment="已完成任务数量"
    )
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )


class UserQuizTasks(Base):
    __tablename__ = "user_quiz_tasks"

    id = Column(Integer, primary_key=True, autoincrement=True)
    evaluation_id = Column(String(100), nullable=True, comment="唯一的评审ID")
    task_id = Column(String(100), nullable=True, comment="任务ID", index=True)
    user_id = Column(String(100), nullable=True, comment="用户ID")
    task_name = Column(String(100), nullable=True, comment="任务名称")
    task_desc = Column(TEXT, nullable=True, comment="任务描述")
    system_version = Column(String(255), nullable=True, comment="系统版本号")
    criteria_set_identifier = Column(
        String(255), nullable=True, comment="评审标准集合版本号"
    )
    qa_round_criteria_set_identifier = Column(
        String(255), nullable=True, comment="QA轮次标准集合版本号"
    )
    dim_set_identifier = Column(
        String(255), nullable=True, comment="评审维度集合版本号"
    )
    qa_round_dim_set_identifier = Column(
        String(255), nullable=True, comment="QA轮次维度集合版本号"
    )
    system_version_info = Column(String(255), nullable=False, comment="系统版本信息")
    question_set_identifier = Column(
        String(255), nullable=True, comment="问题集合版本号"
    )
    evaluation_state = Column(
        Integer, nullable=True, server_default=text("0"), comment="评审任务状态"
    )
    evaluation_type = Column(
        String(100),
        nullable=False,
        server_default="art",
        comment="评测类型  ab测试: ab、人工评测: art",
    )
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )
    task_expire = Column(TIMESTAMP, nullable=True, comment="任务过期时间")
    task_state = Column(
        Integer,
        nullable=True,
        server_default=text("0"),
        index=True,
        comment="1: 未开始  2: 评测中 3: 评测完成",
    )
    # 任务数量
    task_num = Column(
        Integer, nullable=True, server_default=text("0"), comment="任务数量"
    )
    # 已获取答案的任务数量
    task_answer_num = Column(
        Integer,
        nullable=True,
        server_default=text("0"),
        comment="已获取答案的任务数量",
    )
    # 已评测任务的数量
    task_evaluation_num = Column(
        Integer, nullable=True, server_default=text("0"), comment="已评测任务的数量"
    )
    eval_product = Column(TEXT, nullable=True, comment="评测产品")
    plan_id = Column(Integer, nullable=True, default=0, comment="计划ID")
    created_user = Column(String(100), nullable=True, default=0, comment="创建人")
    created_user_name = Column(String(255), nullable=True, comment="创建人名称")


# -- 用户评审任务明细表
class UserQuizTasksInfo(Base):
    __tablename__ = "user_quiz_tasks_info"

    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(String(100), nullable=True, comment="任务ID", index=True)
    parent_task_id = Column(String(100), nullable=True, comment="父任务ID")
    user_id = Column(TINYTEXT, nullable=True, comment="用户ID")
    question_id = Column(Integer, nullable=True, comment="问题ID")
    evaluation_id = Column(String(100), nullable=True, comment="评审ID")
    system_answer = Column(TEXT, nullable=True, comment="系统答案")
    rating = Column(Integer, nullable=True, default=0, comment="评分")
    system_type = Column(
        String(100),
        nullable=False,
        server_default="art",
        comment="评测方式  a系统: a、b系统: b、人工评测: art",
    )
    trace_id = Column(VARCHAR(255), nullable=True)
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )
    feedback = Column(TEXT, nullable=True, default="", comment="反馈")
    source = Column(TEXT, nullable=True, comment="召回数据")
    task_state = Column(
        Integer,
        nullable=True,
        server_default=text("0"),
        comment="1: 未开始 2: 评分中  3: 评分完成",
    )
    system_url = Column(String(255), nullable=True, comment="系统url")
    plan_id = Column(Integer, nullable=True, default=0, comment="计划ID")
    question = Column(String(255), nullable=True, comment="问题")
    created_user = Column(String(255), nullable=True, default=0, comment="创建人")
    created_user_name = Column(String(255), nullable=True, comment="创建人名称")


# -- 用户评审任务标准明细表
class UserQuizTasksCriteriaInfo(Base):
    __tablename__ = "user_quiz_tasks_criteria_info"

    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(String(100), nullable=False, comment="任务ID")
    user_id = Column(String(100), nullable=False, comment="用户ID")
    question_id = Column(Integer, nullable=False, comment="问题ID")
    title = Column(String(100), nullable=False, comment="标题")
    rating_key = Column(String(100), nullable=False, comment="评审标准KEY")
    rating_type = Column(String(256), server_default=text("0"), comment="评分标准类型")
    rating_int_value = Column(
        Integer, nullable=False, default=-1, comment="评分"
    )  # 默认-1, 未评分
    rating_string_value = Column(
        String(255), nullable=False, default="", comment="评分"
    )
    system_type = Column(
        String(100),
        nullable=False,
        server_default="art",
        comment="评测方式  a系统: a、b系统: b、人工评测: art",
    )
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )
    feedback = Column(TEXT, nullable=True, default="", comment="反馈")


# 评测计划表
class SystemQuizPlans(Base):
    __tablename__ = "system_quiz_plan"

    id = Column(Integer, primary_key=True, autoincrement=True)
    plan_id = Column(String(255), nullable=False, unique=True, comment="唯一的计划ID")
    plan_name = Column(String(255), nullable=False, comment="计划名称")
    quiz_type = Column(
        String(100),
        nullable=False,
        server_default="art",
        comment="评测类型  AB评测:ab, 人工评测:art, 自评测试:objective ",
    )
    desc = Column(TEXT, nullable=False, comment="计划描述")
    eval_product = Column(TEXT, nullable=True, comment="评测产品")
    dim_set_id = Column(Integer, nullable=False, comment="评测维度集合版本ID")
    qa_round_dim_set_id = Column(Integer, nullable=False, comment="QA轮次维度集合版本ID", server_default=text("0"))
    question_set_id = Column(String(100), nullable=False, comment="问题集合版本ID")
    user_set_id = Column(String(100), nullable=False, comment="评测人员集合版本ID")
    created_user = Column(String(100), nullable=False, comment="创建人")
    updated_user = Column(String(100), nullable=False, comment="更新人")
    trigger = Column(String(150), nullable=True, comment="触发条件")
    expire = Column(BigInteger, nullable=True, comment="过期时间")
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )
    state = Column(
        Integer,
        nullable=False,
        server_default=text("0"),
        comment="0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束",
    )
    model_id = Column(Integer, nullable=True, default=0, comment="模型ID")
    model_name = Column(String(1000), nullable=True, comment="模型名称")
    model_parameters = Column(TEXT, nullable=True, comment="模型参数")
    gpu_type = Column(TEXT, nullable=True, comment="gpu类型")
    replica_num = Column(Integer, nullable=True, default=0, comment="副本数量")
    replica_gpu_num = Column(Integer, nullable=True, default=0, comment="副本gpu数量")
    resource_group = Column(String(1000), nullable=True, comment="资源组")
    resource_pool = Column(String(1000), nullable=True, comment="资源池")
    deleted = Column(Integer, nullable=False, default=0, comment="是否删除 0: 未删除 1: 已删除")
    create_user_id = Column(String(255), nullable=True, default=0, comment="创建人")
    update_user_id = Column(String(255), nullable=True, default=0, comment="更新人")
    error_msg = Column(TEXT, nullable=True, comment="错误信息")
    model_type = Column(Integer, nullable=True, comment="型类型1-base2-chat3-embedding4-reranker")


# 评测人员集合与评测人员关系表
class SystemQuizUserSetRelations(Base):
    __tablename__ = "system_quiz_user_set_relations"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(VARCHAR(256), nullable=False, comment="评测人员ID")
    user_set_identifier = Column(
        String(255), nullable=False, comment="评测人员集合版本号"
    )
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )
    version = Column(
        Integer, nullable=False, comment="版本号", server_default=text("0")
    )
    __table_args__ = (
        UniqueConstraint('user_set_identifier', 'version', 'user_id', name='_user_set_identifier_version_user_id_uc'),
    )


# 评测人员集合表
class SystemUserSets(Base):
    __tablename__ = "system_quiz_user_sets"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_set_identifier = Column(
        String(255), nullable=False, unique=True, comment="评测人员集合版本号"
    )
    name = Column(String(255), nullable=False, comment="评测人员集合名称")
    quiz_type = Column(
        String(100),
        nullable=False,
        server_default="art",
        comment="评测类型  AB评测:ab, 人工评测:art, 自评测试:objective",
    )
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )
    description = Column(TEXT, nullable=False, comment="描述")
    created_user = Column(String(100), nullable=False, comment="创建人")
    updated_user = Column(String(100), nullable=True, comment="更新人")
    version = Column(
        Integer, nullable=False, comment="版本号", server_default=text("0")
    )
    state = Column(
        Integer,
        nullable=False,
        server_default=text("0"),
        comment="状态 0: 未发布 1:  可用 2: 不可用 3、删除态",
    )
    __table_args__ = (
        UniqueConstraint('user_set_identifier', 'version', name='_user_set_identifier_version_uc'),
    )


class User(Base):
    __tablename__ = "user"

    id = Column(Integer, primary_key=True, autoincrement=True, name="id")
    user_id = Column(TINYTEXT, nullable=False, name="user_id")
    name = Column(TINYTEXT, nullable=False, name="name", comment="系统用户名，例如邮箱前缀等")
    full_name = Column(TINYTEXT, nullable=False, name="full_name", comment="用户姓名")
    department = Column(TINYTEXT, nullable=False, name="department", comment="用户部门")
    permission = Column(JSON, nullable=True, name="permission", comment="用户权限")
    state = Column(Integer, default=0, nullable=False, name="state")
    created_at = Column(DateTime, default=func.now(), name="created_at", server_default=text('CURRENT_TIMESTAMP'))
    updated_at = Column(DateTime, default=func.now(), name="updated_at",
                        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
                        server_onupdate=FetchedValue())


class Menu(Base):
    __tablename__ = "menu"

    id = Column(Integer, primary_key=True, autoincrement=True, name="id")
    name = Column(TINYTEXT, nullable=False, name="name", comment="菜单名称")
    parent_id = Column(Integer, nullable=False, default=0, name="parent_id", comment="父菜单ID")
    menu_order = Column(Integer, nullable=False, default=0, name="menu_order", comment="菜单排序")
    link = Column(TEXT, nullable=True, name="link", comment="菜单链接")
    icon = Column(TEXT, nullable=True, name="icon", comment="菜单图标")
    permission = Column(JSON, nullable=True, name="permission", comment="菜单权限")
    state = Column(Integer, default=0, nullable=False, name="state", comment="0: default, -1: deleted")
    created_at = Column(DateTime, default=func.now(), name="created_at", server_default=text('CURRENT_TIMESTAMP'))
    updated_at = Column(DateTime, default=func.now(), name="updated_at",
                        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
                        server_onupdate=FetchedValue())


class UserGroup(Base):
    __tablename__ = "user_group"

    id = Column(Integer, primary_key=True, autoincrement=True, name="id")
    group_id = Column(TINYTEXT, nullable=False, name="group_id")
    name = Column(TINYTEXT, nullable=False, name="name", comment="用户组名称")
    parent_id = Column(Integer, nullable=False, default=0, name="parent_id", comment="父用户组ID")
    permission = Column(JSON, nullable=True, name="permission", comment="用户组权限")
    state = Column(Integer, default=0, nullable=False, name="state")
    created_at = Column(DateTime, default=func.now(), name="created_at", server_default=text('CURRENT_TIMESTAMP'))
    updated_at = Column(DateTime, default=func.now(), name="updated_at",
                        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
                        server_onupdate=FetchedValue())


def sqlalchemy_serializer(obj):
    fields = {}
    for field in [
        x.key
        for x in class_mapper(obj.__class__).iterate_properties
        if not x.key.startswith("_")
    ]:
        data = obj.__getattribute__(field)
        try:
            json.dumps(data)
            fields[field] = data
        except TypeError:
            fields[field] = None
    return fields


def model_to_dict(inspectable):
    """使用SQLAlchemy的inspect方法将模型转换为字典，并格式化日期时间字段"""
    data = {}
    for c in inspect(inspectable).mapper.column_attrs:
        value = getattr(inspectable, c.key)
        if isinstance(value, datetime):
            data[c.key] = value.strftime("%Y-%m-%d %H:%M:%S")
        else:
            data[c.key] = value
    return data


# 计划生成任务表
class SystemQuizPlanTask(Base):
    __tablename__ = "system_quiz_plan_task"

    id = Column(Integer, primary_key=True, autoincrement=True)
    evaluation_id = Column(String(100), nullable=True, comment="唯一的评审ID")
    task_id = Column(String(100), nullable=True, comment="任务ID", index=True)
    user_id = Column(String(100), nullable=True, comment="用户ID")
    task_name = Column(String(100), nullable=True, comment="任务名称")
    task_desc = Column(TEXT, nullable=True, comment="任务描述")
    system_version = Column(String(255), nullable=True, comment="系统版本号")
    criteria_set_identifier = Column(
        String(255), nullable=True, comment="评审标准集合版本号"
    )
    qa_round_criteria_set_identifier = Column(
        String(255), nullable=True, comment="QA轮次标准集合版本号"
    )
    dim_set_identifier = Column(
        String(255), nullable=True, comment="评审维度集合版本号"
    )
    qa_round_dim_set_identifier = Column(
        String(255), nullable=True, comment="QA轮次维度集合版本号"
    )
    system_version_info = Column(String(255), nullable=False, comment="系统版本信息")
    question_set_identifier = Column(
        String(255), nullable=True, comment="问题集合版本号"
    )
    evaluation_state = Column(
        Integer, nullable=True, server_default=text("0"), comment="评审任务状态"
    )
    evaluation_type = Column(
        String(100),
        nullable=False,
        server_default="art",
        comment="评测类型  ab测试: ab、人工评测: art",
    )
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )
    task_expire = Column(TIMESTAMP, nullable=True, comment="任务过期时间")
    # 0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束
    task_state = Column(
        Integer,
        nullable=True,
        server_default=text("0"),
        index=True,
        comment="0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束",
    )
    # 任务数量
    task_num = Column(
        Integer, nullable=True, server_default=text("0"), comment="任务数量"
    )
    # 已获取答案的任务数量
    task_answer_num = Column(
        Integer,
        nullable=True,
        server_default=text("0"),
        comment="已获取答案的任务数量",
    )
    # 已评测任务的数量
    task_evaluation_num = Column(
        Integer, nullable=True, server_default=text("0"), comment="已评测任务的数量"
    )
    eval_product = Column(TEXT, nullable=True, comment="评测产品")
    created_user = Column(String(255), nullable=True, default=0, comment="创建人")
    created_user_name = Column(String(255), nullable=True, comment="创建人名称")
    error_msg = Column(TEXT, nullable=True, comment="错误信息")


# 计划生成任务表明细
class SystemQuizPlanTaskInfo(Base):
    __tablename__ = "system_quiz_plan_task_info"

    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(String(100), nullable=True, comment="任务ID", index=True)
    parent_task_id = Column(String(100), nullable=True, comment="父任务ID")
    user_id = Column(TINYTEXT, nullable=True, comment="用户ID")
    question_id = Column(Integer, nullable=True, comment="问题ID")
    evaluation_id = Column(String(100), nullable=True, comment="评审ID")
    system_answer = Column(TEXT, nullable=True, comment="系统答案")
    rating = Column(Integer, nullable=True, default=0, comment="评分")
    system_type = Column(
        String(100),
        nullable=False,
        server_default="art",
        comment="评测方式  a系统: a、b系统: b、人工评测: art",
    )
    trace_id = Column(VARCHAR(255), nullable=True)
    created_at = Column(
        TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )
    feedback = Column(TEXT, nullable=True, default="", comment="反馈")
    source = Column(TEXT, nullable=True, comment="召回数据")
    task_state = Column(
        Integer,
        nullable=True,
        server_default=text("0"),
        comment="0: 未开始 1: 生成中  2: 生成完成 3: 生成失败 4:答题进行中 5 答题完成  6 答题过期",
    )
    system_url = Column(String(255), nullable=True, comment="系统url")
    question = Column(String(255), nullable=True, comment="问题")
    created_user = Column(String(255), nullable=True, default=0, comment="创建人")
    created_user_name = Column(String(255), nullable=True, comment="创建人名称")


class InferenceService(Base):
    __tablename__ = "inference_service"

    id = Column(Integer, primary_key=True, autoincrement=True)
    plan_id = Column(Text, nullable=False, comment="计划ID")
    plan_name = Column(Text, nullable=True, comment="计划名称")
    model_id = Column(Integer, nullable=False, comment="模型ID")
    model_name = Column(Text, nullable=True, comment="模型名称")
    model_type = Column(Integer, nullable=False, comment="模型类型")
    inference_service_id = Column(Integer, nullable=True, comment="推理服务ID")
    inference_state = Column(Integer, nullable=True, comment="推理服务状态 1-更新中 2-运行中 3-下线中 4-已下线 5-异常")
    internal_host = Column(Text, nullable=True, comment="内部服务Host")
    external_path = Column(Text, nullable=True, comment="外部服务路径")
    created_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP"),
        comment="创建时间"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间"
    )


class SystemQuestionDislike(Base):
    __tablename__ = "system_question_dislike"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键")
    evaluation_id = Column(String(500), nullable=False, comment="评审ID")
    question_id = Column(Integer, nullable=False, comment="问题id")
    dislike_count = Column(Integer, nullable=False, comment="点踩数量")
    create_time = Column(
        DateTime,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="创建时间"
    )


class SystemQuestionDislikeUser(Base):
    __tablename__ = "system_question_dislike_user"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键")
    dislike_id = Column(Integer, nullable=False, comment="点踩表主键")
    evaluation_id = Column(String(500), nullable=False, comment="评审ID")
    question_id = Column(Integer, nullable=False, comment="问题ID")
    content = Column(Text, nullable=True, comment="点踩内容")
    user_id = Column(String(500), nullable=False, comment="用户ID")
    user_name = Column(String(500), nullable=True, comment="用户名")
    user_full_name = Column(String(500), nullable=True, comment="用户全名")
    create_time = Column(
        DateTime,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="创建时间"
    )


class SystemQuizPlanSnapshot(Base):
    __tablename__ = "system_quiz_plan_snapshot"
    id = Column(Integer, primary_key=True, autoincrement=True, name="id")
    evaluation_id = Column(String(200), nullable=False, comment="评测id")
    question_set_config = Column(Text, nullable=False, comment="题集配置")
    question_set_identifier = Column(String(200), nullable=False, comment="题集UUId")
    user_set_config = Column(Text, nullable=False, comment="用户集配置")
    user_set_identifier = Column(String(200), nullable=False, comment="用户集uuid")
    inference_config = Column(Text, nullable=False, comment="推理配置")
    create_time = Column(DateTime, default=func.now(), server_default=text('CURRENT_TIMESTAMP'))

import json
import logging
from datetime import datetime
from typing import Optional, List, Sequence
import uuid

from sqlalchemy import func, join, select, and_, or_, update
from sqlalchemy import insert, delete
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from evaluation.share.util import util

from evaluation.constants.status_enum import (
    EvaluationSubTaskStatusEnum,
    EvaluationTaskStatusEnum,
    QuizPlanStatusEnum, QuizPlanDelEnum, QuestionStatusEnum, QuestionSetStatusEnum
)
from evaluation.api.req.evaluation_model import (
    CreateEvaluationTaskReq,
    QuizQuestion,
)
from evaluation.share.orm.model import (
    Menu,
    SystemQuizPlans,
    SystemQuizTasks,
    SystemQuizUserSetRelations,
    SystemUserSets,
    User,
    UserQuizTasks,
    UserQuizTasksInfo,
    SystemQuizCriteriaSets,
    UserQuizTasksCriteriaInfo,
    SystemQuizCriteriaSetRelations,
    SystemQuizCriterias,
    SystemQuizQuestions,
    SystemQuizQuestionTypes,
    SystemQuizQuestions_V2,
    SystemQuizQuestionSets,
    SystemQuizQuestionSetRelationsV2, SystemQuizPlanTaskInfo,
)
from evaluation.share.util.permission import should_match, Permission
from sqlalchemy.orm import aliased

from evaluation.share.util.user import get_user_id_v2

# 人工评测
ART = "art"

logger = logging.getLogger("plaintext")


# 获取给用户分配评测任务
async def get_user_task_by_user_id_and_evaluation_id(
        db: AsyncSession, user_id: str, evaluation_id: str
):
    query = (
        select(UserQuizTasks)
        .filter_by(user_id=user_id)
        .filter_by(evaluation_id=evaluation_id)
    )
    result = await db.execute(query)
    user_task = result.scalars().first()
    return user_task


# 获取系统评测任务
async def get_system_task_by_evaluation_id(db: AsyncSession, evaluation_id: str):
    query = select(SystemQuizTasks).filter_by(evaluation_id=evaluation_id)
    result = await db.execute(query)
    system_task = result.scalars().first()
    return system_task


async def get_system_version_by_task_id(db: AsyncSession, task_id: str):
    query = select(UserQuizTasks).filter_by(task_id=task_id)
    result = await db.execute(query)
    user_task = result.scalars().first()
    return user_task.system_version


# 创建系统评测任务
async def create_evaluation_task(db: AsyncSession, val: CreateEvaluationTaskReq):
    evaluation_task = SystemQuizTasks(
        evaluation_id=str(uuid.uuid1()),
        system_version=val.system_version,
        criteria_set_identifier=val.criteria_set_identifier,
        question_set_identifier=val.question_set_identifier,
        system_version_info=val.system_version_info,
    )
    db.add(evaluation_task)
    await db.commit()
    await db.refresh(evaluation_task)
    return evaluation_task


# 用户创建评测任务
async def generate_evaluation_task(
        db: AsyncSession,
        user_id: str,
        evaluation_id: str,
        task_name: str,
        task_desc: str,
        system_version: str,
        system_version_info: str,
        evaluation_type: str,
        dim_set_id,
        qa_round_dim_set_id,
        criteria_set_identifier,
        question_set_identifier,
        qa_round_criteria_set_identifier,
        task_state: int,
        task_num,
        eval_product,
):
    user_task = UserQuizTasks(
        user_id=user_id,
        evaluation_id=evaluation_id,
        evaluation_state=1,
        task_id=uuid.uuid1(),
        task_name=task_name,
        task_desc=task_desc,
        system_version=system_version,
        system_version_info=system_version_info,
        evaluation_type=evaluation_type,
        dim_set_identifier=dim_set_id,
        qa_round_dim_set_identifier=qa_round_dim_set_id,
        criteria_set_identifier=criteria_set_identifier,
        qa_round_criteria_set_identifier=qa_round_criteria_set_identifier,
        question_set_identifier=question_set_identifier,
        task_state=task_state,
        task_num=task_num,
        eval_product=eval_product,
    )
    db.add(user_task)
    await db.commit()
    await db.refresh(user_task)
    return user_task


# 更新用户评测任务状态
async def update_evaluation_task_state(
        db: AsyncSession,
        user_id: str,
        task_id: str,
        task_state: EvaluationTaskStatusEnum,
        is_commit=False,
):
    await db.execute(
        update(UserQuizTasks)
        .where(UserQuizTasks.user_id == user_id)
        .where(UserQuizTasks.task_id == task_id)
        .values(task_state=task_state)
    )
    if is_commit:
        await db.commit()


# 根据评测id拿到问题集合id
async def get_question_set_id_by_evaluation_id(db: AsyncSession, evaluation_id: str):
    query = select(SystemQuizTasks).filter_by(evaluation_id=evaluation_id)
    result = await db.execute(query)
    systemTask = result.scalars().first()
    question_set_identifier = systemTask.question_set_identifier
    return question_set_identifier


async def get_criteria_set_id_by_evaluation_id(db: AsyncSession, evaluation_id: str):
    query = select(SystemQuizTasks).filter_by(evaluation_id=evaluation_id)
    result = await db.execute(query)
    systemTask = result.scalars().first()
    if systemTask is None:
        return None
    criteria_set_identifier = systemTask.criteria_set_identifier
    return criteria_set_identifier


# 根据评测id拿到所有问题
async def list_questions_by_evaluation_id(db: AsyncSession, evaluation_id: str):
    # 1. 根据评测id拿到问题集合id
    question_set_identifier = await get_question_set_id_by_evaluation_id(
        db, evaluation_id
    )

    # 2. 根据问题集合id直接获取与它相关的问题
    questions_query = (
        select(SystemQuizQuestions)
        .join(
            SystemQuizQuestionSetRelationsV2,
            SystemQuizQuestions.id == SystemQuizQuestionSetRelationsV2.question_id,
        )
        .filter(
            SystemQuizQuestionSetRelationsV2.question_set_identifier
            == question_set_identifier
        )
    )

    result = await db.execute(questions_query)

    return result.scalars().all()


# 根据question_set_identifier 拿到所有问题
async def list_questions_by_question_set_identifier(
        db: AsyncSession, question_set_identifier: str
):
    questions_query = (
        select(SystemQuizQuestions_V2)
        .join(
            SystemQuizQuestionSetRelationsV2,
            SystemQuizQuestions_V2.question_id
            == SystemQuizQuestionSetRelationsV2.question_id,
        )
        .filter(
            SystemQuizQuestionSetRelationsV2.question_set_identifier
            == question_set_identifier
        )
    )

    result = await db.execute(questions_query)

    return result.scalars().all()


# 根据问题+标准 创建评测任务明细
async def create_evaluation_task_info(
        db: AsyncSession,
        user_id: str,
        question_id: int,
        evaluation_id: str,
        task_id: str,
        system_type: str,
):
    user_task_info = UserQuizTasksInfo(
        user_id=user_id,
        task_id=uuid.uuid1(),
        question_id=question_id,
        evaluation_id=evaluation_id,
        parent_task_id=task_id,
        system_type=system_type,
    )
    db.add(user_task_info)
    await db.commit()
    await db.refresh(user_task_info)
    return user_task_info


# 获取评测标准
# 根据任务id 创建评测任务标准明细
async def create_evaluation_task_criteria_info(
        db: AsyncSession,
        user_id: str,
        task_id: str,
        question_id: int,
        criteria_set: list,
        system_type,
):
    for criteria in criteria_set:
        user_task_criteria_info = UserQuizTasksCriteriaInfo(
            user_id=user_id,
            task_id=task_id,
            question_id=question_id,
            rating_key=criteria.rating_key,
            rating_int_value=None,
            title=criteria.title,
            rating_string_value="",
            rating_type=criteria.rating_type,
            system_type=system_type,
        )
        db.add(user_task_criteria_info)
    await db.commit()


async def get_criteria_list_by_evaluation_id(db: AsyncSession, evaluation_id: str):
    criteria_set_identifier = await get_criteria_set_id_by_evaluation_id(
        db, evaluation_id
    )
    if criteria_set_identifier is None:
        return None, None
    query = (
        select(SystemQuizCriterias)
        .join(
            SystemQuizCriteriaSetRelations,
            SystemQuizCriteriaSetRelations.criteria_id == SystemQuizCriterias.id,
        )
        .join(
            SystemQuizCriteriaSets,
            SystemQuizCriteriaSets.criteria_set_identifier
            == SystemQuizCriteriaSetRelations.criteria_set_identifier,
        )
        .filter(
            SystemQuizCriteriaSets.criteria_set_identifier == criteria_set_identifier
        )
    )
    result = await db.execute(query)
    criteria_list = result.scalars().all()

    return criteria_list, criteria_set_identifier


# 获取评测标准
async def get_criteria_list_by_criteria_set_identifier(
        db: AsyncSession, criteria_set_identifier: str
):
    query = (
        select(SystemQuizCriterias)
        .join(
            SystemQuizCriteriaSetRelations,
            SystemQuizCriteriaSetRelations.criteria_id == SystemQuizCriterias.id,
        )
        .join(
            SystemQuizCriteriaSets,
            SystemQuizCriteriaSets.criteria_set_identifier
            == SystemQuizCriteriaSetRelations.criteria_set_identifier,
        )
        .filter(
            SystemQuizCriteriaSets.criteria_set_identifier == criteria_set_identifier
        )
    )
    result = await db.execute(query)
    criteria_list = result.scalars().all()

    return criteria_list


# 获取评测标准
async def get_criteria_list_by_id(db: AsyncSession, criteria_id: int):
    query = (
        select(SystemQuizCriterias)
        .join(
            SystemQuizCriteriaSetRelations,
            SystemQuizCriteriaSetRelations.criteria_id == SystemQuizCriterias.id,
        )
        .join(
            SystemQuizCriteriaSets,
            SystemQuizCriteriaSets.id == SystemQuizCriteriaSetRelations.criteria_id,
        )
        .filter(SystemQuizCriteriaSets.id == criteria_id)
    )
    result = await db.execute(query)
    criteria_list = result.scalars().all()

    return criteria_list


# 根据userid获取评测任务 取最新的一个评测任务
async def get_evaluation_task_by_user_id(db: AsyncSession, user_id: str):
    # TODO 如果有多个任务取最新时间的? 只查人工评测类型的任务 评测类型 ab测试: ab、人工评测: art
    query = (
        select(UserQuizTasks)
        .filter_by(user_id=user_id, evaluation_type=ART)
        .order_by(UserQuizTasks.created_at.desc())
    )
    result = await db.execute(query)
    user_task = result.scalars().first()
    return user_task


# 根据task_id获取评测任务
async def get_evaluation_task_by_task_id(db: AsyncSession, task_id: str):
    query = select(UserQuizTasks).filter_by(task_id=task_id)
    result = await db.execute(query)
    user_task = result.scalars().first()
    return user_task


async def get_quiz_questions_by_evaluation_id_and_user_id(
        db: AsyncSession, user_id: str
):
    query = (
        select(UserQuizTasks)
        .filter_by(user_id=user_id, evaluation_type=ART)
        .order_by(UserQuizTasks.created_at.desc())
    )
    result = await db.execute(query)
    user_task = result.scalars().first()

    query = (
        select(UserQuizTasksInfo)
        .distinct()
        .join(
            UserQuizTasks,
            UserQuizTasks.evaluation_id == UserQuizTasksInfo.evaluation_id,
        )
        .join(
            SystemQuizQuestions, SystemQuizQuestions.id == UserQuizTasksInfo.question_id
        )
        .filter(UserQuizTasks.user_id == user_id)
        .filter(UserQuizTasksInfo.parent_task_id == user_task.task_id)
    )

    result = await db.execute(query)
    user_quiz_tasks_info_list = result.scalars().all()

    if len(user_quiz_tasks_info_list) == 0:
        return None

    seen = set()
    unique_question_ids = [
        info.question_id
        for info in sorted(user_quiz_tasks_info_list, key=lambda x: x.id)
        if info.question_id not in seen and not seen.add(info.question_id)
    ]
    question_query = select(SystemQuizQuestions).filter(
        SystemQuizQuestions.id.in_(unique_question_ids)
    )
    result = await db.execute(question_query)
    question_list = result.scalars().all()

    id_to_index = {qid: index for index, qid in enumerate(unique_question_ids)}
    sorted_question_list = sorted(question_list, key=lambda q: id_to_index[q.id])

    data = []
    for question in sorted_question_list:
        question_data = await assemble_data_for_question(
            question, user_quiz_tasks_info_list, db
        )
        data.append(question_data)
    return data


async def get_quiz_questions_by_evaluation_id_and_user_id_v2(
        db: AsyncSession, user_id: str
):
    query = (
        select(UserQuizTasks)
        .filter_by(user_id=user_id, evaluation_type=ART)
        .order_by(UserQuizTasks.created_at.desc())
    )
    result = await db.execute(query)
    user_task = result.scalars().first()

    query = (
        select(UserQuizTasksInfo)
        .distinct()
        .join(
            UserQuizTasks,
            UserQuizTasks.evaluation_id == UserQuizTasksInfo.evaluation_id,
        )
        .join(
            SystemQuizQuestions, SystemQuizQuestions.id == UserQuizTasksInfo.question_id
        )
        .filter(UserQuizTasks.user_id == user_id)
        .filter(UserQuizTasksInfo.parent_task_id == user_task.task_id)
    )

    result = await db.execute(query)
    user_quiz_tasks_info_list = result.scalars().all()

    if len(user_quiz_tasks_info_list) == 0:
        return None

    seen = set()
    unique_question_ids = [
        info.question_id
        for info in sorted(user_quiz_tasks_info_list, key=lambda x: x.id)
        if info.question_id not in seen and not seen.add(info.question_id)
    ]
    question_query = select(SystemQuizQuestions).filter(
        SystemQuizQuestions.id.in_(unique_question_ids)
    )
    result = await db.execute(question_query)
    question_list = result.scalars().all()

    id_to_index = {qid: index for index, qid in enumerate(unique_question_ids)}
    sorted_question_list = sorted(question_list, key=lambda q: id_to_index[q.id])

    data = []
    for question in sorted_question_list:
        question_data = await assemble_data_for_question_v2(
            question, user_quiz_tasks_info_list, db
        )
        data.append(question_data)
    return data


def evaluate_criteria_status(criteria_list):
    need_completed_columns = ["a", "c"]
    if not criteria_list:
        return 0
    completed_criteria = 0
    # 是否操作过
    isEdit = False
    for criteria in criteria_list:
        if (
                criteria.rating_int_value > -1
                and criteria.rating_key in need_completed_columns
        ):
            completed_criteria += 1
        if (
                criteria.rating_int_value > -1
                and criteria.rating_key not in need_completed_columns
        ):
            isEdit = True
    if len(need_completed_columns) == completed_criteria:
        return 2
    elif 0 < completed_criteria < len(need_completed_columns) or isEdit:
        return 1
    else:
        return 0


async def assemble_data_for_question(
        question, user_quiz_tasks_info_list, db: AsyncSession
):
    scores = []
    complete_state = 0
    for user_quiz_tasks_info in user_quiz_tasks_info_list:
        if user_quiz_tasks_info.question_id == question.id:
            criteria_query = (
                select(UserQuizTasksCriteriaInfo)
                .filter(
                    UserQuizTasksCriteriaInfo.task_id == user_quiz_tasks_info.task_id
                )
                .filter(
                    UserQuizTasksCriteriaInfo.question_id
                    == user_quiz_tasks_info.question_id
                )
            )
            result = await db.execute(criteria_query)
            criteria_list = result.scalars().all()
            for criteria in criteria_list:
                scores.append(
                    {
                        "title": criteria.title,
                        "value": criteria.rating_int_value,
                        "rating_key": criteria.rating_key,
                        "rating_type": criteria.rating_type,
                        "feedback": criteria.feedback,
                    }
                )
            complete_state = evaluate_criteria_status(criteria_list)
            break

    return {
        "question_id": question.id,
        "question_text": question.question,
        "question_desc": question.question_desc,
        "rating": user_quiz_tasks_info.rating,
        "reference_answer": question.reference_answer,
        "system_answer": user_quiz_tasks_info.system_answer,
        "task_id": user_quiz_tasks_info.task_id,
        "feedback": user_quiz_tasks_info.feedback,
        "scores": scores,
        "complete_state": complete_state,
    }


async def assemble_data_for_question_v2(
        question, user_quiz_tasks_info_list, db: AsyncSession
):
    scores = []
    for user_quiz_tasks_info in user_quiz_tasks_info_list:
        if user_quiz_tasks_info.question_id == question.id:
            criteria_query = (
                select(UserQuizTasksCriteriaInfo)
                .filter(
                    UserQuizTasksCriteriaInfo.task_id == user_quiz_tasks_info.task_id
                )
                .filter(
                    UserQuizTasksCriteriaInfo.question_id
                    == user_quiz_tasks_info.question_id
                )
            )
            result = await db.execute(criteria_query)
            criteria_list = result.scalars().all()
            for criteria in criteria_list:
                if criteria.system_type == "ab":
                    scores.append(
                        {
                            "title": criteria.title,
                            "value": criteria.rating_string_value,
                            "rating_key": criteria.rating_key,
                            "rating_type": criteria.rating_type,
                            "feedback": criteria.feedback,
                        }
                    )
                else:
                    scores.append(
                        {
                            "title": criteria.title,
                            "value": criteria.rating_int_value,
                            "rating_key": criteria.rating_key,
                            "rating_type": criteria.rating_type,
                            "feedback": criteria.feedback,
                        }
                    )

            break

    return {
        "question_id": question.id,
        "question_text": question.question,
        "question_desc": question.question_desc or '',
        "rating": user_quiz_tasks_info.rating,
        "details": question.details,
        "system_answer": user_quiz_tasks_info.system_answer or '',
        "task_id": user_quiz_tasks_info.task_id,
        "feedback": user_quiz_tasks_info.feedback or '',
        "scores": scores,
    }


# 针对每个问题的不同评测标准进行评分
async def rate_quiz_question(
        db: AsyncSession,
        user_id: str,
        task_id: str,
        question_id: str,
        rating_key: str,
        rating_value: str,
        feedback: str,
):
    query = (
        select(UserQuizTasksCriteriaInfo)
        .filter(UserQuizTasksCriteriaInfo.task_id == task_id)
        .filter(UserQuizTasksCriteriaInfo.question_id == question_id)
        .filter(UserQuizTasksCriteriaInfo.rating_key == rating_key)
    )
    result = await db.execute(query)
    user_quiz_tasks_criteria_info = result.scalars().first()
    if user_quiz_tasks_criteria_info is None:
        return None
    if user_quiz_tasks_criteria_info.rating_type == "star":
        user_quiz_tasks_criteria_info.rating_int_value = rating_value
    elif user_quiz_tasks_criteria_info.rating_type == "yes_no":
        user_quiz_tasks_criteria_info.rating_int_value = rating_value
    elif user_quiz_tasks_criteria_info.rating_type == "ABTEST":
        user_quiz_tasks_criteria_info.rating_string_value = rating_value
    user_quiz_tasks_criteria_info.feedback = feedback
    try:
        db.add(user_quiz_tasks_criteria_info)
        await db.commit()
        await db.refresh(user_quiz_tasks_criteria_info)
        return user_quiz_tasks_criteria_info
    except SQLAlchemyError as e:
        logger.error(f"Database error occurred: {e}")
    except Exception as e:
        logger.error(f"Unexpected error occurred: {e}")
    return None


# 提交用户评测任务
async def submit_evaluation_task(db: AsyncSession, user_id: str, task_id: str):
    try:
        await db.execute(
            update(UserQuizTasks)
            .where(UserQuizTasks.user_id == user_id)
            .where(UserQuizTasks.task_id == task_id)
            .values(evaluation_state=2, task_state=3)
        )
        await db.commit()
        return True
    except SQLAlchemyError as e:
        logger.error(f"Database error occurred: {e}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error occurred: {e}")
        raise e


# 评价问题的参考答案
async def review_question(db: AsyncSession, task_id: str, rating: bool, feedback: str):
    await db.execute(
        update(UserQuizTasksInfo)
        .where(UserQuizTasksInfo.task_id == task_id)
        .values(rating=rating, feedback=feedback)
    )
    await db.commit()
    return True


# 根据任务id获取评测任务明细列表
async def list_evaluation_task_info_by_parent_task_id(
        db: AsyncSession, parent_task_id: str
):
    query = select(UserQuizTasksInfo).filter_by(parent_task_id=parent_task_id)
    result = await db.execute(query)
    user_task_info = result.scalars().all()
    return user_task_info


# 判断用户任务ID问题是否都已经评测完成
async def judge_task_finish(db: AsyncSession, evaluation_id: str, parent_task_id: str):
    count = await get_system_quiz_criterias_count(db, evaluation_id)
    query = select(UserQuizTasksInfo).filter_by(parent_task_id=parent_task_id)
    result = await db.execute(query)
    user_task_info = result.scalars().all()
    unfinished_task_id = []
    for info in user_task_info:
        query = select(UserQuizTasksCriteriaInfo).filter_by(task_id=info.task_id)
        result1 = await db.execute(query)
        q_count = len(result1.scalars().all())
        if q_count != count:
            unfinished_task_id.append(info.task_id)
    return unfinished_task_id


# 判断用户任务ID问题是否都已经评测完成
async def judge_task_finish_v2(db: AsyncSession, parent_task_id: str):
    query = select(UserQuizTasksInfo).where(UserQuizTasksInfo.parent_task_id == parent_task_id).where(
        UserQuizTasksInfo.task_state != 3)
    result = await db.execute(query)
    task_info_list = result.scalars().all()
    unfinished_task_ids = []
    for info in task_info_list:
        unfinished_task_ids.append(info.task_id)
    return unfinished_task_ids


# 获取评测标准指标数量
async def get_system_quiz_criterias_count(db: AsyncSession, evaluation_id: str):
    query1 = select(SystemQuizTasks).filter_by(evaluation_id=evaluation_id)
    result1 = await db.execute(query1)
    system_quiz_tasks_info = result1.scalars().first()
    criteria_set_identifier = system_quiz_tasks_info.criteria_set_identifier
    query = select(SystemQuizCriteriaSetRelations).filter_by(
        criteria_set_identifier=criteria_set_identifier
    )
    result = await db.execute(query)
    count = len(result.scalars().all())
    return count


async def create_quiz_question(db: AsyncSession, qq: QuizQuestion):
    qa = SystemQuizQuestions(
        question=qq.question,
        question_desc=qq.question_desc,
        reference_answer=qq.reference_answer,
    )
    db.add(qa)
    await db.commit()
    await db.refresh(qa)


# 根据task_id和question_id获取评测任务明细
async def get_evaluation_task_info_by_task_id_and_question_id(
        db: AsyncSession, task_id: str, question_id: int
):
    query = select(UserQuizTasksInfo).filter_by(
        task_id=task_id, question_id=question_id
    )
    result = await db.execute(query)
    user_task_info = result.scalars().first()
    return user_task_info


async def update_task_info(
        db: AsyncSession,
        task_id: str,
        question_id: int,
        system_answer: str,
        trace_id: Optional[str] = None,
        reference_str: Optional[str] = None,
) -> bool:
    update_values = {"system_answer": system_answer}
    if trace_id is not None:
        update_values["trace_id"] = trace_id
    # 检查 reference_str 是不是 list，如果是，将其序列化为 JSON 字符串
    if reference_str is not None and isinstance(reference_str, list):
        update_values["source"] = json.dumps(reference_str, ensure_ascii=False)
    else:
        update_values["source"] = reference_str

    try:
        await db.execute(
            update(UserQuizTasksInfo)
            .where(UserQuizTasksInfo.task_id == task_id)
            .where(UserQuizTasksInfo.question_id == question_id)
            .values(**update_values)
        )
        await db.commit()
        return True
    except SQLAlchemyError as e:
        print(f"Database error occurred: {e}")
    except Exception as e:
        print(f"Unexpected error occurred: {e}")
    return False


async def update_system_answer(
        db: AsyncSession,
        task_id: str,
        question_id: int,
        system_answer: str,
        trace_id: str,
        reference_str: Optional[list[dict]],
) -> bool:
    system_answer_str = serialize_system_answer(system_answer)
    return await update_task_info(
        db, task_id, question_id, system_answer_str, trace_id, reference_str
    )


async def update_ab_sys_answer(
        db: AsyncSession,
        task_id: str,
        question_id: int,
        system_answer: str,
) -> bool:
    system_answer_str = serialize_system_answer(system_answer)
    return await update_task_info(db, task_id, question_id, system_answer_str)


def serialize_system_answer(answer):
    if isinstance(answer, (dict, list)):
        return json.dumps(answer, ensure_ascii=False)
    return answer


# 根据task_id获取评测任务明细
async def get_evaluation_task_info_by_task_id(db: AsyncSession, task_id: str):
    query = select(UserQuizTasksInfo).filter_by(task_id=task_id)
    result = await db.execute(query)
    user_task_info = result.scalars().first()
    return user_task_info


# 根据 parent_task_id 获取评测任务明细没有答案的数据
async def get_evaluation_art_task_info_by_parent_task_id_no_answer(
        db: AsyncSession, task_id: str
):
    query = select(UserQuizTasksInfo).filter(
        UserQuizTasksInfo.parent_task_id == task_id,
        UserQuizTasksInfo.system_answer.is_(None)
        | (UserQuizTasksInfo.system_answer == ""),
    )
    result = await db.execute(query)
    user_task_info = result.scalars().all()
    return user_task_info


# 根据 parent_task_id 获取评测任务明细没有答案的数据
async def get_evaluation_ab_task_info_by_parent_task_id_no_answer(
        db: AsyncSession, task_id: str
):
    query = select(UserQuizTasksInfo).filter(
        UserQuizTasksInfo.parent_task_id == task_id
    )
    result = await db.execute(query)
    user_task_info = result.scalars().all()

    need_answer_task_info = [
        info
        for info in user_task_info
        if not info.system_answer
           or any(
            not sys_answer["answer"] for sys_answer in json.loads(info.system_answer)
        )
    ]

    return need_answer_task_info


async def get_system_quiz_questions(
        db: AsyncSession,
        page: int,
        pagesize: int,
        question_type: str = None,
        order_str: str = None,
):
    base_query = select(SystemQuizQuestions_V2).filter(
        SystemQuizQuestions_V2.state != QuestionStatusEnum.get_enum_code(QuestionStatusEnum.DELETED)
    )

    if question_type:
        base_query = base_query.filter(SystemQuizQuestions_V2.question_type == question_type)

    # 查询总数
    total_query = select(func.count()).select_from(base_query)
    total_result = await db.execute(total_query)
    total = total_result.scalar()
    if total == 0:
        return total, []

    # 排序
    order = util.get_order(order_str, SystemQuizQuestions_V2)
    # 查询
    final_query = base_query.order_by(order)
    final_query = final_query.limit(pagesize).offset((page - 1) * pagesize)
    result = await db.execute(final_query)
    questions = result.scalars().all()

    return total, questions


# 根据问题id修改问题信息
async def update_system_quiz_questions(
        db: AsyncSession,
        question_id: int,
        question: str,
        question_desc: str,
        reference_answer: str,
):
    await db.execute(
        update(SystemQuizQuestions)
        .where(SystemQuizQuestions.id == question_id)
        .values(
            question=question,
            question_desc=question_desc,
            reference_answer=reference_answer,
        )
    )
    await db.commit()
    return True


# 获取系统题类型列表
async def get_system_quiz_question_types(db: AsyncSession):
    query = select(SystemQuizQuestionTypes)
    result = await db.execute(query)
    question_types = result.scalars().all()
    return question_types


# 添加系统题型
async def add_system_quiz_question_types(db: AsyncSession, question_type: str):
    await db.execute(
        insert(SystemQuizQuestionTypes).values(
            [
                {"question_type": question_type},
            ]
        )
    )
    await db.commit()
    return True


# 获取系统评测任务列表
async def get_evaluation_task_list(
        db: AsyncSession,
        page: int,
        pagesize: int,
        fetch_total: bool = False,
        search_term: str = None,
):
    total = None
    if fetch_total:
        query = select(SystemQuizTasks)
        result = await db.execute(query)
        total = len(result.scalars().all())

    query = select(SystemQuizTasks)
    if search_term:
        query = query.filter(
            or_(
                SystemQuizTasks.system_version.like(f"%{search_term}%"),
                SystemQuizTasks.system_version_info.like(f"%{search_term}%"),
                SystemQuizTasks.criteria_set_identifier.like(f"%{search_term}%"),
                SystemQuizTasks.question_set_identifier.like(f"%{search_term}%"),
                SystemQuizTasks.evaluation_id.like(f"%{search_term}%"),
            )
        )

    query.limit(pagesize).offset((page - 1) * pagesize)
    query.order_by(SystemQuizTasks.created_at.desc())
    result = await db.execute(query)
    sys_task = result.scalars().all()
    return total, sys_task


# 获取评测标准
async def get_evaluation_criteria_by_rating_key(db: AsyncSession, rating_key: str):
    query = select(SystemQuizCriterias).filter_by(rating_key=rating_key)
    result = await db.execute(query)
    criteria = result.scalars().first()
    return criteria


# 创建系统问题到题库
async def add_system_question(
        db: AsyncSession,
        question: str,
        question_desc: str,
        question_type_id: int,
        question_type: str,
        details: dict,
        category: str,
        user_id: str,
        version=1,
):
    question_id = uuid.uuid1()
    await db.execute(
        insert(SystemQuizQuestions_V2).values(
            [
                {
                    "question_id": question_id,
                    "question": question,
                    "question_desc": question_desc,
                    "question_type_id": question_type_id,
                    "question_type": question_type,
                    "details": details,
                    "state": 1,
                    "category": category,
                    "version": version,
                    "created_user_id": user_id,
                }
            ]
        )
    )
    await db.commit()
    return True


# 修改系统问题到题库
async def update_system_question(
        db: AsyncSession,
        question_id: str,
        question_str: str,
        question_desc: str,
        question_type_id: int,
        question_type: str,
        details: dict,
        category: str,
        user_id: str,
):
    stmt = (
        update(SystemQuizQuestions_V2)
        .where(SystemQuizQuestions_V2.question_id == question_id)  # 指定要更新的记录
        .values(
            question=question_str,
            question_desc=question_desc,
            question_type_id=question_type_id,
            question_type=question_type,
            details=details,
            category=category,
            updated_user_id=user_id,
        )
    )
    await db.execute(stmt)
    await db.commit()
    return True


# 删除系统题库问题
async def delete_system_question(db: AsyncSession, question_id: str, version: int):
    if version:
        await db.execute(
            update(SystemQuizQuestions_V2)
            .where(
                SystemQuizQuestions_V2.question_id == question_id, version == version
            )
            .values(SystemQuizQuestions_V2.state != QuestionStatusEnum.get_enum_code(QuestionStatusEnum.DELETED))
        )
    else:
        await db.execute(
            update(SystemQuizQuestions_V2)
            .where(SystemQuizQuestions_V2.question_id == question_id)
            .values(state=QuestionStatusEnum.get_enum_code(QuestionStatusEnum.DELETED))
        )
    await db.commit()
    return True


# 查询问题最新的版本
async def get_system_question_last_version(db: AsyncSession, question_id: str):
    query = (
        select(SystemQuizQuestions_V2)
        .filter(SystemQuizQuestions_V2.question_id == question_id)
        .filter(SystemQuizQuestions_V2.state != QuestionStatusEnum.get_enum_code(QuestionStatusEnum.DELETED))
        .order_by(SystemQuizQuestions_V2.version.desc())
    )
    result = await db.execute(query)
    question = result.scalars().first()
    return question.version if question else None


# 修改题集
async def update_question_set(
        db: AsyncSession,
        question_set_identifier: str,
        name: str,
        desc: str,
        ids: list,
        user_id: str,
):
    update_values = {}
    if name is not None:
        update_values["name"] = name
    if desc is not None:
        update_values["desc"] = desc
    update_values["updated_user_id"] = user_id
    if update_values:
        await db.execute(
            update(SystemQuizQuestionSets)
            .where(
                SystemQuizQuestionSets.question_set_identifier
                == question_set_identifier
            )
            .values(**update_values)
        )

    return await question_set_relations(db, ids, question_set_identifier, True)


# 创建题集
async def create_question_set(
        db: AsyncSession, name: str, desc: str, ids: list, user_id: str
):
    question_set_identifier = str(uuid.uuid1())
    await db.execute(
        insert(SystemQuizQuestionSets).values(
            [
                {
                    "question_set_identifier": question_set_identifier,
                    "name": name,
                    "desc": desc,
                    "created_user_id": user_id,
                    "state": 1,
                }
            ]
        )
    )
    await question_set_relations(db, ids, question_set_identifier, False)
    await db.commit()
    return True


# 获取题集列表
async def get_question_sets(
        db: AsyncSession,
        page: int,
        pagesize: int,
        fetch_total: bool = False,
        search_term: str = None,
        order_str: str = None,
):
    total = None
    if fetch_total:
        query = select(SystemQuizQuestionSets).filter(
            SystemQuizQuestionSets.state != QuestionSetStatusEnum.get_enum_code(QuestionSetStatusEnum.DELETED)
        )
        result = await db.execute(query)
        total = len(result.scalars().all())
    order = util.get_order(order_str, SystemQuizQuestionSets)
    query = (
        select(SystemQuizQuestionSets)
        .filter(SystemQuizQuestionSets.state != QuestionSetStatusEnum.get_enum_code(QuestionSetStatusEnum.DELETED))
        .offset((page - 1) * pagesize)
        .limit(pagesize)
        .order_by(order)
    )
    if search_term:
        query = query.filter(SystemQuizQuestionSets.name.like(f"%{search_term}%"))
    result = await db.execute(query)
    question_set = result.scalars().all()
    return total, question_set


# 问题关联题集
async def question_set_relations(
        db: AsyncSession, question_ids: list, question_set_identifier: str, is_delete=False
):
    if is_delete:
        await db.execute(
            delete(SystemQuizQuestionSetRelationsV2).where(
                SystemQuizQuestionSetRelationsV2.question_set_identifier
                == question_set_identifier
            )
        )
    for question_id in question_ids:
        await db.execute(
            insert(SystemQuizQuestionSetRelationsV2).values(
                [
                    {
                        "question_set_identifier": question_set_identifier,
                        "question_id": question_id,
                    }
                ]
            )
        )
    await db.commit()
    await update_question_set_count(db, question_set_identifier)
    return True


# 更新问题集合数量
async def update_question_set_count(db: AsyncSession, question_set_identifier: str):
    query = select(SystemQuizQuestionSetRelationsV2).filter_by(
        question_set_identifier=question_set_identifier
    )
    result = await db.execute(query)
    questions_number = len(result.scalars().all())
    await db.execute(
        update(SystemQuizQuestionSets)
        .where(
            SystemQuizQuestionSets.question_set_identifier == question_set_identifier
        )
        .values(questions_number=questions_number)
    )
    await db.commit()
    return True


# 校验题目id列表中的问题是否存在
async def check_question_ids(db: AsyncSession, question_ids: list[str]):
    query = select(SystemQuizQuestions_V2.question_id).filter(
        SystemQuizQuestions_V2.question_id.in_(question_ids)
    )
    result = await db.execute(query)
    found_ids = {row[0] for row in result}
    return set(question_ids) == found_ids


# 校验题集是否存在
async def check_question_set_exists(db: AsyncSession, question_set_identifier: str):
    query = select(SystemQuizQuestionSets.question_set_identifier).filter_by(
        question_set_identifier=question_set_identifier
    )
    result = await db.execute(query)
    result = result.scalars().first()
    return result is None


# 获取题集详情
async def get_question_set_detail(
        db: AsyncSession,
        question_set_identifier: str,
        page: int,
        page_size: int,
        fetch_total: bool = False,
        search_term: str = None,
):
    question_set_result = await db.execute(
        select(
            SystemQuizQuestionSets
        )
        .filter(
            SystemQuizQuestionSets.question_set_identifier == question_set_identifier,
            SystemQuizQuestionSets.state != QuestionSetStatusEnum.get_enum_code(QuestionSetStatusEnum.DELETED),
        )
    )
    question_set_info = question_set_result.scalars().first()
    if question_set_info is None:
        return None

    question_set, creator_name, updater_name = question_set_info, question_set_info.created_user_id, question_set_info.updated_user_id

    question_relations_subquery = (
        select(SystemQuizQuestionSetRelationsV2.question_id)
        .filter_by(question_set_identifier=question_set_identifier)
        .subquery()
    )

    max_version_subquery = (
        select(
            SystemQuizQuestions_V2.question_id,
            func.max(SystemQuizQuestions_V2.version).label("max_version"),
        )
        .where(
            SystemQuizQuestions_V2.question_id.in_(
                select(question_relations_subquery.c.question_id)
            )
        )
        .group_by(SystemQuizQuestions_V2.question_id)
        .subquery()
    )

    base_query = select(SystemQuizQuestions_V2).join(
        max_version_subquery,
        and_(
            SystemQuizQuestions_V2.question_id == max_version_subquery.c.question_id,
            SystemQuizQuestions_V2.version == max_version_subquery.c.max_version,
            SystemQuizQuestions_V2.state != QuestionStatusEnum.get_enum_code(QuestionStatusEnum.DELETED),
        ),
    )

    if search_term:
        base_query = base_query.where(
            SystemQuizQuestions_V2.question.contains(search_term)
        )

    total = None
    if fetch_total:
        total_query = select(func.count()).select_from(base_query.subquery())
        total_result = await db.execute(total_query)
        total = total_result.scalar()

    questions_result = await db.execute(
        base_query.order_by(SystemQuizQuestions_V2.updated_at.desc())
        .offset((page - 1) * page_size)
        .limit(page_size)
    )
    questions = questions_result.scalars().all()

    created_str = question_set.created_at.strftime("%Y-%m-%d %H:%M:%S")
    updated_str = question_set.updated_at.strftime("%Y-%m-%d %H:%M:%S")

    return {
        "name": question_set.name,
        "desc": question_set.desc,
        "created_at": created_str,
        "updated_at": updated_str,
        "creator_name": creator_name,
        "updater_name": updater_name,
        "questions": questions,
        "total_count": total,
    }


async def get_system_quiz_question(db: AsyncSession, question_id: str):
    Creator = aliased(User, name="creator")
    Updater = aliased(User, name="updater")

    query = (
        select(
            SystemQuizQuestions_V2,
            Creator.full_name.label("created_by_full_name"),
            Updater.full_name.label("updated_by_full_name"),
        )
        .outerjoin(Creator, SystemQuizQuestions_V2.created_user_id == Creator.user_id)
        .outerjoin(Updater, SystemQuizQuestions_V2.updated_user_id == Updater.user_id)
        .filter(
            SystemQuizQuestions_V2.question_id == question_id,
            SystemQuizQuestions_V2.state != QuestionStatusEnum.get_enum_code(QuestionStatusEnum.DELETED),
        )
        .order_by(SystemQuizQuestions_V2.updated_at.desc())
    )

    result = await db.execute(query)
    row = result.fetchone()

    if row is None:
        return None
    return row


async def get_user_tasks_by_state(db: AsyncSession, state: int):
    query = select(UserQuizTasks).filter_by(task_state=state)
    result = await db.execute(query)
    user_task = result.scalars().all()
    return user_task


# 更新用户评测子任务状态
async def update_evaluation_task_info_state(
        db: AsyncSession,
        user_id: str,
        task_id: str,
        task_state: EvaluationSubTaskStatusEnum,
        is_commit=False,
):
    await db.execute(
        update(UserQuizTasksInfo)
        .where(UserQuizTasksInfo.user_id == user_id)
        .where(UserQuizTasksInfo.task_id == task_id)
        .values(task_state=task_state)
    )
    if is_commit:
        await db.commit()
    return True


# 更新用户评测主任务数量
async def update_evaluation_task_answer_num(
        db: AsyncSession, user_id: str, task_id: str, is_commit=False
):
    await db.execute(
        update(UserQuizTasks)
        .where(UserQuizTasks.user_id == user_id)
        .where(UserQuizTasks.task_id == task_id)
        .values(task_answer_num=UserQuizTasks.task_answer_num + 1)
    )
    if is_commit:
        await db.commit()
    return True


# 获取主任务 task_answer_num
async def get_evaluation_task_answer_num(db: AsyncSession, user_id: str, task_id: str):
    query = select(UserQuizTasks).filter_by(user_id=user_id, task_id=task_id)
    result = await db.execute(query)
    user_task = result.scalars().first()
    return user_task.task_num


# 根据问题id获取问题详情
async def get_question_by_id(db: AsyncSession, question_id: int):
    query = select(SystemQuizQuestions_V2).filter_by(id=question_id)
    result = await db.execute(query)
    question = result.scalars().first()
    return question


#
async def get_user_task_by_task_id(db: AsyncSession, task_id: str):
    query = select(UserQuizTasks).filter_by(task_id=task_id)
    result = await db.execute(query)
    user_task = result.scalars().first()
    return user_task


# 获取任务评测题
async def get_quiz_questions_by_task_id_and_user_id(
        db: AsyncSession, task_id: str, user_id: str
):
    query = (
        select(UserQuizTasksInfo)
        .join(UserQuizTasks, UserQuizTasks.task_id == UserQuizTasksInfo.parent_task_id)
        .join(
            SystemQuizQuestions_V2,
            SystemQuizQuestions_V2.id == UserQuizTasksInfo.question_id,
        )
        .filter(UserQuizTasksInfo.parent_task_id == task_id)
    )

    result = await db.execute(query)
    user_quiz_tasks_info_list = result.scalars().all()

    if len(user_quiz_tasks_info_list) == 0:
        return None

    seen = set()
    unique_question_ids = [
        info.question_id
        for info in sorted(user_quiz_tasks_info_list, key=lambda x: x.id)
        if info.question_id not in seen and not seen.add(info.question_id)
    ]
    question_query = select(SystemQuizQuestions_V2).filter(
        SystemQuizQuestions_V2.id.in_(unique_question_ids)
    )
    result = await db.execute(question_query)
    question_list = result.scalars().all()

    id_to_index = {qid: index for index, qid in enumerate(unique_question_ids)}
    sorted_question_list = sorted(question_list, key=lambda q: id_to_index[q.id])

    data = []
    for question in sorted_question_list:
        question_data = await assemble_data_for_question_v2(
            question, user_quiz_tasks_info_list, db
        )
        data.append(question_data)
    return data


async def get_quiz_questions_by_task_id_v2(db: AsyncSession, task_id: str):
    query = (
        select(UserQuizTasksInfo)
        .join(UserQuizTasks, UserQuizTasks.task_id == UserQuizTasksInfo.parent_task_id)
        .join(
            SystemQuizQuestions_V2,
            SystemQuizQuestions_V2.id == UserQuizTasksInfo.question_id,
        )
        .filter(UserQuizTasksInfo.parent_task_id == task_id)
    )

    result = await db.execute(query)
    user_quiz_tasks_info_list = result.scalars().all()

    if len(user_quiz_tasks_info_list) == 0:
        return None

    seen = set()
    unique_question_ids = [
        info.question_id
        for info in sorted(user_quiz_tasks_info_list, key=lambda x: x.id)
        if info.question_id not in seen and not seen.add(info.question_id)
    ]
    question_query = select(SystemQuizQuestions_V2).filter(
        SystemQuizQuestions_V2.id.in_(unique_question_ids)
    )
    result = await db.execute(question_query)
    question_list = result.scalars().all()

    id_to_index = {qid: index for index, qid in enumerate(unique_question_ids)}
    sorted_question_list = sorted(question_list, key=lambda q: id_to_index[q.id])

    single_round_questions = []
    multiple_round_questions = []
    for question in sorted_question_list:
        question_data = await assemble_data_for_question_v2(
            question, user_quiz_tasks_info_list, db
        )
        if question.is_multi_round_qa():
            multiple_round_questions.append(question_data)
        else:
            single_round_questions.append(question_data)
    return single_round_questions, multiple_round_questions


# 根据task_id 获取任务下评测标准完成列表
async def get_evaluation_task_criteria_info_by_task_id(db: AsyncSession, task_id: str):
    query = select(UserQuizTasksCriteriaInfo).filter_by(task_id=task_id)
    result = await db.execute(query)
    user_task_criteria_info = result.scalars().all()
    return user_task_criteria_info


# 查询SystemQuizQuestions所有问题
async def get_all_system_quiz_questions(db: AsyncSession):
    query = select(SystemQuizQuestions)
    result = await db.execute(query)
    questions = result.scalars().all()
    return questions


# 查询问题关联了哪些题集
async def get_question_set_by_question_id(db: AsyncSession, question_id: str) -> list:
    query = (
        select(
            SystemQuizQuestionSetRelationsV2.question_set_identifier,
            SystemQuizQuestionSets.name,
        )
        .join(
            SystemQuizQuestionSets,
            SystemQuizQuestionSetRelationsV2.question_set_identifier
            == SystemQuizQuestionSets.question_set_identifier,
        )
        .filter(
            SystemQuizQuestionSetRelationsV2.question_id == question_id,
            SystemQuizQuestionSets.state != QuestionSetStatusEnum.get_enum_code(QuestionSetStatusEnum.DELETED),
        )
    )

    result = await db.execute(query)
    results = result.all()
    if results:
        question_sets = [{"question_set_id": rel[0], "name": rel[1]} for rel in results]
    else:
        question_sets = []

    return question_sets


# 删除题集 delete_question_set
async def delete_question_set(db: AsyncSession, question_set_identifier: str):
    # state = 3
    await db.execute(
        update(SystemQuizQuestionSets)
        .where(
            SystemQuizQuestionSets.question_set_identifier == question_set_identifier
        )
        .values(state=3)
    )
    await db.commit()
    return True


# 获取题集
async def get_question_set_by_id(db: AsyncSession, question_set_identifier: str):
    query = select(SystemQuizQuestionSets).filter_by(
        question_set_identifier=question_set_identifier
    )
    result = await db.execute(query)
    question_set = result.scalars().first()
    return question_set


# 根据题集查id询关联的评测计划列表
async def get_evaluation_plan_by_question_set(db: AsyncSession, question_set_id: str):
    query = select(SystemQuizPlans).filter(
        SystemQuizPlans.state != QuizPlanDelEnum.get_enum_code(QuizPlanDelEnum.DELETED),
        SystemQuizPlans.question_set_id == question_set_id,
    )
    result = await db.execute(query)
    sys_task = result.scalars().all()
    return sys_task


# 查询计划详情
async def get_evaluation_plan_by_plan_id(db: AsyncSession, plan_id: str) -> SystemQuizPlans:
    result = await db.execute(select(SystemQuizPlans).filter(SystemQuizPlans.plan_id == plan_id))
    sys_task = result.scalars().first()
    return sys_task


# 查询题集列表关联了那些的任务
async def get_task_by_question_set_identifiers_and_unstart_task(
        db: AsyncSession, question_set_identifiers: list
):
    query = select(UserQuizTasks).filter(
        UserQuizTasks.question_set_identifier.in_(question_set_identifiers),
        UserQuizTasks.task_state != 0,
    )
    result = await db.execute(query)
    user_task = result.scalars().all()
    return user_task


async def get_available_menu(db: AsyncSession):
    query = select(Menu).filter_by(state=0)
    result = await db.execute(query)
    return result.scalars().all()


# 获取评测组列表
async def get_user_sets(
        db: AsyncSession,
        page: int,
        pagesize: int,
        fetch_total: bool = False,
        search_term: str = None,
        order_str: str = None,
):
    total = None
    if fetch_total:
        subquery = (
            select(
                SystemUserSets.name,
                func.max(SystemUserSets.version).label("max_version"),
            )
            .group_by(SystemUserSets.name)
            .subquery()
        )

        total_query = select(func.count()).select_from(
            join(
                SystemUserSets,
                subquery,
                and_(
                    SystemUserSets.name == subquery.c.name,
                    SystemUserSets.version == subquery.c.max_version,
                ),
            )
        )

        if search_term:
            total_query = total_query.filter(
                SystemUserSets.name.like(f"%{search_term}%")
            )

        total_result = await db.execute(total_query)
        total = total_result.scalar_one()

    subquery = (
        select(
            SystemUserSets.name, func.max(SystemUserSets.version).label("max_version")
        )
        .group_by(SystemUserSets.name)
        .subquery()
    )

    order = util.get_order(order_str, SystemUserSets)
    main_query = (
        select(
            SystemUserSets,
            func.count(SystemQuizUserSetRelations.id).label("related_count"),
        )
        .outerjoin(
            SystemQuizUserSetRelations,
            and_(
                SystemUserSets.user_set_identifier
                == SystemQuizUserSetRelations.user_set_identifier,
                SystemUserSets.version == SystemQuizUserSetRelations.version,
            ),
        )
        .join(
            subquery,
            and_(
                SystemUserSets.name == subquery.c.name,
                SystemUserSets.version == subquery.c.max_version,
            ),
        )
        .group_by(SystemUserSets.id)
        .order_by(order)
        .offset((page - 1) * pagesize)
        .limit(pagesize)
    )

    if search_term:
        main_query = main_query.filter(SystemUserSets.name.like(f"%{search_term}%"))

    result = await db.execute(main_query)
    user_sets_with_counts = result.all()

    user_sets = []
    for row in user_sets_with_counts:
        user_set = row[0]
        related_count = row[1]
        user_set.related_count = related_count
        user_sets.append(user_set)

    return total, user_sets


# 创建评测组
async def create_user_set(
        db: AsyncSession,
        name: str,
        description: Optional[str] = None,
        members: Optional[list] = None,
        created_user: str = None,
        user_set_identifier=None,
        updated_user: str = None,
        version: int = 0,
):
    if user_set_identifier is None:
        user_set_identifier = uuid.uuid1()
        version = 0
    user_set = SystemUserSets(
        user_set_identifier=user_set_identifier,
        name=name,
        description=description,
        created_user=created_user,
        updated_user=updated_user,
        version=version,
    )
    db.add(user_set)

    for member in members:
        user_set_member = SystemQuizUserSetRelations(
            user_set_identifier=user_set_identifier,
            user_id=member,
            version=version,
        )
        db.add(user_set_member)

    await db.commit()
    return True


# 查询评测组最新的版本
async def get_last_version_user_set(db: AsyncSession, user_set_identifier: str) -> SystemUserSets:
    query = (
        select(SystemUserSets)
        .filter(SystemUserSets.user_set_identifier == user_set_identifier)
        .filter(SystemUserSets.state != 3)
        .order_by(SystemUserSets.version.desc())
    )
    result = await db.execute(query)
    user_set = result.scalars().first()
    return user_set


# 更新评测组
async def update_user_set(db: AsyncSession, user_set_identifier: str, name: str, desc: str) -> SystemUserSets:
    query = (
        select(SystemUserSets)
        .filter(SystemUserSets.user_set_identifier == user_set_identifier)
        .order_by(SystemUserSets.version.desc())
    )
    result = await db.execute(query)
    user_set = result.scalars().first()
    logger.info(f"get user_set,user_set_identifier:{user_set_identifier},user_set:{user_set}")

    if user_set is None:
        raise Exception("评测组不存在")

    user_set.name = name
    user_set.description = desc
    user_set.updated_user = get_user_id_v2()
    user_set.updated_at = datetime.now()
    db.add(user_set)
    await db.commit()
    return user_set


# 更新评测组人
async def update_user_set_relations(db: AsyncSession, user_set_identifier: str, members: list[str]):
    # 删除
    await del_user_set_relations(db, user_set_identifier)
    # 添加
    for member in members:
        user_set_member = SystemQuizUserSetRelations(
            user_set_identifier=user_set_identifier,
            user_id=member,
            version=0,
        )
        db.add(user_set_member)
    await db.commit()


# 删除评测组人
async def del_user_set_relations(db: AsyncSession, user_set_identifier: str):
    logger.info(f"del_user_set_relations,user_set_identifier:{user_set_identifier}")
    await db.execute(
        delete(SystemQuizUserSetRelations).where(SystemQuizUserSetRelations.user_set_identifier == user_set_identifier))
    await db.commit()


# 评测组详情
async def get_user_set_detail(db: AsyncSession, user_set_identifier: str):
    query = (
        select(SystemUserSets)
        .filter(SystemUserSets.user_set_identifier == user_set_identifier)
        .order_by(SystemUserSets.id.desc())
    )
    result = await db.execute(query)
    user_set = result.scalars().first()
    if user_set is None:
        return None, None

    query = select(SystemQuizUserSetRelations).filter(
        SystemQuizUserSetRelations.user_set_identifier == user_set.user_set_identifier)
    result = await db.execute(query)
    user_set_members = result.scalars().all()
    user_set_members = [member.user_id for member in user_set_members]

    return user_set, user_set_members


async def get_user_set_relation(db: AsyncSession, user_set_identifiers: list[str]) -> dict:
    query = select(SystemQuizUserSetRelations).filter(
        SystemQuizUserSetRelations.user_set_identifier.in_(user_set_identifiers))
    result = await db.execute(query)
    user_set_members = result.scalars().all()

    result = {}
    for user_set_member in user_set_members:
        if user_set_member.user_set_identifier not in result:
            result[user_set_member.user_set_identifier] = [user_set_member.user_id]

        result[user_set_member.user_set_identifier].append(user_set_member.user_id)

    return result


# 评测组详情
async def get_user_set_detail_by_id(db: AsyncSession, user_set_id: int):
    query = (
        select(SystemUserSets)
        .filter(SystemUserSets.id == user_set_id)
        .filter(SystemUserSets.state != 3)
        .order_by(SystemUserSets.version.desc())
    )
    result = await db.execute(query)
    user_set = result.scalars().first()
    if user_set is None:
        return None, None

    query = select(SystemQuizUserSetRelations).filter(
        SystemQuizUserSetRelations.user_set_identifier == user_set.user_set_identifier,
        SystemQuizUserSetRelations.version == user_set.version,
    )
    result = await db.execute(query)
    user_set_members = result.scalars().all()
    user_set_members = [member.user_id for member in user_set_members]

    return user_set, user_set_members


# 获取题集详情
async def get_question_set_detail_by_id(
        db: AsyncSession,
        question_set_id: int,
) -> SystemQuizQuestionSets | None:
    query = select(SystemQuizQuestionSets).filter(and_(
        SystemQuizQuestionSets.id == question_set_id,
        SystemQuizQuestionSets.state == 1))

    result = await db.execute(query)
    return result.scalar_one_or_none()


async def get_question_relations_detail_by_id(
        db: AsyncSession,
        question_set_identifier: str,
) -> List[SystemQuizQuestionSetRelationsV2] | None:
    query = select(SystemQuizQuestionSetRelationsV2).where(
        SystemQuizQuestionSetRelationsV2.question_set_identifier == question_set_identifier)
    result = await db.execute(query)
    return result.scalars().all()


# 获取问题详情
async def get_system_quiz_question_details(db: AsyncSession, ids: List[str]) -> Sequence[SystemQuizQuestions]:
    query = select(SystemQuizQuestions_V2).filter(SystemQuizQuestions_V2.question_id.in_(ids))
    result = await db.execute(query)
    return result.scalars().all()

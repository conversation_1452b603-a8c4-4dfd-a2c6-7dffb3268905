from evaluation.share.util.plaintext_log import logger
from sqlalchemy import text, func, desc, bindparam, delete, or_
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import select, update

from evaluation.api.req.evaluation_model import UserTaskReq, UserTaskInfoReq
from evaluation.share.orm.model import UserQuizTasks, UserQuizTasksInfo, SystemQuizPlans
from evaluation.share.util import util


async def list_user_task_by_page(db: AsyncSession, userTask: UserTaskReq) -> (int, list):
    query = select(UserQuizTasks)
    if userTask.evaluation_id:
        query = query.where(UserQuizTasks.evaluation_id == userTask.evaluation_id)
    if userTask.status:
        query = query.where(UserQuizTasks.task_state.in_(userTask.status))
    if userTask.task_id:
        query = query.where(UserQuizTasks.task_id == userTask.task_id)
    if userTask.task_name:
        query = query.where(UserQuizTasks.task_name.like(f"%{userTask.task_name}%"))
    if userTask.user_id:
        query = query.where(UserQuizTasks.user_id == userTask.user_id)
    if userTask.admin_id and len(userTask.admin_id) > 0:
        query = query.where(UserQuizTasks.created_user.in_(userTask.admin_id))
    if userTask.keyword:
        query = query.where(
            or_(
                UserQuizTasks.task_name.like(f"%{userTask.keyword}%"),
                UserQuizTasks.id.like(f"%{userTask.keyword}%"),
            )
        )
    query = query.join(SystemQuizPlans, UserQuizTasks.evaluation_id == SystemQuizPlans.plan_id) \
        .where(SystemQuizPlans.deleted != 1)

    # 总数
    count_result = await db.execute(select(func.count()).select_from(query))
    total = count_result.scalar()
    if total is None:
        return 0, []

    if userTask.page and userTask.page_size:
        query = query.limit(userTask.page_size).offset(userTask.page_size * (userTask.page - 1))

    # 排序
    order = util.get_order(userTask.order_str, UserQuizTasks)
    query = query.order_by(order)
    # 查询
    execute = await db.execute(query)
    user_tasks = execute.scalars().all()

    return total, user_tasks


async def list_user_task(db: AsyncSession, userTask: UserTaskReq) -> list[UserQuizTasks]:
    query = select(UserQuizTasks)
    if userTask.evaluation_id:
        query = query.where(UserQuizTasks.evaluation_id == userTask.evaluation_id)
    if userTask.status:
        query = query.where(UserQuizTasks.task_state.in_(userTask.status))
    if userTask.task_id:
        query = query.where(UserQuizTasks.task_id == userTask.task_id)
    if userTask.task_name:
        query = query.where(UserQuizTasks.task_name.like(f"{userTask.task_name}"))
    if userTask.user_id:
        query = query.where(UserQuizTasks.user_id == userTask.user_id)
    if userTask.admin_id and len(userTask.admin_id) > 0:
        query = query.where(UserQuizTasks.created_user.in_(userTask.admin_id))
    if userTask.keyword:
        query = query.where(
            or_(
                UserQuizTasks.task_name.like(f"%{userTask.keyword}%"),
                UserQuizTasks.id.like(f"%{userTask.keyword}%"),
            )
        )

    # 执行查询
    execute = await db.execute(query.order_by(UserQuizTasks.id.desc()))

    # 获取所有结果
    user_tasks = execute.scalars().all()
    return user_tasks

# 删除用户任务
async def delete_user_task_by_evaluation_id(db: AsyncSession, evaluation_id: str):
    query = delete(UserQuizTasks).where(UserQuizTasks.evaluation_id == evaluation_id)
    try:
        await db.execute(query)
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise e


async def list_user_task_info(db: AsyncSession, userTaskInfoReq: UserTaskInfoReq) -> list[UserQuizTasksInfo]:
    query = select(UserQuizTasksInfo)
    if userTaskInfoReq.id:
        query = query.where(UserQuizTasksInfo.id == userTaskInfoReq.id)
    if userTaskInfoReq.parent_task_id:
        query = query.where(UserQuizTasksInfo.parent_task_id == userTaskInfoReq.parent_task_id)
    if userTaskInfoReq.task_id:
        query = query.where(UserQuizTasksInfo.task_id == userTaskInfoReq.task_id)
    if userTaskInfoReq.user_id:
        query = query.where(UserQuizTasksInfo.user_id == userTaskInfoReq.user_id)
    if userTaskInfoReq.evaluation_id:
        query = query.where(UserQuizTasksInfo.evaluation_id == userTaskInfoReq.evaluation_id)

    # 查询
    execute = await db.execute(query.order_by(UserQuizTasksInfo.id.desc()))
    user_task_infos = execute.scalars().all()
    return user_task_infos


# 删除用户任务
async def delete_user_task_info_by_evaluation_id(db: AsyncSession, evaluation_id: str):
    query = delete(UserQuizTasksInfo).where(UserQuizTasksInfo.evaluation_id == evaluation_id)
    try:
        await db.execute(query)
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise e


async def update_user_task_status(db: AsyncSession, task_id: str, task_status: int):
    task_query_exec = await db.execute(select(UserQuizTasks).filter_by(task_id=task_id))
    user_task = task_query_exec.scalars().first()
    if user_task is None:
        raise Exception(f"用户任务不存在:{task_id}")

    user_task.task_state = task_status
    try:
        await db.commit()
    except SQLAlchemyError as e:
        print(f"Database error occurred: {e}")
    except Exception as e:
        print(f"Unexpected error occurred: {e}")


async def update_user_task_info_status(db: AsyncSession, task_id: str, task_status: int):
    task_query_exec = await db.execute(select(UserQuizTasksInfo).filter_by(task_id=task_id))
    user_task_info = task_query_exec.scalars().first()
    if user_task_info is None:
        raise Exception(f"用户任务不存在:{task_id}")

    user_task_info.task_state = task_status
    try:
        await db.commit()
    except SQLAlchemyError as e:
        print(f"Database error occurred: {e}")
    except Exception as e:
        print(f"Unexpected error occurred: {e}")

import json
from typing import Optional

from sqlalchemy.exc import SQLAlchemyError

from evaluation.share.util.plaintext_log import logger
import uuid

from sqlalchemy import text, func, desc, bindparam, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import select, update
from starlette.requests import Request

from evaluation.constants.status_enum import (QuizPlanStatusEnum, QuizTypeEnum, QuizPlanDelEnum)
from evaluation.api.req.evaluation_model import QuizPlanReq, UserSetObj, UserInfo, QueryPlanReq
from evaluation.share.orm.model import (
    SystemQuizCriterias,
    SystemQuizCriteriaSets,
    SystemQuizCriteriaSetRelations,
    SystemQuizPlans,
    SystemQuizUserSetRelations,
    SystemUserSets,
    SystemQuizQuestionSets,
    User,
)
from evaluation.share.util import plaintext_log as log
from evaluation.share.util.user import get_user_name, get_user_id_v2, get_user_full_name
from evaluation.share.util import util

async def get_quiz_dim_sets(db: AsyncSession, req: Request):
    # 获取评测维度集合
    query = select(SystemQuizCriteriaSets.criteria_set_identifier)
    result = await db.execute(query)
    record = list()
    rows = result.fetchall()
    for row in rows:
        record.append(row[0])
    return record


async def get_quiz_criteria(db: AsyncSession, dim_set_id: int):
    # 获取评测维度集合的具体标准
    select_filter = text("1=1")
    if dim_set_id:
        dim_set_id = int(dim_set_id)
        select_filter = SystemQuizCriteriaSets.id == dim_set_id

    query = (
        select(
            SystemQuizCriterias, SystemQuizCriteriaSetRelations, SystemQuizCriteriaSets
        )
        .join(
            SystemQuizCriteriaSetRelations,
            SystemQuizCriterias.id == SystemQuizCriteriaSetRelations.criteria_id,
            isouter=True,
        )
        .join(
            SystemQuizCriteriaSets,
            SystemQuizCriteriaSets.criteria_set_identifier
            == SystemQuizCriteriaSetRelations.criteria_set_identifier,
            isouter=True,
        )
        .where(select_filter)
        .order_by(SystemQuizCriterias.id)
    )
    result = await db.execute(query)
    return result.fetchall()


async def get_quiz_user_sets(db: AsyncSession, req: Request):
    # 获取评测用户集合
    query = select(SystemUserSets)
    result = await db.execute(query)
    record = list()
    rows = result.scalars().all()
    for row in rows:
        record.append(
            UserSetObj(
                id=row.id, name=row.name, user_set_identifier=row.user_set_identifier
            )
        )
    return record


async def get_quiz_user_sets_detail(
        db: AsyncSession,
        set_name: str,
        quiz_type: str,
):
    # 获取评测用户集合及用户成员
    select_filter = text("1=1")
    if set_name:
        select_filter = SystemUserSets.name == set_name
    if quiz_type:
        select_filter = SystemUserSets.quiz_type == quiz_type
    if set_name and quiz_type:
        select_filter = (SystemUserSets.name == set_name) & (
                SystemUserSets.quiz_type == quiz_type
        )

    query = (
        select(
            SystemUserSets.id,
            SystemUserSets.name,
            SystemUserSets.user_set_identifier,
            User.user_id,
            User.name,
            User.full_name,
            User.department,
        )
        .join_from(
            SystemUserSets,
            SystemQuizUserSetRelations,
            SystemUserSets.user_set_identifier
            == SystemQuizUserSetRelations.user_set_identifier,
        )
        .join(User, User.user_id == SystemQuizUserSetRelations.user_id)
        .where(select_filter)
        .order_by(SystemUserSets.name)
    )
    result = await db.execute(query)
    record = list()
    rows = result.fetchall()
    # 创建一个字典来存储每个用户集的信息
    user_sets = {}
    for row in rows:
        # 如果用户集还没有被添加到字典中，创建一个新的条目
        if row.user_set_identifier not in user_sets:
            user_sets[row.user_set_identifier] = UserSetObj(
                id=row.id,
                user_set_identifier=row.user_set_identifier,
                name=row.name,
                user_info=[],
            )
        # 添加用户信息到用户集中
        user_sets[row.user_set_identifier].user_info.append(
            UserInfo(
                user_id=row.user_id, full_name=row.full_name, department=row.department
            )
        )

    # 将字典转换为列表
    data = list(user_sets.values())
    return data


async def get_latest_version_user_set_identifier(db: AsyncSession, user_set_id: int):
    user_set_identifier_query = (
        select(SystemUserSets.user_set_identifier)
        .where(SystemUserSets.id == user_set_id)
        .limit(1)
    )
    user_set_identifier = await db.scalar(user_set_identifier_query)

    if user_set_identifier is None:
        return None, None

    latest_version_query = (
        select(func.max(SystemUserSets.version))
        .where(SystemUserSets.user_set_identifier == user_set_identifier)
    )
    latest_version = await db.scalar(latest_version_query)

    return user_set_identifier, latest_version


async def get_quiz_set_user(db: AsyncSession, user_set_id: int):
    user_set_identifier, latest_version = await get_latest_version_user_set_identifier(db, user_set_id)

    if user_set_identifier is None or latest_version is None:
        return []

    query = (
        select(SystemQuizUserSetRelations)
        .where(
            (SystemQuizUserSetRelations.user_set_identifier == user_set_identifier) &
            (SystemQuizUserSetRelations.version == latest_version)
        )
    )

    result = await db.execute(query)
    relations_list = result.scalars().all()
    return relations_list


async def get_user_info(db: AsyncSession, page_size: int, page: int):
    # 获取用于评测的所有用户信息
    query = select(User).limit(page_size).offset(page_size * (page - 1))
    result = await db.execute(query)
    users = result.scalars().all()

    # 获取总的记录数
    count_query = select(func.count()).select_from(User)
    count_result = await db.execute(count_query)
    total = count_result.scalar_one()

    return users, total


# 创建评测计划
async def create_quiz_plan(db: AsyncSession, val: QuizPlanReq):
    # 验证评测计划名称是否存在
    check_query = (
        select(SystemQuizPlans).where(SystemQuizPlans.plan_name == val.plan_name).where(SystemQuizPlans.deleted != 1))
    check_result = await db.execute(check_query)
    check_plan = check_result.scalars().first()
    if check_plan:
        raise Exception(f"评测计划名称:{val.plan_name}已存在")

    message = ''
    try:
        quiz_plan = SystemQuizPlans(
            plan_id=str(uuid.uuid4()),
            plan_name=val.plan_name,
            quiz_type=QuizTypeEnum.get_enum_code(QuizTypeEnum.ART),
            desc=val.desc,
            dim_set_id=val.dim_set_id,
            question_set_id=val.question_set_id,
            user_set_id=val.user_set_id,
            created_user=get_user_full_name(),
            updated_user=get_user_full_name(),
            model_name=val.model_name,
            model_id=val.model_id,
            model_parameters=val.model_parameters,
            gpu_type=val.gpu_type,
            replica_num=val.replica_num,
            replica_gpu_num=val.replica_gpu_num,
            resource_group=val.resource_group,
            resource_pool=val.resource_pool,
            state=QuizPlanStatusEnum.get_enum_code(QuizPlanStatusEnum.UNPUBLISHED),
            deleted=0,
            create_user_id=get_user_id_v2(),
            model_type=val.model_type

        )
        db.add(quiz_plan)
        await db.commit()
        await db.refresh(quiz_plan)
    except Exception as e:
        quiz_plan = None
        message = f"create quiz plan failed, error: {e}"
        logger.error(message)
        raise e
    return quiz_plan, message


async def update_quiz_plan(db: AsyncSession, val: QuizPlanReq):
    # 验证评测计划名称是否存在
    check_query = (
        select(SystemQuizPlans).where(SystemQuizPlans.plan_name == val.plan_name).where(SystemQuizPlans.deleted != 1)
        .where(SystemQuizPlans.state != 7))
    check_result = await db.execute(check_query)
    check_plan = check_result.scalars().first()
    if check_plan and check_plan.plan_id != val.plan_id:
        raise Exception(f"评测计划名称:{val.plan_name}已存在")

    # 更新评测计划
    stmt = (
        update(SystemQuizPlans)
        .where(SystemQuizPlans.plan_id == val.plan_id)  # 指定要更新的记录
        .values(  # 设置要更新的字段
            plan_name=val.plan_name,
            desc=val.desc,
            dim_set_id=val.dim_set_id,
            question_set_id=val.question_set_id,
            user_set_id=val.user_set_id,
            updated_user=get_user_full_name(),  # 假设QuizPlanReq有一个updated_user字段
            model_id=val.model_id,
            model_name=val.model_name,
            model_parameters=val.model_parameters,
            gpu_type=val.gpu_type,
            replica_num=val.replica_num,
            replica_gpu_num=val.replica_gpu_num,
            resource_group=val.resource_group,
            resource_pool=val.resource_pool,
            update_user_id=get_user_id_v2(),
            state=0,
            model_type=val.model_type
        )
    )
    try:
        await db.execute(stmt)
        await db.commit()
        return True
    except SQLAlchemyError as e:
        print(f"Database error occurred: {e}")
    except Exception as e:
        print(f"Unexpected error occurred: {e}")
    return False


async def get_quiz_plan(db: AsyncSession, plan_id: str) -> Optional[SystemQuizPlans]:
    try:
        # 构建查询以通过 ID 选择 quiz plan
        query = select(SystemQuizPlans).where(SystemQuizPlans.plan_id == plan_id).where(SystemQuizPlans.deleted != 1)
        # 执行查询
        result = await db.execute(query)
        # 获取第一个结果
        return result.scalars().first()
    except SQLAlchemyError as e:
        # 记录异常或根据需要处理
        print(f"发生错误: {e}")
        return None


async def get_quiz_plan_by_plan_ids(db: AsyncSession, plan_ids: list[str]) -> list[SystemQuizPlans]:
    if len(plan_ids) == 0:
        return []
    try:
        # 构建查询以通过 ID 选择 quiz plan
        query = select(SystemQuizPlans).where(SystemQuizPlans.plan_id.in_(plan_ids)).where(SystemQuizPlans.deleted != 1)
        # 执行查询
        result = await db.execute(query)
        # 获取第一个结果
        return result.scalars().all()
    except SQLAlchemyError as e:
        # 记录异常或根据需要处理
        print(f"发生错误: {e}")
        return None


async def get_quiz_plan_list(db: AsyncSession, queryReq: QueryPlanReq) -> (list, int):
    # 获取总的记录数
    base_query = select(SystemQuizPlans). \
        where(SystemQuizPlans.deleted != QuizPlanDelEnum.get_enum_code(QuizPlanDelEnum.DELETED))
    if queryReq.model_name:
        base_query = base_query.where(SystemQuizPlans.model_name == queryReq.model_name)
    if queryReq.question_set_id:
        base_query = base_query.where(SystemQuizPlans.question_set_id == queryReq.question_set_id)
    if queryReq.user_set_id:
        base_query = base_query.where(SystemQuizPlans.user_set_id == queryReq.user_set_id)
    if queryReq.status:
        base_query = base_query.where(SystemQuizPlans.state.in_(queryReq.status))
    if queryReq.resource_group:
        base_query = base_query.where(SystemQuizPlans.resource_group == queryReq.resource_group)
    if queryReq.resource_pool:
        base_query = base_query.where(SystemQuizPlans.resource_pool == queryReq.resource_pool)
    if queryReq.keyword:
        base_query = base_query.where(or_(SystemQuizPlans.plan_name.like(f"%{queryReq.keyword}%"),
                                          SystemQuizPlans.id.like(f"%{queryReq.keyword}%")))
    if queryReq.create_user_id:
        base_query = base_query.where(SystemQuizPlans.create_user_id == queryReq.create_user_id)
    if queryReq.plan_id:
        base_query = base_query.where(SystemQuizPlans.plan_id == queryReq.plan_id)
    if queryReq.user_full_names:
        base_query = base_query.where(SystemQuizPlans.created_user.in_(queryReq.user_full_names))

    # 执行查询并获取总数
    count_result = await db.execute(select(func.count()).select_from(base_query))
    total = count_result.scalar()
    if total <= 0:
        return [], total

    # 获取评测计划列表
    if queryReq.page and queryReq.page_size:
        base_query = base_query.limit(queryReq.page_size).offset(queryReq.page_size * (queryReq.page - 1))

    # 排序
    order = util.get_order(queryReq.order_str, SystemQuizPlans)
    base_query = base_query.order_by(order)
    # 执行查询
    result = await db.execute(base_query)
    plan_list = result.scalars().all()

    return plan_list, total


async def get_criteria_set(db: AsyncSession, dim_set_id: int):
    # 获取评测维度集合
    query = select(SystemQuizCriteriaSets).where(
        SystemQuizCriteriaSets.id == dim_set_id
    )
    result = await db.execute(query)
    return result.scalars().first()


async def get_q_set(db: AsyncSession) -> dict[int, str]:
    # 获取题集集合 dict
    query = select(SystemQuizQuestionSets)
    result = await db.execute(query)
    data = result.scalars().all()
    set_dict = {}
    for row in data:
        set_dict[row.id] = row.name
    return set_dict


async def get_q_identifier_set(db: AsyncSession) -> dict[int, str]:
    # 获取题集集合 dict
    query = select(SystemQuizQuestionSets)
    result = await db.execute(query)
    data = result.scalars().all()
    set_dict = {}
    for row in data:
        set_dict[row.id] = row.question_set_identifier
    return set_dict


async def get_q_identifier_name_set(db: AsyncSession) -> dict[str, str]:
    # 获取题集集合 dict
    query = select(SystemQuizQuestionSets)
    result = await db.execute(query)
    data = result.scalars().all()
    set_dict = {}
    for row in data:
        set_dict[row.question_set_identifier] = row.name
    return set_dict


async def get_user_set(db: AsyncSession) -> dict[int, str]:
    # 获取评测人员集合 dict
    query = select(SystemUserSets)
    result = await db.execute(query)
    data = result.scalars().all()
    set_dict = {}
    for row in data:
        set_dict[row.id] = row.user_set_identifier
    return set_dict


async def get_dim_set(db: AsyncSession) -> dict[int, str]:
    # 获取评测维度集合 dict
    query = select(SystemQuizCriteriaSets)
    result = await db.execute(query)
    data = result.scalars().all()
    set_dict = {}
    for row in data:
        set_dict[row.id] = row.criteria_set_identifier
    return set_dict


# 发布评测计划
async def publish_quiz_plan(db: AsyncSession, plan_id: str):
    login_user = get_user_full_name()
    stmt = (
        update(SystemQuizPlans)
        .where(SystemQuizPlans.plan_id == plan_id)
        .values(state=QuizPlanStatusEnum.get_enum_code(QuizPlanStatusEnum.QUEUING), updated_user=login_user)
    )
    try:
        await db.execute(stmt)
        await db.commit()
        return True
    except Exception as e:
        log.logger.error(f"publish quiz plan failed, error: {e}")
        return False


# 更新计划状态
# 0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束
async def update_quiz_plan_status(db: AsyncSession, plan_id: str, status: int, error_msg: Optional[str] = None):
    stmt = (
        update(SystemQuizPlans)
        .where(SystemQuizPlans.plan_id == plan_id)
        .values(state=status, error_msg=error_msg)
    )
    try:
        await db.execute(stmt)
        await db.commit()
        return True
    except Exception as e:
        log.logger.error(f"publish quiz plan failed, error: {e}")
        return False


# 删除评测计划
async def delete_quiz_plan(db: AsyncSession, plan_id: str):
    stmt = (
        update(SystemQuizPlans)
        .where(SystemQuizPlans.plan_id == plan_id)
        .values(deleted=QuizPlanDelEnum.get_enum_code(QuizPlanDelEnum.DELETED), updated_user=get_user_full_name(),
                update_user_id=get_user_id_v2())
    )
    await db.execute(stmt)
    await db.commit()
    return True


async def get_quiz_plan_users(db: AsyncSession):
    # query = select(SystemQuizPlans.created_user, SystemQuizPlans.updated_user).distinct()
    # result = await db.execute(query)
    # users = set()
    # for row in result.fetchall():
    #     users.add(row.created_user)
    #     users.add(row.updated_user)
    # return list(users)
    query = select(SystemQuizPlans.created_user).distinct()
    result = await db.execute(query)
    users = [row.created_user for row in result.fetchall()]
    return users

import logging
from typing import Callable, AsyncGenerator, Optional

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio.engine import AsyncEngine

from evaluation.share.util import aes_utils
from evaluation.share.util.app_config import CONFIG
from evaluation.share.util.plaintext_log import logger

from urllib.parse import quote_plus

# 数据库连接配置
SQLALCHEMY_DATABASE_URI = (
    "mysql+aiomysql://root:123456@localhost/fastapi_db?charset=utf8mb4"
    #                用户:密码@服务器/数据库?参数
)

Base = declarative_base()
logger = logging.getLogger("plaintext")

engine: Optional[AsyncEngine]

async_session: Callable[..., AsyncSession]


def init():
    global engine
    global async_session

    db_username = CONFIG["mysql"]["db_username"]
    db_password = CONFIG["mysql"]["db_password"]
    db_host = CONFIG["mysql"]["db_host"]
    db_port = CONFIG["mysql"]["db_port"]
    db_name = CONFIG["mysql"]["db_name"]
    decrypt_aes_key = CONFIG["mysql"]["decrypt_aes_key"]
    print(
        f"db_username={db_username}, db_password={db_password}, db_host={db_host}, db_port={db_port}, db_name={db_name}")

    db_password = quote_plus(aes_utils.decrypt_aes(decrypt_aes_key, db_password))
    dsn = f"mysql+aiomysql://{db_username}:{db_password}@{db_host}:{db_port}/{db_name}?charset=utf8mb4"
    # dsn = f"mysql+aiomysql://{db_username}:{db_password}@{db_host}:{db_port}/{db_name}?charset=utf8mb4"
    # print(f"dsn={dsn}")

    # 创建数据库引擎
    engine = create_async_engine(
        dsn,
        pool_size=100,
        max_overflow=10,
        pool_recycle=3600,  # mysql配置当中默认连接超过8小时连接就是失效断开
        pool_pre_ping=True
        # echo=True
    )

    # class_=AsyncSession, 有静态分析告警
    async_session = sessionmaker(bind=engine, expire_on_commit=False, class_=AsyncSession)

    logger.info(f"mysql, dsn={dsn}")


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with async_session() as session:
        yield session


def init_engine(dsn: str):
    return create_async_engine(dsn, pool_size=100, max_overflow=10, echo=True)


def init_session_maker(eg):
    return sessionmaker(bind=eg, expire_on_commit=False, class_=AsyncSession)

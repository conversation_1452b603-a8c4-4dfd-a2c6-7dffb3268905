import logging
from typing import Optional

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy import select, update, delete

from evaluation.share.orm.model import InferenceService

logger = logging.getLogger("plaintext")


# 更新推理服务状态
# 推理服务状态 1-更新中 2-运行中 3-下线中 4-已下线 5-异常
async def update_inference_service_state(db: AsyncSession, plan_id: str, state: int):
    stmt = update(InferenceService).where(InferenceService.plan_id == plan_id).values(inference_state=state)
    try:
        await db.execute(stmt)
        await db.commit()
    except SQLAlchemyError as e:
        logger.info(f"Database error occurred: {e}")
        raise e
    except Exception as e:
        logger.info(f"Unexpected error occurred: {e}")
        raise e


# state 推理服务状态 1-更新中 2-运行中 3-下线中 4-已下线 5-异常
async def update_inference_service_url(db: AsyncSession, plan_id: str, state: int, internal_url_: str,
                                       api_address_: str):
    stmt = update(InferenceService).where(InferenceService.plan_id == plan_id).values(inference_state=state,
                                                                                      internal_host=internal_url_,
                                                                                      external_path=api_address_)
    try:
        await db.execute(stmt)
        await db.commit()
    except SQLAlchemyError as e:
        logger.info(f"Database error occurred: {e}")
        raise e
    except Exception as e:
        logger.info(f"Unexpected error occurred: {e}")
        raise e


# 创建推理服务
async def create_inference_service(db: AsyncSession, plan_id: str, plan_name: str, model_id: int, model_name: str,
                                   service_id: int, state: int, model_type: int):
    inference_service = InferenceService(plan_id=plan_id, plan_name=plan_name, model_id=model_id, model_name=model_name,
                                         inference_service_id=service_id, inference_state=state, model_type=model_type)
    try:
        db.add(inference_service)
        await db.commit()
        await db.refresh(inference_service)
    except SQLAlchemyError as e:
        logger.info(f"Database error occurred: {e}")
        raise e
    except Exception as e:
        logger.info(f"Unexpected error occurred: {e}")
        raise e


# 删除评测计划
async def delete_inference_service(db: AsyncSession, plan_id: str):
    query = delete(InferenceService).filter(InferenceService.plan_id == plan_id)
    try:
        await db.execute(query)
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise e


# 查询推理服务
async def list_inference_services(db: AsyncSession, plan_id: Optional[str], model_id: Optional[int],
                                  service_id: Optional[int], states: Optional[list[int]]) -> list[InferenceService]:
    query = select(InferenceService)
    if plan_id:
        query = query.where(InferenceService.plan_id == plan_id)
    if model_id:
        query = query.where(InferenceService.model_id == model_id)
    if service_id:
        query = query.where(InferenceService.inference_service_id == service_id)
    if states and len(states) > 0:
        query = query.where(InferenceService.inference_state.in_(states))

    execute = await db.execute(query)
    return execute.scalars().all()

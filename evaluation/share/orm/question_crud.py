from sqlalchemy import text, func, desc, bindparam
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import select, update

from evaluation.share.orm.model import SystemQuizQuestionSets, SystemQuizQuestions, SystemQuizQuestionSetRelationsV2, \
    SystemQuizQuestions_V2
from evaluation.vo.question_vo import QuestionPlanVo


# 获取题集
async def get_question_set_by_id(db: AsyncSession, question_set_id: int):
    query = select(SystemQuizQuestionSets).filter_by(
        id=question_set_id
    )
    result = await db.execute(query)
    question_set = result.scalars().first()
    return question_set


# 获取题集
async def get_question_set_by_question_set_identifier(db: AsyncSession, question_set_identifier: str):
    query = select(SystemQuizQuestionSets).filter_by(
        question_set_identifier=question_set_identifier
    )
    result = await db.execute(query)
    question_set = result.scalars().first()
    return question_set


# 按照名称搜索，题集名称唯一
async def get_question_set_by_name(db: AsyncSession, name: str):
    query = select(SystemQuizQuestionSets).where(SystemQuizQuestionSets.name == name)
    result = await db.execute(query)
    question_set = result.scalars().first()
    return question_set


async def get_question_set_by_ids(db: AsyncSession, question_ids: list[int]) -> [SystemQuizQuestions_V2]:
    query = select(SystemQuizQuestions_V2).where(
        SystemQuizQuestions_V2.id.in_(question_ids)
    )
    result = await db.execute(query)
    questions = result.scalars().all()
    return questions


# 问题列表
async def list_question_details(db: AsyncSession, question_set_id: int) -> list[SystemQuizQuestions]:
    query = (select(SystemQuizQuestions_V2)
    .join(
        SystemQuizQuestionSetRelationsV2,
        SystemQuizQuestionSetRelationsV2.question_id == SystemQuizQuestions_V2.question_id)
    .join(
        SystemQuizQuestionSets,
        SystemQuizQuestionSets.question_set_identifier == SystemQuizQuestionSetRelationsV2.question_set_identifier, )
    .where(
        SystemQuizQuestionSets.id == question_set_id)
    )

    exec_result = await db.execute(query)
    return exec_result.scalars().all()


async def list_question_dict(db: AsyncSession, question_set_id: int) -> dict[int, str]:
    questions = await list_question_details(db, question_set_id)
    return {q.id: q for q in questions}


async def list_question_info_by_id(db: AsyncSession, question_id: str) -> list[SystemQuizQuestions_V2]:
    query = select(SystemQuizQuestions_V2).where(SystemQuizQuestions_V2.question_id == question_id)
    exec_result = await db.execute(query)
    return exec_result.scalars().first()


# 查询题目关联的计划
sql = text("""

""")


async def list_question_plan_relation(db: AsyncSession, question_id: str) -> list[
    QuestionPlanVo]:
    raw_sql = """
    SELECT t1.plan_id,t1.plan_name,t1.state,t4.question_id,t4.question from system_quiz_plan t1 
LEFT JOIN system_quiz_question_sets t2 on t2.id = t1.question_set_id
LEFT JOIN system_quiz_question_set_relations_v2 t3 on t3.question_set_identifier=t2.question_set_identifier
LEFT JOIN system_quiz_questions_v2 t4 on t4.question_id=t3.question_id
WHERE t4.question_id = :question_id 
     """

    # Execute the raw SQL
    sql = text(raw_sql)
    query_exec = await db.execute(sql,
                                  {"question_id": question_id})

    # Execute the raw SQL
    results = [
        QuestionPlanVo(
            plan_id=row.plan_id,
            plan_name=row.plan_name,
            plan_status=row.state,
            question_id=row.question_id,
            question=row.question,
        )
        for row in query_exec
    ]
    return results

from typing import Optional, List, Sequence
from sqlalchemy import func, join, select, and_, or_, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from evaluation.share.orm.model import SystemUserSets, SystemQuizUserSetRelations


# 查询用户集合
async def get_user_set_by_id(db: AsyncSession, user_set_id: int) -> SystemUserSets:
    query = select(SystemUserSets).filter_by(id=user_set_id)
    result = await db.execute(query)
    return result.scalars().first()


# 查询用户集合
async def get_user_set_by_identifiers(db: AsyncSession, user_set_identifiers: List[str]) -> List[SystemUserSets]:
    query = select(SystemUserSets).where(SystemUserSets.user_set_identifier.in_(user_set_identifiers))
    result = await db.execute(query)
    return result.scalars().all()


async def get_user_set_by_name(db: AsyncSession, name: str) -> List[SystemUserSets]:
    query = select(SystemUserSets).where(SystemUserSets.name == name)
    result = await db.execute(query)
    return result.scalars().all()


# 查询用户集合关系
async def get_user_set_relations(db: AsyncSession, user_set_identifier: str) -> Sequence[SystemQuizUserSetRelations]:
    query = select(SystemQuizUserSetRelations).filter_by(user_set_identifier=user_set_identifier)
    result = await db.execute(query)
    return result.scalars().all()


# 查询用户id
async def get_user_id_array(db: AsyncSession, user_set_identifier: str) -> list[str]:
    user_set_relations = await get_user_set_relations(db, user_set_identifier)
    user_id_array = []
    for user_set_relation in user_set_relations:
        user_id_array.append(user_set_relation.user_id)

    return user_id_array


# 删除用户组
async def delete_user_set(db, user_set_identifiers: list[str]):
    query = delete(SystemUserSets).where(SystemUserSets.user_set_identifier.in_(user_set_identifiers))
    try:
        await db.execute(query)
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise e


# 删除用户组关系
async def delete_user_set_relations(db, user_set_identifiers: list[str]):
    query = delete(SystemQuizUserSetRelations).where(
        SystemQuizUserSetRelations.user_set_identifier.in_(user_set_identifiers))
    try:
        await db.execute(query)
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise e

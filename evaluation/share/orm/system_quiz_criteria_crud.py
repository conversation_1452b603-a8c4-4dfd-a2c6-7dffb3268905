import logging

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import select
from evaluation.share.orm.model import  SystemQuizCriterias, SystemQuizCriteriaSetRelations, \
    SystemQuizCriteriaSets, UserQuizTasksCriteriaInfo, model_to_dict

logger = logging.getLogger("plaintext")

# 获取评分标准集
async def get_criteria_sets_by_id(db: AsyncSession, criteria_set_id: int) -> SystemQuizCriteriaSets:
    query = select(SystemQuizCriteriaSets).filter_by(id=criteria_set_id)
    execute = await db.execute(query)
    return execute.scalars().first()


# 获取评测标准
async def get_criteria_list_by_criteria_set_identifier(
        db: AsyncSession, criteria_set_id: int
):
    query = (
        select(SystemQuizCriterias)
        .join(
            SystemQuizCriteriaSetRelations,
            SystemQuizCriteriaSetRelations.criteria_id == SystemQuizCriterias.id,
        )
        .join(
            SystemQuizCriteriaSets,
            SystemQuizCriteriaSets.criteria_set_identifier
            == SystemQuizCriteriaSetRelations.criteria_set_identifier,
        )
        .filter(
            SystemQuizCriteriaSets.id == criteria_set_id
        )
    )
    result = await db.execute(query)
    criteria_list = result.scalars().all()

    return criteria_list


# 添加用户评分明细
async def generate_user_evaluation_task(db, user_task_criteria_info: UserQuizTasksCriteriaInfo):
    logger.info(f"add user_task_criteria_info:{model_to_dict(user_task_criteria_info)}")
    db.add(user_task_criteria_info)
    await db.commit()

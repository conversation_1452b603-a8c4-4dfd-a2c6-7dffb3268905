from datetime import datetime

from sqlalchemy.sql import select, update
from evaluation.share.orm import database, model
from evaluation.share.orm.platform_schema import User


async def create_user_if_not_exists(user: User):
    async with database.async_session() as db:
        query = select(model.User).filter_by(user_id=user.user_id)
        result = await db.execute(query)
        existing_user = result.scalars().first()
        if existing_user:
            await db.execute(
                update(model.User)
                .where(model.User.user_id == user.user_id)
                .values(permission=user.permission, updated_at=datetime.now())
            )
        else:
            db.add(
                model.User(
                    user_id=user.user_id,
                    name=user.name,
                    full_name=user.full_name,
                    department=user.department,
                    permission=user.permission,
                )
            )
        await db.commit()



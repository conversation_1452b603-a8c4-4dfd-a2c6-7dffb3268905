from typing import List, Any, Sequence

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.testing import db
from typing import Optional, List, Sequence
from sqlalchemy import select, and_, or_, update, delete

from evaluation.share.orm.model import SystemQuizPlanTask, SystemQuizPlanTaskInfo, UserQuizTasks, SystemQuizCriteriaSets


# 添加任务
async def generate_eval_task(
        db: AsyncSession,
        system_quiz_plan_task: SystemQuizPlanTask
) -> SystemQuizPlanTask | None:
    try:
        db.add(system_quiz_plan_task)
        await db.commit()
        await db.refresh(system_quiz_plan_task)
        return system_quiz_plan_task
    except Exception as e:
        await db.rollback()  # 回滚事务
        raise e  # 或者处理异常


# 删除
async def del_eval_task(
        db: AsyncSession,
        evaluation_id: str
):
    query = delete(SystemQuizPlanTask).where(SystemQuizPlanTask.evaluation_id == evaluation_id)
    try:
        await db.execute(query)
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise e


# 查询任务
# evaluation_id 计划id（plan_id）
async def get_eval_task(db: AsyncSession, evaluation_id: str) -> SystemQuizPlanTask | None:
    query = select(SystemQuizPlanTask).filter(SystemQuizPlanTask.evaluation_id == evaluation_id)
    result = await db.execute(query)
    return result.scalars().first()


# 批量创建评测任务明细
async def batch_create_evaluation_task_info(
        db: AsyncSession,
        user_task_infos: List[SystemQuizPlanTaskInfo],
):
    if not user_task_infos:
        return  # 如果列表为空，直接返回

    db.add_all(user_task_infos)

    try:
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise e


# 删除任务明细
async def del_eval_task_detail(
        db: AsyncSession,
        evaluation_id: str
):
    query = delete(SystemQuizPlanTaskInfo).filter(SystemQuizPlanTaskInfo.evaluation_id == evaluation_id)
    try:
        await db.execute(query)
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise e


# 查询任务明细
async def get_eval_task_detail(db: AsyncSession, parent_task_id: str) -> Sequence[List[SystemQuizPlanTaskInfo]]:
    query = (select(SystemQuizPlanTaskInfo).where(SystemQuizPlanTaskInfo.parent_task_id == parent_task_id)
             .order_by(SystemQuizPlanTaskInfo.id.desc()))
    result = await db.execute(query)
    return result.scalars().all()


# 查询任务明细
async def get_eval_task_detail_by_evaluation_id(db: AsyncSession, evaluation_id: str) -> Sequence[
    List[SystemQuizPlanTaskInfo]]:
    query = (select(SystemQuizPlanTaskInfo).where(SystemQuizPlanTaskInfo.evaluation_id == evaluation_id)
             .order_by(SystemQuizPlanTaskInfo.id.desc()))
    result = await db.execute(query)
    return result.scalars().all()


# 更新推理结果
async def update_task_detail_sys_answer(db: AsyncSession, task_id: str, sys_answer: Optional[str],
                                        trace_id: Optional[str]):
    stmt = update(SystemQuizPlanTaskInfo).where(SystemQuizPlanTaskInfo.task_id == task_id).values(
        system_answer=sys_answer, trace_id=trace_id)
    try:
        await db.execute(stmt)
        await db.commit()
    except SQLAlchemyError as e:
        print(f"Database error occurred: {e}")
        raise e
    except Exception as e:
        print(f"Unexpected error occurred: {e}")
        raise e


# 创建用户评测任务
async def generate_user_evaluation_task(
        db: AsyncSession,
        user_task: UserQuizTasks
) -> UserQuizTasks | None:
    db.add(user_task)
    await db.commit()
    await db.refresh(user_task)
    return user_task


# 评测标准
async def get_system_quiz_criteria_sets_detail(
        db: AsyncSession,
        criteria_set_id: int
) -> SystemQuizCriteriaSets | None:
    query = select(SystemQuizCriteriaSets).filter(SystemQuizCriteriaSets.id == criteria_set_id)
    result = await db.execute(query)
    return result.scalars().first()


# 更新任务状态
# 0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束
async def update_plan_task_state(db: AsyncSession, plan_id: str, state: int, error_msg: Optional[str] = None):
    stmt = update(SystemQuizPlanTask).where(SystemQuizPlanTask.evaluation_id == plan_id).values(task_state=state,
                                                                                                error_msg=error_msg)
    try:
        await db.execute(stmt)
        await db.commit()
    except SQLAlchemyError as e:
        print(f"Database error occurred: {e}")
        raise e
    except Exception as e:
        print(f"Unexpected error occurred: {e}")
        raise e




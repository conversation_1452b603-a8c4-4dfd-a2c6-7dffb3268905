from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field, root_validator

from evaluation.share.util.util import timestamp_to_str


class UserChatConversationsCreate(BaseModel):
    user_id: str
    title: str
    ticket_id: Optional[str]


class UserChatConversationsDelete(BaseModel):
    conversation_id: str


class UserChatConversationsList(BaseModel):
    user_id: Optional[str]
    limit: int = Field(..., ge=1)
    pinned: Optional[bool]
    last_id: Optional[str]


class UserChatConversationsUpdate(BaseModel):
    user_id: str
    conversation_id: str
    title: Optional[str]
    ticket_id: Optional[str]


class UserChatMessagesCreate(BaseModel):
    conversation_id: str
    user_id: str
    responseMode: Optional[str]
    role: str
    content: str
    source: Optional[str]
    prompt: Optional[str]
    user_name: Optional[str]
    product_name: Optional[str]
    user_msg_id: Optional[str]
    user_content: Optional[str]
    trace_id: Optional[str]


class UserChatMessagesList(BaseModel):
    conversation_id: str
    limit: int = Field(..., ge=20)
    first_id: Optional[str]


class UserQuestionRatingsCreate(BaseModel):
    message_id: str
    user_id: Optional[str]
    sourceIp: Optional[str]
    source: Optional[str]
    rating: int
    feedback: Optional[str]
    category: Optional[str]


class UserKnowledgePrompt(BaseModel):
    document: str
    query: str
    context: Optional[str]


class PromptGenerator(BaseModel):
    """
    根据关键字和描述生成提示（Prompt）字符串的类。
    Example:
        generator = PromptGenerator()
        generator.generate("Python", "编程语言")
        '生成一个关于 Python 编程语言的提示。'
    """

    keyword: List[str]
    description: str
    category: str


class PromptJudgeReq(BaseModel):
    prompt: str


class CCCHAT(BaseModel):
    query: str


class User(BaseModel):
    user_id: Optional[str]
    user_no: Optional[str]
    name: Optional[str]
    full_name: Optional[str]
    department: Optional[str]
    permission: Optional[List[str]] = Field(default_factory=list)
    roles: Optional[list[dict]]
    remark: Optional[str] = None

class ChatTraceCreate(BaseModel):
    trace_id: str
    time_stamp: float
    step_number: int
    step: str
    data: dict | list | None


class VectorType(Enum):
    FAISS = "faiss"
    MILVUS = "milvus"


class RerankModelType(Enum):
    BGE_RERANKER_BASE = "bge-reranker-base"
    BGE_RERANKER_LAYERWISE = "bge-reranker-v2-minicpm-layerwise"


class UserChatMessagesStop(BaseModel):
    user_id: Optional[str]
    task_id: str


class Criteria(BaseModel):
    id: int
    title: str
    value: str
    rating_key: str
    rating_type: str
    created_at: Optional[str]
    updated_at: Optional[str]

    class Config:
        orm_mode = True

    @root_validator(pre=True)
    def format_timestamps(cls, values):
        values['created_at'] = timestamp_to_str(values.get('created_at'))
        values['updated_at'] = timestamp_to_str(values.get('updated_at'))
        return values

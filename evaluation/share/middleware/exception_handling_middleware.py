import logging
import traceback

from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import JSONResponse

from evaluation.api.resp import resp_model
from evaluation.share.exception.hanhai_exception import HanHaiException

logger = logging.getLogger("plaintext")

# 统一异常拦截中间件
class ExceptionHandlingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            # 尝试调用下一个中间件或路由
            response = await call_next(request)
            return response
        except HanHaiException as e:
            logger.error(f"HanHaiException caught: {str(e)}")  # 这里添加日志
            error_resp = resp_model.error_res(code=500, msg=str(e))
            return JSONResponse(status_code=200, content=error_resp.dict())
        except Exception as exc:
            logger.error(traceback.format_exc())
            logger.error(f"An error occurred: {str(exc)}")
            error_resp = resp_model.error_res(code=500, msg=str(exc))
            return JSONResponse(status_code=200, content=error_resp.dict())

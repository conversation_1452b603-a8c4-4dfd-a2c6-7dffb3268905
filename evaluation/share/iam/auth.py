import requests
import hashlib
import hmac
import urllib.request
import time
from evaluation.share.util.plaintext_log import logger


def signature(timestamp: str, ak: str, sk: str):
    params = {
        'Timestamp': timestamp,
        'Accesskey': ak,
    }
    str_encode = ''
    param_keys = sorted(params.keys())
    for key in param_keys:
        str_encode += urllib.request.quote(key, '~') + '=' + urllib.request.quote(str(params[key]), '~') + '&'
    return hmac.new(bytes(sk, 'utf-8'), bytes(str_encode[:-1], 'utf-8'), hashlib.sha256).hexdigest()


async def done_auth(ak: str, sk: str, timestamp: int, action: str, service: str, resources: str, ip: str,
                    user_uuid: str):
    """
    IAM  后鉴权
    """
    sign = signature(timestamp, ak, sk)
    url = f"http://iam.inner.sdns.ksyun.com/postauth?Accesskey={ak}&Signature={sign}&Timestamp={timestamp}"
    data = {
        "Action": action,
        "Service": service,
        "Resources": resources,
        "Ip": ip,
        "UserUuid": user_uuid,
    }

    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }

    status_code = 0
    try:
        response = requests.post(url, data=data, headers=headers)
        status_code = response.status_code
        data = response.json()
    except Exception:
        logger.error(f"sse_chat done_auth fail, ak={ak}, sk={sk}, requests_data={data}, url={url}")

    logger.info(f"sse_chat done_auth, ak={ak}, sk={sk}, requests.data={data}, url={url}, "
                f"response.status_code={status_code}, response.data={data}")
    return status_code, data


if __name__ == '__main__':
    timestamp = int(time.time())
    ak = 'AKLT87PFDSnnQnuTF-WOYxD_EQ'
    sk = 'OBmNPgfvKnLyH3Osn9lwmHs/HVTjJNMqlzsm1nD6wGi/TDD45O9QYW0pb5dUOz2paA=='
    action = 'ChatAction'
    service = 'qingzhou'
    resources = 'krn:ksc:iam::2000171575:user/longlei'
    ip = '*******'
    user_uuid = 'ddom6aoSre34FH7wh92rg'
    done_auth(ak, sk, timestamp, action, service, resources, ip, user_uuid)

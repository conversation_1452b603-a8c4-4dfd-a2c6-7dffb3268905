from configparser import ConfigParser, ExtendedInterpolation
from pathlib import Path
import os


class AppConfig:
    def __init__(self):
        self._config = ConfigParser(interpolation=ExtendedInterpolation())

    def load_config(self, prefix="config", project="", env="local", specified_file="", blog=True, bdefault=True):
        """
            文件名称由:
                前缀, 环境变量组成的ini文件，{prefix}_{env}.ini
            读取过程:
                1. 读取默认配置文件: {prefix}_default.ini
                2. 读取环境变量配置文件: {prefix}_{env}.ini
            {prefix}, 不同的应用可以使用不同的{prefix}, 内容可包含相对目录
            {env}, 代表环境变量, 例如: local, dev, tools, prod, 默认为local, 既本地环境
            {prefix}_local.ini, 本地环境的配置文件, 不提交到git中, 请在应用的.gitignore中添加

            使用ConfigParser的目的, 是简单的实现上面的环境配置文件覆盖默认配置文件的功能, 从而减少环境变量配置文件配置项
            使用ini, 而不是toml, 是由于ConfigParser暂时不支持toml
            blog, 是否输出标准输出日志
        """

        if os.environ.get("APP_SPECIFIED_CFG"):
            specified_file = os.environ.get("APP_SPECIFIED_CFG")

        if specified_file:
            fp = open(specified_file, "r", encoding="utf-8")
            filenames = []
            # 指定文件, 也可以使用default
            if bdefault:
                filenames = [f"{prefix}_{project}_default.ini"] if project else [f"{prefix}_default.ini"]
            filenames.append(specified_file)

            if blog:
                print(f"Loading specified config files: {filenames}, {bdefault}")
            self._config.read(filenames, encoding="utf-8")
        else:
            filenames = [f"{prefix}_{project}_default.ini"] if project else [f"{prefix}_default.ini"]
            env_filename = f"{prefix}_{project}_{env}.ini" if project else f"{prefix}_{env}.ini"
            if Path(env_filename).is_file():
                filenames.append(env_filename)
            if blog:
                print(f"Loading config files: {filenames}")
            self._config.read(filenames, encoding="utf-8")

        # for env
        self._config["DEFAULT"]["pod_uid"] = (
            os.environ.get("POD_UID") or self._config["DEFAULT"]["pod_uid"])

        self._config["DEFAULT"]["version"] = (
            os.environ.get("APP_DATA_VERSION") or self._config["DEFAULT"]["version"])

        # for mysql, ['mysql']['dsn'], 貌似是实时获取ini中的变量, 而不是在load_config时赋值的变量
        self._config["mysql"]["db_username"] = (
            os.environ.get("APP_MYSQL_USERNAME") or self._config["mysql"]["db_username"])
        self._config["mysql"]["db_password"] = (
            os.environ.get("APP_MYSQL_PASSWORD") or self._config["mysql"]["db_password"])
        self._config["mysql"]["db_host"] = (
            os.environ.get("APP_MYSQL_HOST") or self._config["mysql"]["db_host"])
        self._config["mysql"]["db_port"] = (
            os.environ.get("APP_MYSQL_PORT") or self._config["mysql"]["db_port"])
        self._config["mysql"]["db_name"] = (
            os.environ.get("APP_MYSQL_DBNAME") or self._config["mysql"]["db_name"])


        if blog:
            print(f"config: {self}")

    def __str__(self):
        r = list()
        sections = self._config.sections()
        for section in sections:
            options = self._config.options(section)
            for option in options:
                value = self._config.get(section, option)
                l = f"{section}: {option} = {value}"
                r.append(l)
        return "\n".join(r)

    def get_config(self):
        return self._config


config_instance = AppConfig()
CONFIG = config_instance.get_config()

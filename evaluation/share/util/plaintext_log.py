import logging
import sys
from evaluation.share.util.reqid import redid_ctx_var


class ColorfulFormatter(logging.Formatter):
    RED = "\033[1;31m"
    YELLOW = "\033[1;33m"
    RESET = "\033[0m"

    _message = "%(asctime)s.%(msecs).03d %(levelname)s %(name)s %(module)s:%(lineno)d [%(request_id)s] %(message)s"

    FORMATS = {
        logging.ERROR: RED + _message + RESET,
        logging.WARNING: YELLOW + _message + RESET,
        'DEFAULT': _message
    }

    def format(self, record):
        record.request_id = redid_ctx_var.get()
        log_fmt = self.FORMATS.get(record.levelno, self.FORMATS['DEFAULT'])
        _formatter = logging.Formatter(log_fmt, datefmt="%Y-%m-%d %H:%M:%S")
        return _formatter.format(record)


logger = logging.getLogger("plaintext")  # 没指定名字就是root logger


def init(filename=None, log_level=logging.DEBUG, max_bytes=10 * 1024 * 1024, backup_count=5):
    # default log to stdout
    print(f"logger file {filename}, log_level {log_level}")
    if not filename:
        stream_handler = logging.StreamHandler(stream=sys.stdout)
        stream_handler.setFormatter(ColorfulFormatter())
        stream_handler.setLevel(log_level)
        logger.handlers = []
        logger.setLevel(log_level)  # 设置logger的level, 如果一个日志消息的级别小于日志记录器或处理器的设定级别，则该消息不会被记录或处理
        logger.addHandler(stream_handler)
    # supervisor and docker to stdout and stderr
    # else:
    #     log_directory = os.path.dirname(filename)
    #     if not os.path.exists(log_directory):
    #         os.makedirs(log_directory)
    #     # file_handler = logging.FileHandler(filename)
    #     file_handler = RotatingFileHandler(
    #         filename, maxBytes=max_bytes, backupCount=backup_count
    #     )
    #     file_handler.setFormatter(ColorfulFormatter())
    #     file_handler.setLevel(log_level)
    #     logger.addHandler(file_handler)


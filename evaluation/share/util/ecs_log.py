from evaluation.share.util.plaintext_log import logger
import os
from logger.handlers import RotatingFileHandler
from share.util.user import UserFormatter

ecs_logger = logger.getLogger("ecs")


def init(filename: str = "api.ecs.json", max_bytes: int = 100 * 1024 * 1024, backup_count: int = 5):
    log_directory = os.path.dirname(filename)
    if not os.path.exists(log_directory):
        os.makedirs(log_directory)
    handler = RotatingFileHandler(
      filename, maxBytes=max_bytes, backupCount=backup_count
    )
    handler.setFormatter(UserFormatter())
    ecs_logger.addHandler(handler)
    ecs_logger.setLevel(logger.DEBUG)

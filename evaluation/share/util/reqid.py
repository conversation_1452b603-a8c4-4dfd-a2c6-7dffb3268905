import contextvars
import hashlib
import random
import socket
import string
from datetime import datetime

from starlette.types import Receive, Scope, Send


redid_ctx_var = contextvars.ContextVar('request_id', default='UNKNOWN')
user_id_context_var = contextvars.ContextVar("user_id", default=None)




def get_request_id() -> str:
    return redid_ctx_var.get()


def generate_request_id(user_id):
    if user_id is None:
        user_id = 'anonymous'
    timestamp = datetime.now().strftime('%S%f')[-4:]
    hostname = socket.gethostname()
    host_id = hashlib.sha256(hostname.encode()).hexdigest()[:4]
    user_id_hash = hashlib.sha256(user_id.encode()).hexdigest()[:4]
    random_str_length = 32 - (len(timestamp) + len(host_id) + len(user_id_hash))
    random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=random_str_length))
    request_id = f"{host_id}{user_id_hash}{random_str}{timestamp}"
    return request_id


# 请求 ID 中间件
class RequestIDMiddleware:
    def __init__(self, app):
        self.app = app

    async def __call__(self, scope: Scope, receive: Receive, send: Send):
        if scope['type'] == 'http':
            user_id = user_id_context_var.get()
            request_id = generate_request_id(user_id)
            redid_ctx_var.set(request_id)
            original_send = send

            async def send_wrapper(message):
                if message['type'] == 'http.response.start':
                    headers = dict(message['headers'])
                    headers[b'x-human-eval-request-id'] = request_id.encode()
                    message['headers'] = [(k, v) for k, v in headers.items()]
                await original_send(message)

            await self.app(scope, receive, send_wrapper)
        else:
            await self.app(scope, receive, send)


from starlette.middleware.base import BaseHTTPMiddleware


class CustomloggerMiddleware(BaseHTTPMiddleware):

    async def dispatch(self, request, call_next):
        from evaluation.share.util.plaintext_log import logger
        logger.debug(f"Request url: {request.url.path}")
        logger.debug(f"Request headers: {request.headers}")
        logger.debug(f"Request query params: {request.query_params}")

        response = await call_next(request)

        # 记录响应信息
        logger.debug(f"Response headers: {response.headers}")

        return response

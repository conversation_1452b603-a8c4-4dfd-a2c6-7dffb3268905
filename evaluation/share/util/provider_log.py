import os
import sys
from evaluation.share.util.plaintext_log import logger
from logger.handlers import RotatingFileHandler
from datetime import datetime
from pythonjsonlogger import jsonlogger


class FilebeatFormatter(jsonlogger.JsonFormatter):
    def add_fields(self, log_record, record, message_dict):
        super().add_fields(log_record, record, message_dict)
        if not log_record.get('@timestamp'):
            log_record['@timestamp'] = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')


provider_logger = logger.getLogger("provider")


def init(filename: str = "", log_level=logger.DEBUG, max_bytes: int = 100 << 20, backup_count: int = 5):

    filebeat_formatter = FilebeatFormatter(
        json_ensure_ascii=False,
        rename_fields={
            "message": "provider_id",
        },
    )
    if not filename:
        stream_handler = logger.StreamHandler(stream=sys.stdout)
        stream_handler.setFormatter(filebeat_formatter)
        stream_handler.setLevel(log_level)
        provider_logger.handlers = []
        provider_logger.setLevel(log_level)
        provider_logger.addHandler(stream_handler)
    else:
        log_directory = os.path.dirname(filename)
        if not os.path.exists(log_directory):
            os.makedirs(log_directory)
        handler = RotatingFileHandler(
          filename, maxBytes=max_bytes, backupCount=backup_count
        )
        handler.setFormatter(filebeat_formatter)
        provider_logger.addHandler(handler)
        provider_logger.setLevel(logger.DEBUG)

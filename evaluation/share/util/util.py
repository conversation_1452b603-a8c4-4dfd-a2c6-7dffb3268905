

def timestamp_to_str(timestamp):
    """Safely converts a timestamp to a formatted string."""
    try:
        return timestamp.strftime("%Y-%m-%d %H:%M:%S")
    except (TypeError, ValueError):
        return None


def get_order(order_str, model):
    if not order_str or order_str == "":
        return model.id.desc()
    order_str = order_str.split(" ")
    order_by = getattr(model, order_str[0], None)
    if order_by:
        if len(order_str) > 1 and order_str[1].lower() == "desc":
            order = order_by.desc()
        else:
            order = order_by.asc()
    else:
        # logger.warning(f"Invalid order_str: {order_str}")
        return model.id.desc()
    return order
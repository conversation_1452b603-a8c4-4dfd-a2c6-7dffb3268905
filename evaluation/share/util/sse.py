import asyncio
import json
from evaluation.share.util.plaintext_log import logger
from datetime import datetime, timedelta

import httpx
import jwt

from evaluation.api.base import AuthType
from evaluation.share.util.app_config import CONFIG


class SSEChatModel:
    def __init__(self):
        self.conversation = None
        self.trace_id = None
        self.messages = []
        self.reference = None

    def set_trace(self, trace_id):
        self.trace_id = trace_id

    def add_message(self, message):
        self.messages.append(message)

    def set_reference(self, reference):
        self.reference = reference

    def set_conversation(self, conversation_id, message_id):
        self.conversation = {
            "conversation_id": conversation_id,
            "message_id": message_id,
        }

    def get_messages(self):
        if self.messages:
            return "".join(self.messages)
        return ""


async def parse_sse_events(response):

    sse_chat_model = SSEChatModel()
    current_event = None
    try:
        async for line in response.aiter_lines():

            if not line:
                continue

            line = line.strip()
            if line.startswith("event:"):
                current_event = line.split(":", 1)[1].strip()
                continue

            if line.startswith("data:") and current_event:
                try:
                    if current_event == "message":
                        message = line.split(":", 1)[1].strip().strip('"')
                        sse_chat_model.add_message(message)
                    else:
                        data = json.loads(line.split(":", 1)[1].strip())
                        if current_event == "trace":
                            sse_chat_model.set_trace(data.get("trace_id"))
                        elif current_event == "reference":
                            sse_chat_model.set_reference(data)
                        elif current_event == "conversation":
                            conversation_id = data.get("conversation_id")
                            message_id = data.get("message_id")
                            if conversation_id and message_id:
                                sse_chat_model.set_conversation(
                                    conversation_id, message_id
                                )
                        elif current_event == "close":
                            break
                except json.JSONDecodeError as e:
                    logger.error(f"parse_sse_events error: {e}")
                except Exception as e:
                    logger.error(
                        f"parse_sse_events processing event '{current_event}': {e}"
                    )
    except httpx.RemoteProtocolError as e:
        logger.error(f"Remote protocol error occurred: {e}")
    except Exception as e:
        logger.error(f"Error: {e}")

    finally:
        return sse_chat_model


async def test_get_sse_chat():
    sse_chat_model = SSEChatModel()
    sse_chat_model.status = "success"
    sse_chat_model.message = "test_message"
    sse_chat_model.conversation = "test_conversation"
    sse_chat_model.reference = "test_reference"
    sse_chat_model.trace = "test_trace"
    return sse_chat_model


async def get_sse_chat(
    addr: str,
    question: str,
    user_id: str = "9999",
    debug: bool = True,
    max_retries: int = 3,
    retry_delay: float = 1.0,
):
    api = "/stream/v1/chat"
    url = f"{addr}{api}"
    payload = {"question": question, "debug": debug, "user_id": user_id}
    timestamp = datetime.utcnow() + timedelta(minutes=15)
    sk  = CONFIG["auth"]["chat_api_sk"]
    token = jwt.encode({"api": api, "exp": timestamp}, sk, "HS256")
    headers = {"Authorization": token, "AuthType": AuthType.TOKEN.value}

    sse_chat_model = None
    retry_count = 0
    while retry_count < max_retries:
        try:
            timeout_config = httpx.Timeout(100.0, connect=60.0, read=60.0)
            async with httpx.AsyncClient(timeout=timeout_config) as client:
                async with client.stream(
                    "POST", url, json=payload, headers=headers
                ) as response:
                    response.raise_for_status()
                    sse_chat_model = await parse_sse_events(response)
                    break
        except httpx.HTTPStatusError as e:
            # 4xx 或 5xx 错误，不重试
            logger.error(f"HTTP error occurred: {e}")
            break
        except (httpx.RequestError, Exception) as e:
            logger.error(f"Request error occurred: {e}")
            retry_count += 1
            if retry_count < max_retries:
                logger.info(f"Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
            else:
                logger.error("Max retry count reached, aborting.")
                break

    return sse_chat_model
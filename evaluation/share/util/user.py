from contextvars import ContextVar
from typing import List
import httpx
import jwt
from jwt.exceptions import DecodeError, ExpiredSignatureError
from pydantic import BaseModel
from pydantic.typing import Optional
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import J<PERSON><PERSON>esponse

from evaluation.api.resp import resp_model
from evaluation.share.orm.platform_schema import User
from evaluation.share.util import plaintext_log
from evaluation.share.util.app_config import CONFIG
from evaluation.share.util.plaintext_log import logger

user_id_context_var: ContextVar[str | None] = ContextVar("user_id", default=None)
user_name_context_var: ContextVar[str | None] = ContextVar("user_name", default=None)
user_full_name_context_var: ContextVar[str | None] = ContextVar("user_full_name", default=None)
user_department_context_var: ContextVar[str | None] = ContextVar("user_department", default=None)
user_permissions_context_var: ContextVar[list[str]] = ContextVar("permissions", default=[])
user_account_id_context_var: ContextVar[str | None] = ContextVar("account_id", default=None)

INVALID_TOKEN_RESPONSE = JSONResponse(status_code=401, content={"code": 40104, "message": "Invalid token"})
TOKEN_EXPIRED_RESPONSE = JSONResponse(status_code=401, content={"code": 40103, "message": "Token has expired"})


class Userinfo(BaseModel):
    account_id: str
    user_id: str
    user_name: str
    user_full_name: str
    user_department: Optional[str] = None
    permissions: List[str]


userinfo: ContextVar[Optional[Userinfo]] = ContextVar("userinfo", default=None)
jwt_token: ContextVar[Optional[str]] = ContextVar("jwt_token", default=None)

JWT_PUBLIC_KEY = None
JWT_ALGORITHM = "RS256"


def init_jwt_public_key(public_key_path: str):
    global JWT_PUBLIC_KEY
    with open(public_key_path, "r", encoding='utf-8') as f:
        JWT_PUBLIC_KEY = f.read().strip()


# def get_user_id() -> int | None:
#     return user_id_context_var.get()

def get_user_id_v2() -> str | None:
    return user_id_context_var.get()


def get_user_name() -> str | None:
    return user_name_context_var.get()


def get_user_full_name() -> str | None:
    return user_full_name_context_var.get()


def get_user_department() -> str | None:
    return user_department_context_var.get()


def get_user_permissions() -> list[str]:
    return user_permissions_context_var.get()


def get_account_id() -> str | None:
    return user_account_id_context_var.get()


def get_login_user_info() -> dict | None:
    user_info = userinfo.get()
    # 转成字典
    return {
        "user_id": user_info.user_id,
        "user_name": user_info.user_name,
        "user_full_name": user_info.user_full_name,
        "user_department": user_info.user_department,
        "permissions": user_info.permissions,
        "account_id": user_info.account_id
    }


class AuthenticationMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        ignore_urls = [
            "/api/v1/inner/job/add_inference_task",
            "/api/v1/inner/job/update_inference_task",
            "/api/v1/inner/job/begin_inference_task",
            "/api/v1/inner/job/delete_service",
            "/eval/api/v1/docs",
            "/eval/api/v1/openapi.json",
        ]
        uri = request.url.path
        if uri in ignore_urls:
            # chat 需要返回流式数据，单独处理, 或其他不能走鉴权的接口直接放行
            return await call_next(request)

        authorization: str = request.headers.get("Authorization")
        if not authorization:
            error_resp = resp_model.error_res(code=401, msg="Authorization header is missing")
            return JSONResponse(status_code=200, content=error_resp.dict())
        token_prefix, token = authorization.split(" ")
        if token_prefix.lower() != "bearer":
            error_resp = resp_model.error_res(code=401, msg="Invalid token prefix")
            return JSONResponse(status_code=200, content=error_resp.dict())
        try:
            validate_jwt(token)
        except Exception as e:
            error_resp = resp_model.error_res(code=401, msg=f"Invalid token{str(e)}")
            return JSONResponse(status_code=200, content=error_resp.dict())

        return await call_next(request)


def validate_jwt(token: str):
    try:
        payload = jwt.decode(token, JWT_PUBLIC_KEY, algorithms=[JWT_ALGORITHM], options={"verify_aud": False})
        logger.debug(f"validate_jwt userinfo:{Userinfo(**payload)}")
        userinfo.set(Userinfo(**payload))
        set_user_info(userinfo.get())
        jwt_token.set(token)
    except jwt.ExpiredSignatureError as e:
        raise e
    except jwt.InvalidTokenError as e:
        logger.exception(f"Invalid token: {e}")
        raise e
    except jwt.PyJWTError as e:
        logger.exception(f"JWT error: {e}")
        raise e


def set_user_info(user_info: Userinfo) -> None:
    user_id_context_var.set(user_info.user_id)
    user_name_context_var.set(user_info.user_name)
    user_full_name_context_var.set(user_info.user_full_name)
    user_department_context_var.set(user_info.user_department)
    user_permissions_context_var.set(user_info.permissions)
    user_account_id_context_var.set(user_info.account_id)


async def jwt_auth(authorization: str, sk: str):
    """
    jwt token 鉴权
    后期需要在这里添加过期时间，目前是调用方传设置的过期时间
    """
    auth_error = False
    auth_error_info = ""
    payload = {}
    if not authorization:
        auth_error = True
        auth_error_info = "authorization is not null"
    else:
        try:
            payload = jwt.decode(authorization, sk, algorithms='HS256')
        except ExpiredSignatureError:
            auth_error = True
            auth_error_info = "Token has expired"
        except DecodeError:
            auth_error = True
            auth_error_info = "Token is invalid"
        except Exception as e:
            auth_error = True
            auth_error_info = f"An error occurred: {e}"

    if auth_error:
        plaintext_log.logger.info(f"UserAuthMiddleware check data:{payload},auth_error_info:{auth_error_info})")
    results = {
        "auth_error": auth_error,
        "auth_error_info": auth_error_info
    }
    return results
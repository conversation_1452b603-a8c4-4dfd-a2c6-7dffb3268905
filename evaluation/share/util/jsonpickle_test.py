from datetime import datetime

import jsonpickle


# 定义一个类，它可以包含其他类的实例
class Person:
    def __init__(self, name, age, time):
        self.name = name
        self.age = age
        self.time = time


class Group:
    def __init__(self, name, members: list[Person]):
        self.name = name
        self.members = members


# 创建一个 Person 实例
alice = Person('Alice', 30, datetime.now())
bob = Person('Bob', 25, datetime.now())

# 创建一个 Group 实例，它包含 Person 实例的列表
group = Group('Friends', [alice, bob])

# 使用 jsonpickle 将对象序列化为 JSON 字符串
group_json = jsonpickle.encode(group)
print('Serialized object:', group_json)

# 将 JSON 字符串反序列化为 Python 对象
group_decoded = jsonpickle.decode(group_json)
print('Deserialized object:', group_decoded)
print('Group name:', group_decoded.name)
for member in group_decoded.members:
    print('Member:', member.name, 'Age:', member.age)

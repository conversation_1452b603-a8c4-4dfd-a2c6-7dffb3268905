import logging
from functools import wraps
from typing import Dict, Callable, Any
from evaluation.share.util.user_info_injector import UserInfoInjector

logger = logging.getLogger("plaintext")


def inject_user_info(user_field_mappings: Dict[str, str]):
    """
    用户信息注入装饰器
    
    Args:
        user_field_mappings: 字段映射字典
            例：{"created_user": "created_user_name", "updated_user": "updated_user_name"}
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            logger.info(f"开始执行带用户信息注入的方法: {func.__name__}, 字段映射: {user_field_mappings}")
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 处理返回结果
            if isinstance(result, tuple) and len(result) == 2:
                # 处理 (total, data) 格式的返回值（分页数据）
                total, data = result
                logger.info(f"处理分页数据，总数: {total}, 数据条数: {len(data) if isinstance(data, list) else 'N/A'}")
                if isinstance(data, list):
                    data = await UserInfoInjector.inject_user_names(data, user_field_mappings)
                return total, data
            elif isinstance(result, list):
                # 处理直接返回列表的情况
                logger.info(f"处理列表数据，数据条数: {len(result)}")
                return await UserInfoInjector.inject_user_names(result, user_field_mappings)
            else:
                logger.info(f"返回结果不是列表或分页格式，跳过用户信息注入，结果类型: {type(result)}")
            
            return result
        return wrapper
    return decorator
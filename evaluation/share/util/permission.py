from enum import Enum

from evaluation.share.util.app_config import CONFIG


class Permission(Enum):
    # RUN_AS_ADMIN = "RUN_AS_ADMIN"
    # VIEW_CHAT_PAGE = "VIEW_CHAT_PAGE"
    # VIEW_ADMIN_PAGE = "VIEW_ADMIN_PAGE"
    # CHAT = "CHAT"
    # VIEW_DOC_MGMT = "VIEW_DOC_MGMT"
    # VIEW_STATS = "VIEW_STATS"
    # VIEW_EVAL_MGMT = "VIEW_EVAL_MGMT"
    # DOC_MGMT = "DOC_MGMT"
    ADMIN = "MGMT_EVAL"
    EVAL_MGMT = "MGMT_EVAL"  # 评测平台管理
    VIEW_EVAL = "VIEW_EVAL"  # 评测平台查看
    MGMT_EVAL_TASK = "MGMT_EVAL_TASK"  # 评测任务管理
    VIEW_EVAL_TASK = "VIEW_EVAL_TASK"  # 评测任务查看


# PERMISSION_MAPPING_USS = {
#     "knowledge_base_cc:admin": [Permission.ADMIN,
#                                 Permission.VIEW_CHAT_PAGE, Permission.VIEW_ADMIN_PAGE,
#                                 Permission.VIEW_DOC_MGMT, Permission.VIEW_STATS, Permission.VIEW_EVAL_MGMT,
#                                 Permission.CHAT, Permission.DOC_MGMT, Permission.EVAL_MGMT, Permission.EXEC_EVAL],
#     "knowledge_base_cc:run_as_admin": [Permission.RUN_AS_ADMIN],
#     "knowledge_base_cc:visit": [Permission.VIEW_CHAT_PAGE],
#     "knowledge_base_cc:dashboard_visit": [Permission.VIEW_ADMIN_PAGE],
#     "knowledge_base_cc:chat": [Permission.VIEW_CHAT_PAGE, Permission.CHAT],
#     "knowledge_base_cc:view_doc_mgmt": [Permission.VIEW_ADMIN_PAGE, Permission.VIEW_DOC_MGMT],
#     "knowledge_base_cc:doc_mgmt": [Permission.VIEW_ADMIN_PAGE, Permission.VIEW_DOC_MGMT, Permission.DOC_MGMT],
#     "knowledge_base_cc:view_stats": [Permission.VIEW_ADMIN_PAGE, Permission.VIEW_STATS],
#     "knowledge_base_cc:view_eval_mgmt": [Permission.VIEW_ADMIN_PAGE, Permission.VIEW_EVAL_MGMT],
#     "knowledge_base_cc:eval_mgmt": [Permission.VIEW_ADMIN_PAGE, Permission.VIEW_EVAL_MGMT, Permission.EVAL_MGMT],
#     "knowledge_base_cc:manual_evaluation": [Permission.VIEW_ADMIN_PAGE, Permission.EXEC_EVAL],
#     "knowledge_base_cc:developer": [Permission.VIEW_CHAT_PAGE, Permission.VIEW_ADMIN_PAGE, Permission.CHAT,
#                                     Permission.VIEW_DOC_MGMT, Permission.VIEW_STATS, Permission.VIEW_EVAL_MGMT],
# }
#
# PERMISSION_MAPPING_IAM = {
#     "PostChat": [Permission.VIEW_CHAT_PAGE, Permission.CHAT],
# }


# def convert_permissions(permissions: list[str], permission_mapping: dict[str, list[Permission]]) -> list[str]:
#     result = set()
#     for permission in permissions:
#         if not (permission := permission.strip()):
#             continue
#         # for compatible with old version, can be removed in the future
#         result.add(permission)
#
#         if permission in permission_mapping:
#             for p in permission_mapping[permission]:
#                 result.add(p.value)
#
#     if Permission.RUN_AS_ADMIN.value in result and CONFIG.getboolean("auth", "allow_run_as_admin"):
#         result.add(Permission.ADMIN.value)
#
#     return list(result)


def should_match(permissions_required: list[str], user_permissions: list[str]) -> bool:
    # return true if user has one of the required permissions
    if Permission.ADMIN.value in user_permissions:
        return True
    return any(p in user_permissions for p in permissions_required)


def must_match(permissions_required: list[str], user_permissions: list[str]) -> bool:
    # return true if user has all the required permissions
    if Permission.ADMIN.value in user_permissions:
        return True
    return all(p in user_permissions for p in permissions_required)

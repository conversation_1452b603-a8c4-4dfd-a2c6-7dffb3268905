import base64
from Crypto.Cipher import <PERSON><PERSON>
from Crypto.Util.Padding import pad,unpad
import sys
import secrets

def encrypt_aes(key, plaintext):
    # Ensure the key is of correct length (16, 24, or 32 bytes for AES-128, AES-192, or AES-256)
    if len(key) not in [16, 24, 32]:
        raise ValueError("Key must be 16, 24, or 32 bytes long")

    # Generate a random nonce (typically 12 bytes for GCM)
    nonce = secrets.token_bytes(12)

    # Create an AES cipher object with the key and nonce
    cipher = AES.new(key.encode('utf-8'), AES.MODE_GCM, nonce=nonce)

    # Pad the plaintext to be a multiple of the block size
    padded_plaintext = pad(plaintext.encode('utf-8'), AES.block_size)

    # Encrypt the padded plaintext
    ciphertext, tag = cipher.encrypt_and_digest(padded_plaintext)

    # Combine nonce, ciphertext, and tag into one byte string
    encrypted_message = nonce + ciphertext + tag

    # Return the Base64 encoded version of the encrypted message
    return base64.b64encode(encrypted_message).decode('utf-8')

def decrypt_aes(key, ciphertext_base64):
    # Decode the base64 encoded ciphertext to bytes
    encrypted_message = base64.b64decode(ciphertext_base64)

    # Extract nonce (first 12 bytes), ciphertext (everything but the last 16 bytes), and tag (last 16 bytes)
    nonce = encrypted_message[:12]
    ciphertext = encrypted_message[12:-16]
    tag = encrypted_message[-16:]

    # Create an AES cipher object with the key and nonce
    cipher = AES.new(key.encode('utf-8'), AES.MODE_GCM, nonce=nonce)

    try:
        # Decrypt and verify the authenticity of the data
        plaintext_padded = cipher.decrypt_and_verify(ciphertext, tag)
        # Remove padding
        plaintext = unpad(plaintext_padded, AES.block_size).decode('utf-8')
        return plaintext
    except Exception as e:
        return f"Decryption failed: {str(e)}"



if __name__ == "__main__":
    key = "CUSR/0hrCx1XT43C4aGjJ5YEbvT3rI7f"
    # Example usage:
    # plaintext = "Hello, World!"
    # local
    # plaintext = "123456"
    # poc
    plaintext = "12@34%56"

    # Encrypt the plaintext
    ciphertext = encrypt_aes(key, plaintext)
    print("Ciphertext:", ciphertext)

    # Decrypt the ciphertext
    decrypted_plaintext = decrypt_aes(key, ciphertext)
    print("Decrypted plaintext:", decrypted_plaintext)

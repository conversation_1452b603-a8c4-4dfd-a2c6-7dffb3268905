import logging
import os
from contextvars import ContextV<PERSON>
from datetime import datetime
from logging.handlers import RotatingFileHandler
from pythonjsonlogger import jsonlogger

metric_context_var: ContextVar[dict | None] = ContextVar("metric", default=None)
filebeat_logger = logging.getLogger("metric")


def get_current_time() -> str:
    return datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')


def init(filename: str = "metric.json", max_bytes: int = 100 * 1024 * 1024, backup_count: int = 5):
    class FilebeatFormatter(jsonlogger.JsonFormatter):
        def add_fields(self, log_record, record, message_dict):
            super(FilebeatFormatter, self).add_fields(log_record, record, message_dict)
            if not log_record.get('@timestamp'):
                log_record['@timestamp'] = get_current_time()

    filebeat_formatter = FilebeatFormatter(
        json_ensure_ascii=False,
        rename_fields={
            "message": "metric_type",
        },
    )
    log_directory = os.path.dirname(filename)
    if not os.path.exists(log_directory):
        os.makedirs(log_directory)
    handler = RotatingFileHandler(
        filename, maxBytes=max_bytes, backupCount=backup_count
    )
    handler.setFormatter(filebeat_formatter)
    filebeat_logger.addHandler(handler)
    filebeat_logger.setLevel(logging.DEBUG)


def start():
    metric_context_var.set(None)


def update(metric_type: str, value: dict):
    context_var = metric_context_var.get()
    if context_var is None:
        context_var = {metric_type: value}
    elif context_var[metric_type] is None:
        context_var[metric_type] = value
    else:
        context_var[metric_type].update(value)

    metric_context_var.set(context_var)


def log(metric_type, unset: bool = True):
    context_var = metric_context_var.get()
    if context_var is None or context_var[metric_type] is None:
        return
    filebeat_logger.info(metric_type, extra=context_var[metric_type])
    if unset:
        del context_var[metric_type]
        metric_context_var.set(context_var)


def stop():
    context_var = metric_context_var.get()
    if context_var is None:
        return
    for metric_type in context_var.keys():
        filebeat_logger.info(metric_type, extra=context_var[metric_type])
    metric_context_var.set(None)

from typing import List, Optional

from fastapi import Depends, FastAPI
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.api.resp import resp_model
from evaluation.share.orm import crud, database
from evaluation.share.util.permission import should_match
from evaluation.share.util.plaintext_log import logger
from evaluation.share.util.user import get_user_permissions
from typing import Union, Any

class MenuModel(BaseModel):
    menu_id: int
    parent_id: int
    name: str
    link: Optional[str]
    icon: Optional[str]
    permission: List[str]
    children: Optional[List["MenuModel"]] = []

class ResponseModel(BaseModel):
    code: int = 200
    message: str = "success"
    data: Optional[Union[Any, List[Any]]] = None


async def build_menu_tree(menu: MenuModel, accessible_menus: List[MenuModel]):
    menu.children = [
        await build_menu_tree(child_menu, accessible_menus)
        for child_menu in accessible_menus
        if child_menu.parent_id == menu.menu_id
    ]
    return menu


def register_setting_api(app: FastAPI):
    @app.get("/api/v1/settings/menu", summary="获取菜单")
    async def get_menu(db: AsyncSession = Depends(database.get_db)):
        result = list()
        # 获取用户权限, 配置了无需认证赋予ADMIN权限
        user_permissions = get_user_permissions()
        logger.debug(f"user_permissions: {user_permissions}")

        if not user_permissions:
            return ResponseModel(data=result)

        db_records = await crud.get_available_menu(db)

        # 根据权限过滤可访问的记录
        if "admin" in user_permissions:
            accessible_menus = [
                MenuModel(
                    menu_id=record.id,
                    parent_id=record.parent_id,
                    name=record.name,
                    link=record.link,
                    icon=record.icon,
                    children=[],  # 子菜单暂时设为空
                    permission=record.permission,
                )
                for record in db_records
            ]
        else:
            accessible_menus = [
                MenuModel(
                    menu_id=record.id,
                    parent_id=record.parent_id,
                    name=record.name,
                    link=record.link,
                    icon=record.icon,
                    children=[],  # 子菜单暂时设为空
                    permission=record.permission,
                )
                for record in db_records
                if should_match(permissions_required=record.permission, user_permissions=user_permissions)
            ]

        # 获取所有的一级菜单
        top_level_menus = [menu for menu in accessible_menus if menu.parent_id == 0]

        # 为每个一级菜单查找对应的子菜单
        result = [
            await build_menu_tree(top_level_menu, accessible_menus)
            for top_level_menu in top_level_menus
        ]

        # 通过data返回可以访问的数据结构
        return resp_model.success_res(data=result)

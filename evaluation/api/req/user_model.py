# 获取系统问题
from typing import List, Optional
from pydantic import BaseModel


class User(BaseModel):
    user_id: str
    name: str
    full_name: str
    department: str
    permission: Optional[List[str]]


class EvaluationGroupCreateRequest(BaseModel):

    name: str
    description: Optional[str]
    members: List[str]

class EvaluationGroupUpdateRequest(EvaluationGroupCreateRequest):
    user_set_identifier: str


class DeleteUserSetReq(BaseModel):
    user_set_identifiers: list[str]
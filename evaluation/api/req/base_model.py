from pydantic import BaseModel, Field
from typing import Optional


class BaseModelPageListReq(BaseModel):

    page: int = Field(..., ge=1)
    page_size: int = Field(..., ge=1)
    fetch_total: bool = False
    search_term: str = Field(None, description="搜索关键词")
    question_type: str = Field(None, description="题型")
    only_me: bool = False
    task_state: int = -1,
    show_members_name: bool = False,
    order_str: Optional[str] = None


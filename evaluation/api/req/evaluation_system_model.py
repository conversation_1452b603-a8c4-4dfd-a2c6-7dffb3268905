# 获取系统问题
from typing import Optional, Dict

from pydantic import BaseModel

from evaluation.api.req.base_model import BaseModelPageListReq


class SystemQuestionData(BaseModel):
    id: int
    question_id: str
    question: str
    question_desc: Optional[str] = None
    question_type: str
    version: int
    created_at: str
    updated_at: str
    state: int
    details: Dict
    created_user_name: Optional[str] = None
    updated_user_name: Optional[str] = None


# 系统题目创建req
class SystemQuestionCreateReq(BaseModel):
    question: Optional[str] = None
    question_desc: Optional[str] = None
    question_type_id: int
    question_type: str
    details: Dict
    user_id: Optional[str] = None


# 获取系统问题类型
class SystemQuestionTypeData(BaseModel):
    id: int
    question_type_desc: str
    question_type: str


# 系统题目删除req
class SystemQuestionDeleteReq(BaseModel):
    question_id: str
    question_version: Optional[int] = None


# 系统题目修改req
class SystemQuestionUpdateReq(BaseModel):
    question_id: str
    question: str
    question_desc: Optional[str] = None
    question_type_id: int
    question_type: str
    details: Dict
    user_id: str


# 题集创建req
class QuestionSetCreateReq(BaseModel):
    name: str
    desc: Optional[str] = None
    type: Optional[int] = None
    question_ids: list[str]
    user_id: str


# 题集修改req
class QuestionSetUpdateReq(BaseModel):
    question_set_identifier: str
    name: Optional[str] = None
    desc: Optional[str] = None
    question_ids: list[str]
    user_id: str


# 题集列表响应
class QuestionSetData(BaseModel):
    id: str
    question_set_identifier: str
    name: str
    questions_number: int
    version: int
    # 是否含有多轮的题目
    has_multi_round: bool
    desc: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    created_user_id: str
    created_user_name: Optional[str] = None



# 问题关联题集req
class QuestionSetDataRelationReq(BaseModel):
    question_set_identifier: str
    question_ids: list[str]


# 题集详情+列表req
class QuestionSetDetailReq(BaseModelPageListReq):
    question_set_identifier: str
    evaluation_id: Optional[str] = None


# 删除题集req
class QuestionSetDeleteReq(BaseModel):
    question_set_identifier: str

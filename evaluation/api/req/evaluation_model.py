from typing import Optional, Any

from pydantic import BaseModel

from evaluation.api.base import ErrorRes
from evaluation.share.orm.platform_schema import User


# 创建评测任务请求
class CreateEvaluationTaskReq(BaseModel):
    system_version: str
    criteria_set_identifier: str
    question_set_identifier: str
    system_version_info: str
    aurl: Optional[str]
    burl: Optional[str]


# 创建评测任务响应
class CreateEvaluationTaskRes(BaseModel):
    evaluation_id: str
    error: ErrorRes


# 分配评测任务请求
class GenerateEvaluationReq(BaseModel):
    user_id: str
    evaluation_id: str


# 根据用户获取目标评测任务号响应
class GetEvaluationIdRes(BaseModel):
    evaluation_id: str
    task_id: str
    evaluation_state: Optional[int]
    error: ErrorRes


# 根据评测任务号获取系统版本和信息响应
class GetSystemInfoRes(BaseModel):
    system_version: Optional[str]
    system_version_info: Optional[str]
    error: ErrorRes


# 评测标准信息
class SyStemCriteriaInfo(BaseModel):
    title: str
    value: str
    rating_key: str
    rating_type: str


# 评测标准得分信息
class CriteriaData(BaseModel):
    title: str
    value: Optional[int]
    rating_key: str
    rating_type: str
    feedback: Optional[str] = ""


# 评测标准列表
class CriteriaListRes(BaseModel):
    criteria_set_identifier: Optional[str]
    data: list[SyStemCriteriaInfo]
    error: ErrorRes


# 获取评测题
class QuestionData(BaseModel):
    question_id: str
    question_text: str
    question_desc: str
    reference_answer: str
    system_answer: str
    task_id: str
    rating: Optional[int]
    feedback: Optional[str] = ""
    complete_state: Optional[int]
    scores: list[CriteriaData]


class QuestionListRes(BaseModel):
    data: Optional[list[QuestionData]]
    error: ErrorRes


class RateQuestionReq(BaseModel):
    task_id: str
    user_id: Optional[str]
    question_id: Optional[str]
    rating_key: str
    rating_value: str
    feedback: Optional[str] = ""


# 提交用户评测任务
class SubmitEvaluationTaskReq(BaseModel):
    user_id: Optional[str]
    task_id: str


# 提交用户评测任务响应
class SubmitEvaluationTaskRes(BaseModel):
    uncompleted_tasks: Optional[list[str]]
    error: ErrorRes


# 评价问题的参考答案
class ReviewQuestionReq(BaseModel):
    task_id: str
    question_id: Optional[str]
    rating: bool
    evaluation_id: Optional[str]
    feedback: Optional[str] = ""


class QuizQuestion(BaseModel):
    question: str
    question_desc: str
    reference_answer: str


class FQuestionData(BaseModel):
    question_id: str
    task_id: str
    question: str


# 分配评测任务接口2响应
class GenerateEvaluationRes(BaseModel):
    data: list[FQuestionData]
    error: ErrorRes


# 评测题系统答案更新
class UpdateQuestionDataReq(BaseModel):
    question_id: str
    question: str
    task_id: str
    sys_type: Optional[str]
    sys_url: Optional[str]


class ABAnswer(BaseModel):
    answer: str
    sys_type: str
    task_id: str


# 评测标准信息
class ABCriteriaInfo(BaseModel):
    title: str
    # value: Optional[int]
    rating_key: str
    rating_type: str
    selected: str


# 获取评测题 AB 测试
class ABQuestionData(BaseModel):
    question_id: str
    question_text: str
    question_desc: str
    reference_answer: str
    # task_id: str
    rating: Optional[int]
    scores: list[ABCriteriaInfo]
    sys_answer: list[ABAnswer]


class ABQuestionListRes(BaseModel):
    data: Optional[list[ABQuestionData]]
    error: ErrorRes


# AB 测试问题评测
class ABRateQuestionReq(BaseModel):
    evaluation_id: str
    task_id: str
    user_id: str
    question_id: str
    rating_key: str
    rating_value: Optional[str]
    sys_type: str


#  AB 测试 评价问题的参考答案
class ABReviewQuestionReq(BaseModel):
    task_id: Optional[str]
    user_id: str
    question_id: str
    rating: bool
    evaluation_id: str


# AB 测试 评审任务结果统计请求
class ABRsStatsReq(BaseModel):
    evaluation_id: str


class PersonnelInfo(BaseModel):
    user_name: str
    user_id: str
    department: str


# AB 测试  评审任务结果统计
class ABRsStats(BaseModel):
    evaluation_id: str
    people_count: int
    personnel_info: list[PersonnelInfo]
    question_count: int
    like_a_count: int
    like_b_count: int
    created_at: str


# AB 测试 评审任务结果统计响应
class ABRsStatsRes(BaseModel):
    # data: ABRsStats
    data: Optional[str]
    error: ErrorRes


class GenerateABEvaluationReq(BaseModel):
    user_id: list[str]
    evaluation_id: str


class FQuestionRef(BaseModel):
    document_id: str
    raw_document_id: str
    document_type: int
    score: int
    msg_id: str
    source: str
    table: str
    source_db: str
    title: str
    chunk_id: str
    start_pos: int
    end_pos: int
    page_content: str


# 根据评测任务号获取召回信息
class GetTaskSourceRes(BaseModel):
    data: list[dict]
    error: ErrorRes


# 客观题集评测结果
class ObjectiveStatReq(BaseModel):
    evaluation_id: Optional[str]


class ObjectiveRes(BaseModel):
    content: str
    evaluation_id: Optional[str]
    error: ErrorRes


class Dim(BaseModel):
    criteria: str
    desc: str
    rating_key: str
    rating_type: str


class DimSetObj(BaseModel):
    id: int
    criteria_set_identifier: str
    criterias: list[Dim]


class DimSetRes(BaseModel):
    data: dict[str, list[DimSetObj]]
    error: ErrorRes


class DimRes(BaseModel):
    data: list[Dim] = None
    error: ErrorRes


class UserInfo(BaseModel):
    name: str
    full_name: str
    user_id: str
    department: str


class UserSetObj(BaseModel):
    id: int
    user_set_identifier: str
    name: str
    user_info: list[UserInfo] = None


class UserSetRes(BaseModel):
    data: list[UserSetObj] = None
    error: ErrorRes


class SetUserRes(BaseModel):
    data: list[UserInfo] = None
    error: ErrorRes


class UserRes(BaseModel):
    data: Optional[list[UserInfo]] = []
    total_count: int
    error: ErrorRes


class Product(BaseModel):
    system: Optional[str] = ""
    product: str
    version: str
    version_info: Optional[str] = ""


class QuizPlanReq(BaseModel):
    plan_id: Optional[str] = None
    plan_name: str
    desc: Optional[str] = ""
    # 评测维度集合ID：准确性，专业性，幻觉
    dim_set_id: str
    question_set_id: str
    user_set_id: str
    # 模型ID
    model_id: int
    # 模型名称
    model_name: str
    # 模型类型1-base2-chat3-embedding4-reranker
    model_type: Optional[int] = 2
    # 模型参数
    model_parameters: str
    # gpu类型
    gpu_type: str
    # 模型数量
    replica_num: int
    # 单副本GPU数量
    replica_gpu_num: int
    # 资源组
    resource_group: str
    # 资源池
    resource_pool: str


class QueryPlanReq(BaseModel):
    # 模型ID
    model_name: Optional[str] = None
    question_set_id: Optional[int] = None
    user_set_id: Optional[int] = None
    status: Optional[list[int]] = None
    keyword: Optional[str] = None
    resource_group: Optional[str] = None
    resource_pool: Optional[str] = None
    plan_id: Optional[str] = None
    create_user_id: Optional[str] = None
    page_size: Optional[int] = None
    user_full_names: Optional[list[str]] = None
    page: Optional[int] = None
    order_str: Optional[str] = None


class UserTaskReq(BaseModel):
    # 模型ID
    id: Optional[int] = None
    evaluation_id: Optional[str] = None
    task_id: Optional[str] = None
    user_id: Optional[int] = None
    admin_id: Optional[list[str]] = None
    task_name: Optional[str] = None
    status: Optional[list[int]] = None
    page_size: Optional[int] = 10
    page: Optional[int] = 1
    order_str: Optional[str] = None
    keyword: Optional[str] = None


class UserTaskInfoReq(BaseModel):
    # 模型ID
    id: Optional[int] = None
    parent_task_id: Optional[str] = None
    task_id: Optional[str] = None
    user_id: Optional[str] = None
    evaluation_id: Optional[str] = None


class CreateQuizPlanRes(BaseModel):
    plan_id: str
    error: ErrorRes


class UpdateQuizPlanRes(BaseModel):
    plan_id: Optional[str] = ""
    error: ErrorRes


class DimSet(BaseModel):
    set_name: str
    set_info: list[str]


class QuizPlanObj(BaseModel):
    id: Optional[int] = None
    plan_id: Optional[str] = None  # 评测计划ID
    plan_name: Optional[str] = None  # 评测计划名称
    quiz_type: Optional[str] = None  # 评测类型人工评测
    quiz_type_name: Optional[str] = None  # 评测类型人工评测
    desc: Optional[str] = None  # 评测计划描述
    dim_set_id: Optional[int] = None  # 评测维度集合ID
    dim_set: Optional[str] = None  # 评测维度集合名称
    question_set_id: Optional[str] = None  # 题集ID
    question_identifier: Optional[str] = None  # 题集ID
    question_set: Optional[str] = None  # 题集名称
    user_set_id: Optional[int] = None  # 用户集合ID
    user_set: Optional[str] = None  # 用户集合名称
    created_user: Optional[str] = None  # 创建人
    updated_user: Optional[str] = None  # 更新人
    created_at: Optional[str] = None  # 创建时间
    updated_at: Optional[str] = None  # 更新时间
    state: Optional[int] = None  # 状态
    state_desc: Optional[str] = None  # 状态描述
    model_id: Optional[int] = None  # 模型ID
    model_name: Optional[str]  # 模型名称
    model_parameters: Optional[str]  # 模型参数
    gpu_type: Optional[str]  # gpu类型
    replica_num: Optional[int] = None  # 副本数量
    replica_gpu_num: Optional[int] = None  # 单副本GPU数量
    resource_group: Optional[str] = None  # 资源组
    resource_pool: Optional[str] = None  # 资源池
    deleted: Optional[int] = None  # 是否删除1-已删除
    eval_product: Optional[list[Product]] = []
    task_num: Optional[int] = 0  # 问题数量
    task_evaluation_num: Optional[int] = 0  # 已推理数量


class QuizPlanListRes(BaseModel):
    data: Optional[list[QuizPlanObj]] = []
    total_count: int
    error: ErrorRes


# class QuizPlanDetailRes(BaseModel):
#     data: Optional[QuizPlanObj] = None
#     error: ErrorRes


class PublishQuizPlanReq(BaseModel):
    plan_id: str


# 重试生成评测任务
class RetryGenerateEvaluationReq(BaseModel):
    task_id: str


class DeleteQuizPlanReq(BaseModel):
    plan_ids: list[str]


class FetchModelsReq(BaseModel):
    page_index: int = 1
    page_size: int = 1000


class EstimationReq(BaseModel):
    model_id: str
    gpu_type: str
    context_length: Optional[int] = None
    seq_len: Optional[int] = None
    type: str
    model_parameters: str
    resource_pool: Optional[str] = None
    resource_group: Optional[str] = None

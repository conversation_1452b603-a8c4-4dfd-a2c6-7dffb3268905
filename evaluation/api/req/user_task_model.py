from typing import Optional

from pydantic import BaseModel


class UserTaskObj(BaseModel):
    id: Optional[int] = None
    evaluation_id: Optional[str] = None
    task_id: Optional[str] = None
    user_id: Optional[str] = None
    task_name: Optional[str] = None
    task_desc: Optional[str] = None
    dim_set_identifier: Optional[str] = None
    question_set_identifier: Optional[str] = None
    question_set_name: Optional[str] = None
    evaluation_type: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    # 1: 未开始  2: 评测中 3: 评测完成
    task_state: Optional[int] = None
    # 任务数量
    task_num: Optional[int] = None
    # 已评测任务的数量
    task_evaluation_num: Optional[int] = None
    created_user: Optional[str] = None
    created_user_name: Optional[str] = None
    rate_enable: Optional[bool] = False

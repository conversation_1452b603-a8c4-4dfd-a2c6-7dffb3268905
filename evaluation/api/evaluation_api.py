from fastapi.params import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.background import BackgroundTasks

from evaluation.api.req.base_model import BaseModelPageListReq
from evaluation.api.req.evaluation_model import (
    GetEvaluationIdRes,
    CriteriaListRes,
    ReviewQuestionReq,
    CreateEvaluationTaskReq,
    RetryGenerateEvaluationReq,
)
from evaluation.api.resp import resp_model
from evaluation.share.orm import database
from evaluation.services import (
    user_evaluation_service,
    system_evaluation_service,
)


def register_evaluation_api(app):
    # 生成系统评测任务号
    @app.post(
        "/api/v2/quiz/evaluation/task/create",
        summary="生成评测任务号",
    )
    async def create_evaluation_task(
            req: CreateEvaluationTaskReq, db: AsyncSession = Depends(database.get_db)
    ):
        task = await system_evaluation_service.create_evaluation_task(db, req)
        return resp_model.success_res(data=task)

    # 获取目标评测任务信息
    @app.get(
        "/api/v2/quiz/evaluations/info",
        summary="获取目标评测任务信息",
    )
    async def get_evaluation_task_by_user_id(
            user_id: str, db: AsyncSession = Depends(database.get_db)
    ):
        task = await user_evaluation_service.get_evaluation_task_by_user_id(db, user_id)
        if task is None:
            return resp_model.success_res(data=None)

        data = GetEvaluationIdRes(
            evaluation_id=task.evaluation_id,
            task_id=task.task_id,
            evaluation_state=task.evaluation_state,
        )
        return resp_model.success_res(data=data)

    # 获取评测标准
    @app.get(
        "/api/v2/quiz/evaluations/criteria",
        summary="获取评测标准",
    )
    async def get_evaluation_criteria_by_evaluation_id(
            evaluation_id: str, db: AsyncSession = Depends(database.get_db)
    ):
        criteria_list, criteria_set_identifier = (
            await system_evaluation_service.get_evaluation_criteria_by_evaluation_id(
                db, evaluation_id
            )
        )
        data = CriteriaListRes(
            data=criteria_list,
            criteria_set_identifier=criteria_set_identifier,
        )
        return resp_model.success_res(data=data)

    # 获取评测题
    @app.get(
        "/api/v3/quiz/evaluations/questions",
        summary="获取评测题",
    )
    async def get_quiz_questions_by_evaluation_id_and_user_id(
            evaluation_id: str, user_id: str, db: AsyncSession = Depends(database.get_db)
    ):
        data = await system_evaluation_service.get_quiz_questions_by_evaluation_id_and_user_id(
            db, user_id
        )
        return resp_model.success_res(data=data)

    @app.get(
        "/api/v2/quiz/evaluations/questions",
        summary="获取评测题",
    )
    async def get_quiz_questions_by_evaluation_id_and_user_id(
            evaluation_id: str, user_id: str, db: AsyncSession = Depends(database.get_db)
    ):
        data = await system_evaluation_service.get_quiz_questions_by_evaluation_id_and_user_id_v2(
            db, user_id
        )
        return resp_model.success_res(data=data)

        # 评价问题的参考答案

    @app.post(
        "/api/v3/quiz/questions/rate",
        summary="评价问题的参考答案",
    )
    async def review_question(
            req: ReviewQuestionReq, db: AsyncSession = Depends(database.get_db)
    ):
        val = await user_evaluation_service.review_question(req, db=db)
        return resp_model.success_res(data=val)

    @app.get(
        "/api/v3/quiz/evaluations/doc-info",
        summary="获取文档信息",
    )
    async def get_recall_documents(task_id: str, db: AsyncSession = Depends(database.get_db)
                                   ):
        val = await system_evaluation_service.get_recall_documents(db, task_id)
        return resp_model.success_res(data=val)

        # 获取系统评测任务列表

    @app.post(
        "/api/v1/quiz/evaluations/task-list",
        summary="获取系统评测任务列表",
    )
    async def get_evaluation_task_list(
            base_req: BaseModelPageListReq, db: AsyncSession = Depends(database.get_db)
    ):
        val = await system_evaluation_service.get_evaluation_task_list(db, base_req)
        return resp_model.success_res(data=val)

    #  重新发布失败的任务
    @app.post(
        "/api/v1/quiz/evaluations/tasks/retry",
        summary="重新发布失败的任务",
    )
    async def retry_failed_tasks(
            base_req: RetryGenerateEvaluationReq, background_tasks: BackgroundTasks,
            db: AsyncSession = Depends(database.get_db)
    ):
        val = await user_evaluation_service.retry_failed_tasks(
            db, background_tasks, base_req.task_id
        )
        return resp_model.success_res(data=val)

    # 获取用户任务详情
    @app.get(
        "/api/v1/quiz/evaluations/tasks/detail",
        summary="获取用户任务详情",
    )
    async def get_user_task_detail(
            task_id: str, db: AsyncSession = Depends(database.get_db)
    ):
        val = await user_evaluation_service.get_user_task_detail(db, task_id)
        return resp_model.success_res(data=val)

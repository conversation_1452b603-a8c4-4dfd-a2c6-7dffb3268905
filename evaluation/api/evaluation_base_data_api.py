from fastapi.params import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.requests import Request

from evaluation.api.resp import resp_model
from evaluation.share.orm import database, evaluation_plan_crud
from evaluation.services import evaluation_plan_service


def register_evaluation_base_data_api(app):
    @app.get("/api/v2/quiz/dim_set",
             summary="获取评测维度集合信息")
    async def get_quiz_dim_sets(req: Request, db: AsyncSession = Depends(database.get_db)):
        dim_set_id = 0
        data = await evaluation_plan_service.get_quiz_dim_sets(db, dim_set_id)
        return resp_model.success_res(data=data)

    @app.get("/api/v2/quiz/dim_set/dim",
             summary="根据评测维度集合ID获取集合具体的评测标准信息")
    async def get_quiz_dim(dim_set_id: int, db: AsyncSession = Depends(database.get_db)):
        if dim_set_id == 0 or dim_set_id < 0:
            raise Exception("dim_set_id 不正确")
        data = await evaluation_plan_service.get_quiz_dim(db, dim_set_id)
        return resp_model.success_res(data=data)

    @app.get("/api/v2/quiz/user_set",
             summary="获取评测人员集合信息")
    async def get_quiz_user_sets(set_name: str = None, quiz_type: str = None,
                                 db: AsyncSession = Depends(database.get_db)):
        data = await evaluation_plan_crud.get_quiz_user_sets_detail(db, set_name, quiz_type)
        return resp_model.success_res(data=data)

    # !DEPRECATED
    @app.get("/api/v2/quiz/user_set/user",
             summary="根据用户集合ID获取结合的具体用户")
    async def get_quiz_dim(user_set_id: int, db: AsyncSession = Depends(database.get_db)):
        if user_set_id == 0 or user_set_id < 0:
            raise Exception("user_set_id 不正确")
        data = await evaluation_plan_service.get_quiz_set_user(db, user_set_id)
        return resp_model.success_res(data=data)

    @app.get("/api/v2/quiz/user",
             summary="获取用于评测的所以用户信息")
    async def get_quiz_dim(search_term: str, page_size: int = 20, page: int = 1, db: AsyncSession = Depends(database.get_db)):
        data, total = await evaluation_plan_service.get_user_info(db, page_size, page,search_term)
        return resp_model.success_page_res(page=page, page_size=page_size, total=total,
                                           data=data)

from fastapi.params import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from evaluation.api.req.base_model import BaseModelPageListReq
from evaluation.api.req.evaluation_system_model import SystemQuestionCreateReq, \
    SystemQuestionDeleteReq, SystemQuestionUpdateReq
from evaluation.api.resp import resp_model
from evaluation.share.orm import database
from evaluation.services import system_evaluation_service


def register_questions_api(app):
    # 获取系统所有题目
    @app.post("/api/v1/quiz/evaluations/system-questions",
              summary="获取系统所有题目")
    async def get_system_questions(req: BaseModelPageListReq, db: AsyncSession = Depends(database.get_db)):
        count, val = await system_evaluation_service.get_system_quiz_questions(db, req)
        return resp_model.success_page_res(page=req.page, page_size=req.page_size, total=count,
                                           data=val)

    # 获取题型列表
    @app.get("/api/v1/quiz/evaluations/question-types", summary="获取题型列表")
    async def get_question_types(db: AsyncSession = Depends(database.get_db)):
        val = await system_evaluation_service.get_system_quiz_question_types(db)
        return resp_model.success_res(data=val)

    # 添加问题到系统题库
    @app.post("/api/v1/quiz/evaluations/system-questions/add",
              summary="添加问题到系统题库")
    async def add_system_question(val: SystemQuestionCreateReq, db: AsyncSession = Depends(database.get_db)):
        val = await system_evaluation_service.add_system_question(db, val)
        return resp_model.success_res(data=val)

    # 删除系统题库问题
    @app.post("/api/v1/quiz/evaluations/system-questions/delete",
              summary="删除系统题库问题")
    async def delete_system_question(val: SystemQuestionDeleteReq, db: AsyncSession = Depends(database.get_db)):
        val = await system_evaluation_service.delete_system_question(db, val)
        return resp_model.success_res(data=val)

    # 更新系统题库问题
    @app.post("/api/v1/quiz/evaluations/system-questions/update",
              summary="更新系统题库问题")
    async def update_system_question(val: SystemQuestionUpdateReq, db: AsyncSession = Depends(database.get_db)):
        val = await system_evaluation_service.update_system_question(db, val)
        return resp_model.success_res(data=val)

    # 获取某个系统题目
    @app.get("/api/v1/quiz/evaluations/system-questions/{question_id}",
             summary="获取某个系统题目")
    async def get_system_question(question_id: str, db: AsyncSession = Depends(database.get_db)):
        val = await system_evaluation_service.get_system_quiz_question(db, question_id)
        return resp_model.success_res(data=val)

    # 获取废弃题目提示信息
    # @app.get("/api/v1/quiz/evaluations/system-questions/{question_id}/tip",
    #          summary="获取废弃题目提示信息")
    # async def get_system_question_quiz(question_id: str, db: AsyncSession = Depends(database.get_db)):
    #     val = await system_evaluation_service.check_discarded_question(db, question_id)
    #     return resp_model.success_res(data=val)


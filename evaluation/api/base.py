import json
from enum import Enum
from typing import Any, Union
from typing import List
from typing import Optional

import pydantic
from pydantic import BaseModel, Field
from starlette.responses import JSONResponse


class RoleType(Enum):
    """ 支持的模型类型"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    STATE = "state"
    AI = "ai"
    QUERY_HISTORY_INTENTION = "query_history_intention"


class AuthType(Enum):
    """ 支持的鉴权类型"""
    USS = "USS"
    IAM = "IAM"
    TOKEN = "TOKEN"


class ServiceStatus(Enum):
    """ 服务状态"""
    ACTIVE = "Active"
    EXPIRED = "Expired"
    CANCELLED = "Cancelled"
    OVERDUE = "Overdue"
    NOTOPENED = "NotOpened"
    OPENING = "Opening"
    DEACTIVE = "DeActive"


class ErrorRes(BaseModel):
    errorCode: int
    errorMessage: str


class AuthErrorRes(BaseModel):
    authType: str = None
    errorCode: int
    errorMessage: str


NOT_LOGIN = AuthErrorRes(errorCode=10401, errorMessage="用户尚未登录。")
NOT_PERMISSIONS = AuthErrorRes(errorCode=10400, errorMessage="权限不足。")
EXPIRED = AuthErrorRes(errorCode=10402, errorMessage="服务已到期，用户需要续费才能继续使用。")
CANCELLED = AuthErrorRes(errorCode=10403, errorMessage="服务已被取消。")
OVERDUE = AuthErrorRes(errorCode=10404, errorMessage="用户服务处于欠费状态。")
NOTOPENED = AuthErrorRes(errorCode=10405, errorMessage="服务未开通。")
OPENING = AuthErrorRes(errorCode=10406, errorMessage="服务开通中。")
CONSOLE_FAIL = AuthErrorRes(errorCode=10500, errorMessage="请求 console 失败。")






class UserInfo(BaseModel):
    user_id: str
    user_name: str
    permissions: List[str] | None = []
    is_main_account: bool
    service_status: str | None = None
    # headers: Optional[Mapping[str, str]] = None


class UserInfoRes(BaseModel):
    user_info: UserInfo | None = None
    auth_error: AuthErrorRes


def check_status():
    return "ok"


from fastapi import FastAP<PERSON>
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from evaluation.api.resp import resp_model

def register_validation_exception_handler(app: FastAPI):
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request, exc: RequestValidationError):
        # 生成自定义的错误响应
        errors = []
        for error in exc.errors():
            field = error.get("loc")[-1]  # 获取错误字段
            message = error.get("msg")    # 错误提示信息
            errors.append(f"{field}: {message}")

        return JSONResponse(
            status_code=200,
            content=resp_model.error_res(code=500, msg="; ".join(errors)).dict()
        )

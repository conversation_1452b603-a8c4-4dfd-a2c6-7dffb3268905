from fastapi import FastAPI

from fastapi.params import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.api.req.eval_report_model import ReportGroupByQuestionReq, SubmitDislikeReq
from evaluation.api.resp import resp_model
from evaluation.services import user_quiz_tasks_criteria_report_service, dislike_service
from evaluation.share.orm import database


def register_eval_report_api(app: FastAPI):
    @app.get(
        "/api/v1/user/evaluations/summary_report",
        summary="汇总报告",
    )
    async def summary_report(plan_id: str, db: AsyncSession = Depends(database.get_db)):
        summary_report = await user_quiz_tasks_criteria_report_service.summary_report(db, plan_id)
        return resp_model.success_res(data=summary_report)

    @app.post(
        "/api/v1/user/evaluations/report_group_by_question",
        summary="按问题汇总",
    )
    async def report_group_by_question(req: ReportGroupByQuestionReq, db: AsyncSession = Depends(database.get_db)):
        total, summary_report = await user_quiz_tasks_criteria_report_service.report_group_by_question(db, req.plan_id,
                                                                                                       req.page,
                                                                                                       req.page_size)
        return resp_model.success_page_res(page=req.page, page_size=req.page_size, total=total, data=summary_report)

    @app.post(
        "/api/v1/user/evaluations/report_group_by_user",
        summary="按问题汇总",
    )
    async def report_group_by_user(req: ReportGroupByQuestionReq, db: AsyncSession = Depends(database.get_db)):
        total, summary_report = await user_quiz_tasks_criteria_report_service.report_group_by_user(db, req.plan_id,
                                                                                                   req.page,
                                                                                                   req.page_size)
        return resp_model.success_page_res(page=req.page, page_size=req.page_size, total=total, data=summary_report)

    @app.post(
        "/api/v1/user/evaluations/list_dislike",
        summary="按问题汇总",
    )
    async def list_dislike(req: ReportGroupByQuestionReq, db: AsyncSession = Depends(database.get_db)):
        total, summary_report = await user_quiz_tasks_criteria_report_service.list_dislike(db, req.plan_id,
                                                                                           req.page, req.page_size)
        return resp_model.success_page_res(page=req.page, page_size=req.page_size, total=total, data=summary_report)

    @app.post(
        "/api/v1/user/evaluations/submit_dislike",
        summary="点踩",
    )
    async def submit_dislike(req: SubmitDislikeReq, db: AsyncSession = Depends(database.get_db)):
        await dislike_service.submit_dislike(db, req.plan_id, req.question_id, req.content)
        return resp_model.success_res(data=None)

from typing import Optional, Union, List, Any
from pydantic import BaseModel
# error
class PageDataResp(BaseModel):
    page: Optional[int] = None
    page_size: Optional[int] = None
    total_count: Optional[int] = 0
    data: Any = None  # 可以是任意类型


class CommonDataResp(BaseModel):
    code: Optional[int] = 200
    message: Optional[str] = "success"
    data: Optional[Any] = None  # 可以是任意类型


def error_res(code: int = 500, msg: str = "error") -> CommonDataResp:
    return CommonDataResp(code=code, message=msg, data=None)


def success_res(data: Optional[Any] = None) -> CommonDataResp:
    return CommonDataResp(code=200, message="success", data=data)


def success_page_res(
        page: int, page_size: int, total: int, data: Optional[Any] = None
) -> CommonDataResp:
    """
    返回分页数据的成功响应对象
    """
    page_data_resp = PageDataResp(
        page=page,
        page_size=page_size,
        total_count=total,  # 确保 total_count 赋值
        data=data  # data 可以是字典或列表
    )

    resp = success_res(data=page_data_resp)

    return resp

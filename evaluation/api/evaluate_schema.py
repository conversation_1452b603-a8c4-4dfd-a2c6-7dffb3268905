from enum import Enum
from typing import List, Optional

from pydantic import BaseModel


class ModelType(Enum):
    """ 支持的模型类型"""
    GPT35 = "gpt3.5"
    MINIMAX = "minimax"
    QWEN_14B_BASE = "qwen-14b-base"
    QWEN_14B_SFT = "qwen-14b-sft-full-20231128-10w-single"
    QWEN_7B_SFT = "qwen-7b-sft-full-20231128-10w-single"


class EvaluationTaskType(Enum):
    """
    评任务类型
    multiple_select：多选题
    single_select：单选题
    fill_in_the_blank：填空题
    true_or_false：    判断题
    multi_round_qa：    多轮对话
    """
    MULTIPLE_SELECT = "multiple_select"
    SINGLE_SELECT = "single_select"
    FILL_IN_THE_BLANK = "fill_in_the_blank"
    TRUE_OR_FALSE = "true_or_false"
    MULTI_ROUND_QA = "multi_round_qa"



class EvaluationTaskSubType(Enum):
    """ 评任务子类型"""
    UN_RECALL = "un_recall"
    RECALL = "recall"


class AnswerType(Enum):
    """ 任务结果类型"""
    CORRECT = "Y"
    UNCORRECT = "N"
    TFCORRECT = "correct"
    TFINCORRECT = "incorrect"


class TemplateName(Enum):
    """
    支持的模型类型
    multiple_select：多选题
    single_select：单选题
    fitb：填空题
    tf：    判断题
    """
    MULTIPLE_RECALL = "object_evaluate_multiple_select_recall"
    MULTIPLE_UNRECALL = "object_evaluate_multiple_select_unrecall"
    SINGLE_RECALL = "object_evaluate_single_select_recall"
    SINGLE_UNRECALL = "object_evaluate_single_select_unrecall"
    TF_RECALL = "object_evaluate_tf_recall"
    TF_UNRECALL = "object_evaluate_tf_unrecall"
    FITB_GPT35_RECALL = "object_evaluate_gpt35_fitb_recall"
    FITB_GPT35_UNRECALL = "object_evaluate_gpt35_fitb_unrecall"
    FITB_MINIMAX_RECALL = "object_evaluate_minimax_fitb_recall"
    FITB_MINIMAX_UNRECALL = "object_evaluate_minimax_fitb_unrecall"

    MULTIPLE_QWEN_RECALL = "object_evaluate_qwen_multiple_select_recall"
    MULTIPLE_QWEN_UNRECALL = "object_evaluate_qwen_multiple_select_unrecall"
    SINGLE_QWEN_RECALL = "object_evaluate_qwen_single_select_recall"
    SINGLE_QWEN_UNRECALL = "object_evaluate_qwen_single_select_unrecall"

    TF_QWEN_RECALL = "object_evaluate_qwen_tf_recall"
    TF_QWEN_UNRECALL = "object_evaluate_qwen_tf_unrecall"
    FITB_QWEN_RECALL = "object_evaluate_qwen_fitb_recall"
    FITB_QWEN_UNRECALL = "object_evaluate_qwen_fitb_unrecall"


class EvaluateResult(BaseModel):
    """
    评测结果
    """
    query: Optional[str]
    answer_list: Optional[List[str]]
    answer: Optional[List[str]]
    is_correct: Optional[str]
    recall_count: Optional[int]


class EvaluateResultFitb(BaseModel):
    """
    评测结果  填空题
    """
    query: Optional[str]
    q_list: Optional[List[str]]
    answer_list: Optional[List[str]]
    answer: Optional[str]
    is_correct: Optional[str]
    recall_count: Optional[int]

from typing import Optional

from fastapi import FastAP<PERSON>, Request
from fastapi.params import Query

from evaluation.api.req.user_model import (
    EvaluationGroupCreateRequest,
    EvaluationGroupUpdateRequest, DeleteUserSetReq,
)
from evaluation.api.resp import resp_model
from evaluation.share.orm import database
from evaluation.services import user_service, inference_service
from fastapi.params import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from evaluation.api.req.base_model import BaseModelPageListReq
from evaluation.share.util.user import (
    get_login_user_info
)


def register_user_api(app: FastAPI):
    @app.get(
        "/api/v1/user/get/userinfo",
        summary="获取用户信息",
    )
    async def get_user_info():
        info = get_login_user_info()
        return resp_model.success_res(data=info)

    @app.get(
        "/api/v1/user/list",
        summary="获取用户列表"
    )
    async def get_user_list(
            search_term: str = Query(
                None, title="Search query", description="Query string for searching users"
            ),
    ):
        val = await user_service.get_user_list(search_term)
        return resp_model.success_res(data=val)

    # 获取评测组列表
    @app.post(
        "/api/v1/user/evaluation_group_list",
        summary="获取评测组列表",
    )
    async def get_evaluation_group_list(
            req: BaseModelPageListReq, db: AsyncSession = Depends(database.get_db)
    ):
        total_count, val = await user_service.get_evaluation_group_list(db, req)
        return resp_model.success_page_res(page=req.page, page_size=req.page_size, total=total_count, data=val)

    # 新建评测组
    @app.post(
        "/api/v1/user/evaluation_group/create",
        summary="新建评测组",
    )
    async def create_evaluation_group(
            req: EvaluationGroupCreateRequest, db: AsyncSession = Depends(database.get_db)
    ):
        val = await user_service.create_evaluation_group(db, req)
        return resp_model.success_res(data=val)

    @app.post(
        "/api/v1/user/evaluation_group/delete_evaluation_group",
        summary="删除评测组",
    )
    async def delete_evaluation_group(
            req: DeleteUserSetReq, db: AsyncSession = Depends(database.get_db)
    ):
        await user_service.delete_evaluation_group(db, req.user_set_identifiers)
        return resp_model.success_res(data=None)

    # 更新评测组
    @app.post(
        "/api/v1/user/evaluation_group/update",
        summary="更新评测组",
    )
    async def update_evaluation_group(
            req: EvaluationGroupUpdateRequest, db: AsyncSession = Depends(database.get_db)
    ):
        val = await user_service.update_evaluation_group(db, req)
        return resp_model.success_res(data=val)

    # 评测组详情
    @app.get(
        "/api/v1/user/evaluation_group/detail",
        summary="评测组详情",
    )
    async def get_evaluation_group_detail(
            user_set_identifier: str, db: AsyncSession = Depends(database.get_db)
    ):
        val = await user_service.get_evaluation_group_detail(
            db, user_set_identifier
        )
        return resp_model.success_res(data=val)

    @app.get(
        "/api/v1/evaluation_groups/{user_set_identifier}/users",
        summary="获取评测组计划详情的评测人员列表",
    )
    async def fetch_users_by_ids(
            user_set_identifier: str,
            evaluation_id: Optional[str] = None,
            search_term: str = Query(
                None, title="Search query", description="Query string for searching users"
            ), db: AsyncSession = Depends(database.get_db)
    ):

        # 获取快照
        if evaluation_id:
            val = await inference_service.get_members_from_snapshot(db, evaluation_id, user_set_identifier)
            if val:
                return resp_model.success_res(data=val)

        # 查真实数据
        val = await user_service.get_evaluation_group_detail(
            db, user_set_identifier
        )
        val = await user_service.filter_evaluation_group_members(val, search_term)
        return resp_model.success_res(data=val)

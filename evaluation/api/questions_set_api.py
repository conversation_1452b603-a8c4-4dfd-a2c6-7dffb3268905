from fastapi.params import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from evaluation.api.req.base_model import BaseModelPageListReq
from evaluation.api.req.evaluation_system_model import QuestionSetDeleteReq, SystemQuestionUpdateReq, \
    QuestionSetCreateReq, \
    QuestionSetUpdateReq, QuestionSetDetailReq
from evaluation.api.resp import resp_model
from evaluation.share.orm import database
from evaluation.services import system_evaluation_service, inference_service


def register_questions_set_api(app):
    # 创建题集
    @app.post("/api/v1/quiz/evaluations/question-set/create",
              summary="创建题集")
    async def create_question_set(val: QuestionSetCreateReq, db: AsyncSession = Depends(database.get_db)):
        val = await system_evaluation_service.create_question_set(db, val)
        return resp_model.success_res(data=val)

    # 获取题集列表
    @app.post("/api/v1/quiz/evaluations/question-set/list",
              summary="获取题集列表")
    async def get_question_sets(req: BaseModelPageListReq, db: AsyncSession = Depends(database.get_db)):
        count, val = await system_evaluation_service.get_question_sets(db, req)
        return resp_model.success_page_res(page=req.page, page_size=req.page_size, total=count,
                                           data=val)

    # 问题关联题集
    @app.post("/api/v1/quiz/evaluations/question-set/relation",
              summary="问题关联题集")
    async def relation_question_set(val: SystemQuestionUpdateReq, db: AsyncSession = Depends(database.get_db)):
        val = await system_evaluation_service.relation_question_set(db, val)
        return resp_model.success_res(data=val)

    # 修改题集
    @app.post("/api/v1/quiz/evaluations/question-set/update",
              summary="修改题集")
    async def update_question_set(val: QuestionSetUpdateReq, db: AsyncSession = Depends(database.get_db)):
        val = await system_evaluation_service.update_question_set(db, val)
        return resp_model.success_res(data=val)

    # 获取题集详情接口
    @app.post("/api/v1/quiz/evaluations/question-set/detail",
              summary="获取题集详情接口")
    async def get_question_set_detail(req: QuestionSetDetailReq, db: AsyncSession = Depends(database.get_db)):
        # 尝试获取快照
        if req.evaluation_id:
            val = await inference_service.get_question_set_from_snapshot(db, req.evaluation_id, req.question_set_identifier)
            if val:
                return resp_model.success_res(data=val)

        # 查询
        val = await system_evaluation_service.get_question_set_detail(db, req)
        return resp_model.success_res(data=val)

    # 删除题集
    @app.post("/api/v1/quiz/evaluations/question-set/delete",
              summary="删除题集")
    async def delete_question_set(val: QuestionSetDeleteReq,
                                  db: AsyncSession = Depends(database.get_db)):
        val = await system_evaluation_service.delete_question_set(db, val)
        return resp_model.success_res(data=val)

    # 获取废弃题集关联的计划提示
    @app.get("/api/v1/quiz/evaluations/question-set/{question_set_identifier}/tip",
             summary="获取废弃题集关联的计划提示")
    async def get_evaluation_plan_by_question_set(question_set_identifier: str,
                                                  db: AsyncSession = Depends(database.get_db)):
        val = await system_evaluation_service.check_task_by_question_set_identifier(db, question_set_identifier)
        return resp_model.success_res(data=val)

from fastapi.params import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.api.req.evaluation_model import (
    RateQuestionReq,
    SubmitEvaluationTaskReq,
    UserTaskReq,
)
from evaluation.api.resp import resp_model
from evaluation.share.orm import database
from evaluation.services import (
    user_evaluation_service,
)


def register_user_eval_task_api(app):
    # 对问题评分
    @app.post(
        "/api/v3/quiz/evaluations-questions/rate",
        summary="对问题评分",
    )
    async def rate_quiz_question(
            req: RateQuestionReq, db: AsyncSession = Depends(database.get_db)
    ):
        await user_evaluation_service.rate_quiz_question(req, db=db)
        return resp_model.success_res(data=None)

    # 提交用户评测任务
    @app.post(
        "/api/v2/quiz/evaluations/complete",
        summary="提交用户评测任务",
    )
    async def submit_evaluation_task(
            request: SubmitEvaluationTaskReq, db: AsyncSession = Depends(database.get_db)
    ):
        await user_evaluation_service.submit_evaluation_task(request, db=db)
        return resp_model.success_res(data=None)

    # 获取用户任务列表
    @app.post(
        "/api/v1/quiz/evaluations/tasks",
        summary="获取用户任务列表",
    )
    async def get_user_tasks(
            base_req: UserTaskReq, db: AsyncSession = Depends(database.get_db)
    ):
        total_count, val = await user_evaluation_service.get_user_tasks(db, base_req)
        return resp_model.success_page_res(page=base_req.page, page_size=base_req.page_size, total=total_count,
                                           data=val)

    # 获取管理员列表
    @app.get(
        "/api/v1/quiz/evaluations/admin_users",
        summary="获取管理员列表",
    )
    async def get_admin_users(db: AsyncSession = Depends(database.get_db)):
        result = await user_evaluation_service.get_admin_users(db)
        return resp_model.success_res(data=result)

    # 获取用户任务详情
    @app.get(
        "/api/v2/quiz/evaluations/tasks/detail",
        summary="获取用户任务详情v2",
    )
    async def get_user_task_detail_v2(
            task_id: str, db: AsyncSession = Depends(database.get_db)
    ):
        val = await user_evaluation_service.get_user_task_detail_v2(db, task_id)
        return resp_model.success_res(data=val)

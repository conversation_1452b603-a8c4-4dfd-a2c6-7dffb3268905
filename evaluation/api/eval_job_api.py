import threading

from fastapi import FastAP<PERSON>

from fastapi.params import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.api.req.evaluation_model import PublishQuizPlanReq
from evaluation.api.resp import resp_model
from evaluation.services import inference_service, \
    evaluation_plan_service, plan_task_service
from evaluation.share.orm import database


def register_eval_job_api(app: FastAPI):
    @app.get(
        "/api/v1/inner/job/update_inference_task",
        summary="更新推理服务状态",
    )
    async def update_inference_task():
        thread = threading.Thread(target=await inference_service.update_inference_task())
        thread.start()
        return resp_model.success_res(data=None)

    @app.get(
        "/api/v1/inner/job/begin_inference_task",
        summary="推理开始",
    )
    async def begin_inference_task(plan_id: str):
        thread = threading.Thread(target=await inference_service.begin_inference_task(plan_id))
        thread.start()
        return resp_model.success_res(data=None)

    @app.get(
        "/api/v1/inner/job/delete_service",
        summary="释放推理资源",
    )
    async def delete_service(service_id: int):
        await evaluation_plan_service.delete_service(service_id)
        return resp_model.success_res(data=None)

    # 发布用户评测任务
    @app.post("/api/v1/inner/job/publish_user_task", summary="发布用户评测任务")
    async def publish_quiz_plan(request: PublishQuizPlanReq, db: AsyncSession = Depends(database.get_db)):
        if not request.plan_id:
            raise Exception("plan_id 不能为空")
        await plan_task_service.generate_user_task(db, request.plan_id)
        return resp_model.success_res(data=None)

    # 生成任务快照
    @app.get("/api/v1/inner/job/generate_snapshot", summary="生成任务快照")
    async def generate_snapshot(plan_id: str, db: AsyncSession = Depends(database.get_db)):
        if not plan_id:
            raise Exception("plan_id 不能为空")
        await inference_service.generate_snapshot(db, plan_id)
        return resp_model.success_res(data=None)

    @app.get("/api/v1/inner/job/get_snapshot", summary="获取任务快照")
    async def get_snapshot(plan_id: str, db: AsyncSession = Depends(database.get_db)):
        if not plan_id:
            raise Exception("plan_id 不能为空")
        var = await inference_service.get_snapshot(db, plan_id, None, None)
        return resp_model.success_res(data=var)

import logging
import re

from fastapi.params import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.api.req.evaluation_model import DeleteQuizPlanReq, QuizPlanReq, PublishQuizPlanReq, EstimationReq, \
    QueryPlanReq
from evaluation.api.resp import resp_model
from evaluation.share.orm import database
from evaluation.services import evaluation_plan_service, model_service, user_task_service

logger = logging.getLogger("plaintext")


def register_evaluation_plan_api(app):
    # 创建评测计划
    @app.post("/api/v2/quiz/plan/create",
              summary="创建评测计划")
    async def create_quiz_plan(request: QuizPlanReq, db: AsyncSession = Depends(database.get_db)):
        plan_name = request.plan_name
        if not plan_name:
            return resp_model.error_res(msg="任务名不能为空")
        if not re.match(r"^[\u4e00-\u9fffa-zA-Z0-9_.-]{2,50}$", plan_name):
            return resp_model.error_res(msg="任务名称包含不支持的字符,请输入2-50字符，仅支持中文、英文、数字和中划线(-)下划线(_)英文.")
        quiz_plan, message = await evaluation_plan_service.create_quiz_plan(db, request)
        return resp_model.success_res(data=quiz_plan)

    @app.post("/api/v2/quiz/plan/list",
              summary="获取评测计划列表信息")
    async def get_quiz_plans(queryReq: QueryPlanReq, db: AsyncSession = Depends(database.get_db)):
        data, total = await evaluation_plan_service.get_quiz_plans(db, queryReq)
        return resp_model.success_page_res(page=queryReq.page, page_size=queryReq.page_size, total=total,
                                           data=data)

    @app.get("/api/v2/quiz/plan/detail",
             summary="获取评测计划详情")
    async def get_quiz_plan_detail(plan_id: str, db: AsyncSession = Depends(database.get_db)):
        if not plan_id:
            raise Exception("plan_id 不能为空")
        data = await evaluation_plan_service.get_quiz_plan_detail(db, plan_id)
        return resp_model.success_res(data=data)

    @app.get("/api/v2/quiz/plan/copy",
             summary="克隆评测计划")
    async def get_quiz_plan_copy(plan_id: str, db: AsyncSession = Depends(database.get_db)):
        if not plan_id:
            raise Exception("plan_id 不能为空")
        await evaluation_plan_service.copy_plan(db, plan_id)
        return resp_model.success_res(data=None)

    @app.post("/api/v2/quiz/plan/modify",
              summary="修改评测计划")
    async def update_quiz_plan(request: QuizPlanReq, db: AsyncSession = Depends(database.get_db)):
        if not request.plan_id:
            raise Exception("plan_id 不能为空")
        plan_name = request.plan_name
        if not plan_name:
            return resp_model.error_res(msg="任务名不能为空")
        if not re.match(r"^[\u4e00-\u9fffa-zA-Z0-9_.-]{2,50}$", plan_name):
            return resp_model.error_res(msg="任务名称包含不支持的字符,请输入2-50字符，仅支持中文、英文、数字和中划线(-)下划线(_)英文.")
        success, errormessage = await evaluation_plan_service.update_quiz_plan(db, request)
        if success:
            return resp_model.success_res(data=None)
        else:
            message = f"modify quiz plan failed {errormessage or ''}"
            return resp_model.error_res(code=500, msg=message)

    @app.post("/api/v1/quiz/plan/publish",
              summary="发布评测计划")
    async def publish_quiz_plan(request: PublishQuizPlanReq,
                                db: AsyncSession = Depends(database.get_db)):
        if not request.plan_id:
            raise Exception("plan_id 不能为空")
        await evaluation_plan_service.publish_quiz_plan(db, request.plan_id)
        return resp_model.success_res(data=None)

    @app.post("/api/v1/quiz/plan/delete",
              summary="删除评测计划")
    async def delete_quiz_plan(req: DeleteQuizPlanReq, db: AsyncSession = Depends(database.get_db)):
        if len(req.plan_ids) == 0:
            raise Exception("id不能为空")
        await evaluation_plan_service.delete_quiz_plan(db, req.plan_ids)
        return resp_model.success_res(data=None)


    @app.get("/api/v1/quiz/plan/task_cancel",
                summary="取消任务")
    async def cancel_quiz_task(plan_id: str, db: AsyncSession = Depends(database.get_db)):
        if not plan_id:
            raise Exception("plan_id 不能为空")
        await evaluation_plan_service.cancel_task(db, plan_id)
        return resp_model.success_res(data=None)



    @app.get("/api/v1/quiz/plan/task_progress",
             summary="任务进度条")
    async def delete_quiz_plan(plan_id: str, db: AsyncSession = Depends(database.get_db)):
        if not plan_id:
            raise Exception("plan_id 不能为空")
        val = await user_task_service.list_task_progress(db, plan_id)
        return resp_model.success_res(data=val)

    @app.get("/api/v1/quiz/plan/fetch_models",
             summary="获取模型列表")
    async def fetch_models(page_index: int = 1, page_size: int = 1000):
        val = await model_service.fetch_models(page_index, page_size)
        res = resp_model.success_res(data=val.data)
        return res

    @app.get("/api/v1/quiz/plan/resource_pool_groups",
             summary="获取资源池与资源组列表")
    async def get_inference_resource_pool_list():
        data = await model_service.get_inference_resource_pool_groups()
        return resp_model.success_res(data=data)

    @app.get("/api/v1/quiz/plan/resources",
             summary="获取资源池与资源组下的资源列表")
    async def get_inference_service_free_resource(pool: str, group: str):
        data = await model_service.get_inference_service_free_resource(pool, group)
        return resp_model.success_res(data=data)

    @app.post("/api/v1/quiz/plan/estimate",
              summary="获取预估的副本数")
    async def estimate(request: EstimationReq):
        data = await model_service.estimate(request)
        return resp_model.success_res(data=data)


    @app.get("/api/v1/quiz/plan/users",
             summary="获取任务管理员列表")
    async def get_quiz_plan_users(db: AsyncSession = Depends(database.get_db)):
        data = await evaluation_plan_service.get_quiz_plan_users(db)
        return resp_model.success_res(data=data)

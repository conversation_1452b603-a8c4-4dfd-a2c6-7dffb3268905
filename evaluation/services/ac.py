import logging
from typing import List, Optional

import httpx
from evaluation.services.evaluation_plan_service import get_header
from evaluation.share.util.app_config import CONFIG
from evaluation.share.util.json_utils import JsonUtils
from evaluation.share.orm.platform_schema import User

url_uss_role_users = "http://************/third_party/business/get_role_employees?business_code=knowledge_base_cc&role_id=1221"
USS_API_HEADER = {
    "Content-Type": "application/json",
    "Authorization": "Basic a25vd2xlZGdlX2Jhc2VfY2M6OTY2ZDgwZGI3ODVjNTE3NTdmYjZmOTEwODdjMWUzY2Y=",
    "Host": "uss.api.sdns.ksyun.com"
}
# 评测人员角色id
EVALUATION_ROLE_ID = 1221

logger = logging.getLogger("plaintext")


# 从uss获取用户列表
async def get_user_list_from_uss(search_term: Optional[str]) -> List[User]:
    logger.info(f"get_user_list_uss begin: {search_term}")
    async with httpx.AsyncClient() as client:
        response = await client.get(
            url=url_uss_role_users,
            headers=USS_API_HEADER,
        )
        logger.info(f"get_user_list_uss end.url:{url_uss_role_users} headers:{USS_API_HEADER}")
        result = response.json()
        logger.info(f"get_user_list_uss end: {result}")
        if search_term:
            search_term_lower = search_term.lower()
            filtered_users = [
                User(
                    user_id=str(employee["employee_id"]),
                    user_no=str(employee["employee_id"]),
                    name=employee["email"].split("@")[0].lower(),
                    full_name=employee["real_name"],
                    department=employee["dept_full_name"],
                )
                for employee in result["info"]["employees"]
                if search_term_lower in employee["real_name"].lower()
                   or search_term_lower in employee["email"].split("@")[0].lower()
            ]
        else:
            filtered_users = [
                User(
                    user_id=str(employee["employee_id"]),
                    user_no=str(employee["employee_id"]),
                    name=employee["email"].split("@")[0].lower(),
                    full_name=employee["real_name"],
                    department=employee["dept_full_name"],
                )
                for employee in result["info"]["employees"]
            ]

        return filtered_users


# 从hanhai-common获取用户列表
async def get_user_list_from_common(search_term: Optional[str]) -> List[User]:
    logger.info(f"get_user_list_poc begin: {search_term}")
    # url = f"{CONFIG.get('common', 'platform')}/basic_user/api/v1/users"
    # role_id = 5  # 评测人员角色id
    # role id or code
    role = CONFIG.get('channel', 'role')
    if not role:
        role = "5"

    url = f"{CONFIG.get('common', 'platform')}/api/v1/roles/{role}/users"

    header = get_header()
    # header["host"] = CONFIG.get('common', 'host')

    async with httpx.AsyncClient() as client:
        response = await client.get(
            url=url,
            headers=header,
        )

        logger.info(f"get_user_list_from_poc url: {url},head:{JsonUtils.to_json(header)},userInfo: {response}")
        result = response.json()

        if search_term:
            search_term_lower = search_term.lower()
            filtered_users = [
                User(
                    user_id=str(employee["user_id"]) if employee["user_id"] is not None else 0,
                    user_no=str(employee["user_id"]) if employee["user_id"] is not None else "",
                    name=employee["user_name"] if employee["user_name"] is not None else "",
                    full_name=employee["user_full_name"] if employee["user_full_name"] is not None else "",
                    department=employee["user_department"] if employee["user_department"] is not None else "",
                    # roles=employee["roles"] if employee["roles"] is not None else [],
                )
                for employee in result["data"]
                if search_term_lower in employee["user_name"].lower()
                   or search_term_lower in employee["user_full_name"].lower()
            ]
        else:
            filtered_users = [
                User(
                    user_id=str(employee["user_id"]) if employee["user_id"] is not None else 0,
                    user_no=str(employee["user_id"]) if employee["user_id"] is not None else "",
                    name=employee["user_name"] if employee["user_name"] is not None else "",
                    full_name=employee["user_full_name"] if employee["user_full_name"] is not None else "",
                    department=employee["user_department"] if employee["user_department"] is not None else "",
                    # roles=employee["roles"] if employee["roles"] is not None else [],
                )
                for employee in result["data"]
            ]

        return filtered_users


# # 从kcde的authserver获取用户列表
async def get_user_list_from_kcde(search_term: Optional[str]) -> List[User]:
    logger.info(f"get_user_list_kcde begin: {search_term}")
    # role_id = 10  # 评测人员角色id
    # role id or code
    role = CONFIG.get('channel', 'role')
    if not role:
        role = "model_evaluator"
    url = f"{CONFIG.get('auth_server', 'platform')}/authserver/permit/{role}/users"
    # source = "HH"
    # 用户通过hanhai平台创建
    user_source = CONFIG.get('channel', 'source')
    if not user_source:
        user_source = "HH"

    header = get_header()
    # header["host"] = CONFIG.get('common', 'host')

    async with httpx.AsyncClient() as client:
        response = await client.get(
            url=url,
            headers=header,
        )

        logger.info(f"get_user_list_from_kcde url: {url},head:{JsonUtils.to_json(header)},userInfo: {response.text}")
        result = response.json()

        if search_term:
            search_term_lower = search_term.lower()
            filtered_users = [
                User(
                    user_id=str(employee["id"]) if employee["id"] is not None else 0,
                    user_no=str(employee["id"]) if employee["id"] is not None else "",
                    name=employee["name"] if employee["name"] is not None else "",
                    full_name=employee["alias"] if employee["alias"] is not None else "",
                    department="",
                    # roles=employee["roles"] if employee["roles"] is not None else [],
                    remark=employee["remark"] if employee["remark"] is not None else "",
                )
                for employee in result["result"]
                if employee["source"] == user_source and (search_term_lower in employee["name"].lower()
                                                          or search_term_lower in employee["alias"].lower())
            ]
        else:
            filtered_users = [
                User(
                    user_id=str(employee["id"]) if employee["id"] is not None else 0,
                    user_no=str(employee["id"]) if employee["id"] is not None else "",
                    name=employee["name"] if employee["name"] is not None else "",
                    full_name=employee["alias"] if employee["alias"] is not None else "",
                    department="",
                    # roles=employee["roles"] if employee["roles"] is not None else [],
                    remark=employee["remark"] if employee["remark"] is not None else "",
                )
                for employee in result["result"]
                if employee["source"] == user_source
            ]

        return filtered_users


async def get_user_list_with_search_term(search_term: Optional[str]) -> List[User]:
    channel = CONFIG.get('channel', 'platform')
    if channel == 'uss':
        return await get_user_list_from_uss(search_term)
    if channel == 'kcde':
        return await get_user_list_from_kcde(search_term)
    return await get_user_list_from_common(search_term)

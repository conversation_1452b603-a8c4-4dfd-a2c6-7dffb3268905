from datetime import time, datetime
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.share.orm import system_question_dislike_curd
from evaluation.share.orm.model import SystemQuestionDislike, SystemQuestionDislikeUser
from evaluation.share.util.user import get_user_name, get_user_full_name, get_user_id_v2


# 点踩
async def submit_dislike(db: AsyncSession, evaluation_id: str, question_id: int, content: Optional[str]):
    # 判断是否已点踩
    dislike = await system_question_dislike_curd.list_done_dislike(db, evaluation_id, question_id)
    if dislike is None:
        dislike_ = SystemQuestionDislike(
            evaluation_id=evaluation_id,
            question_id=question_id,
            dislike_count=1,
            create_time=datetime.now()
        )
        dislike_ = await system_question_dislike_curd.add_system_question_dislike(db, dislike_)

        # 保存明细
        user_id = get_user_id_v2()
        dislike_user = SystemQuestionDislikeUser(
            dislike_id=dislike_.id,
            evaluation_id=evaluation_id,
            content=content,
            question_id=question_id,
            user_id=user_id,
            user_name=get_user_name(),
            user_full_name=get_user_full_name(),
            create_time=datetime.now()
        )
        await system_question_dislike_curd.add_system_question_dislike_user(db, dislike_user)
        return

    # 查看当前用户是否已点踩
    dislike_user = await system_question_dislike_curd.list_done_dislike_user(db, evaluation_id, question_id,
                                                                             get_user_id_v2())
    if dislike_user:
        raise Exception(f"当前用户{get_user_name()}已点踩")

    # 保存用户点踩记录
    dislike_user = SystemQuestionDislikeUser(
        dislike_id=dislike.id,
        evaluation_id=evaluation_id,
        question_id=question_id,
        content=content,
        user_id=get_user_id_v2(),
        user_name=get_user_name(),
        user_full_name=get_user_full_name(),
        create_time=datetime.now()
    )
    await system_question_dislike_curd.add_system_question_dislike_user(db, dislike_user)

    # 更新点踩次数
    await  system_question_dislike_curd.update_dislike_count(db, dislike.id, dislike.dislike_count + 1)

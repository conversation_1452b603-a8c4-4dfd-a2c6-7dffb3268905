import json
import logging
import uuid
from typing import Op<PERSON>

from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.api.req.evaluation_model import UserTaskReq, UserTaskInfoReq
from evaluation.constants.status_enum import QuizPlanStatusEnum, QuizTypeEnum, QuizPlanDelEnum
from evaluation.share.orm import crud, evaluation_plan_crud, plan_task_crud, user_set_crud, question_crud, \
    system_quiz_criteria_crud, user_task_crud, user_quiz_tasks_criteria_info_crud
from evaluation.share.orm.model import (
    SystemQuizPlans,
    UserQuizTasks,
    UserQuizTasksInfo,
    SystemQuizPlanTask,
    SystemQuizPlanTaskInfo, SystemUserSets, model_to_dict, UserQuizTasksCriteriaInfo,
)
from evaluation.share.util.user import get_user_name, get_user_id_v2, get_user_full_name

logger = logging.getLogger("plaintext")


# 添加评测任务
async def generate_plan_task(db: AsyncSession, plan: SystemQuizPlans) -> SystemQuizPlanTask | None:
    quiz_task = SystemQuizPlanTask(
        evaluation_id=plan.plan_id,
        task_id=str(uuid.uuid4()),
        task_name=plan.plan_name,
        task_desc=plan.desc,
        evaluation_type=plan.quiz_type,
        task_state=QuizPlanStatusEnum.get_enum_code(QuizPlanStatusEnum.QUEUING),
        created_user=get_user_id_v2(),
        created_user_name=get_user_full_name(),
    )
    await plan_task_crud.del_eval_task(db, plan.plan_id)
    user_task = await plan_task_crud.generate_eval_task(db, quiz_task)
    return user_task


# 添加任务明细
async def generate_plan_task_info(db: AsyncSession, plan: SystemQuizPlans,
                                  plan_task: SystemQuizPlanTask):
    # 获取题集详情
    question_set = await crud.get_question_set_detail_by_id(
        db,
        plan.question_set_id
    )
    if question_set is None:
        logger.info(f"题集不存在{plan.question_set_id}")
        raise Exception("题集不存在")

    # 获取问题集关系
    question_relations = await crud.get_question_relations_detail_by_id(db, question_set.question_set_identifier)
    question_ids = [qs.question_id for qs in question_relations]

    # 获取问题详情
    questions = await crud.get_system_quiz_question_details(db, question_ids)
    if questions is None:
        logger.info(f"问题明细不存在{question_ids}")
        raise Exception("问题明细不存在")

    task_infos = []
    for qs in questions:
        quiz_task = SystemQuizPlanTaskInfo(
            evaluation_id=plan.plan_id,
            task_id=str(uuid.uuid4()),
            parent_task_id=plan_task.task_id,
            question_id=qs.id,
            question=qs.question,
            task_state=0,
            created_user=get_user_id_v2(),
            created_user_name=get_user_full_name(),
        )
        task_infos.append(quiz_task)

    await plan_task_crud.del_eval_task_detail(db, plan.plan_id)
    await plan_task_crud.batch_create_evaluation_task_info(db, task_infos)


# 当模型评测完成后生成用户任务
async def generate_user_task(db: AsyncSession, plan_id: str):
    # 查询任务
    system_eval_task = await plan_task_crud.get_eval_task(db, plan_id)
    if system_eval_task is None:
        raise Exception("任务不存在")

    # 状态判断
    # 0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束
    if system_eval_task.task_state != 4:
        raise Exception("仅在推理完成后发布用户任务")

    # 查询任务明细
    system_task_details = await plan_task_crud.get_eval_task_detail(db, system_eval_task.task_id)
    if system_task_details is None:
        raise Exception("任务明细不存在")

    # 查询执行计划
    plan = await evaluation_plan_crud.get_quiz_plan(db, plan_id)
    if plan is None:
        raise Exception("执行计划不存在")

    # 评测标准
    system_quiz_criteria_set = await plan_task_crud.get_system_quiz_criteria_sets_detail(db, plan.dim_set_id)
    if system_quiz_criteria_set is None:
        raise Exception("评测标准集不存在")

    # 评测标准明细
    system_criteria_list = await system_quiz_criteria_crud.get_criteria_list_by_criteria_set_identifier(db,
                                                                                                        plan.dim_set_id)
    if system_criteria_list is None:
        raise Exception("评测标准不存在")

    # 题集
    question_set = await question_crud.get_question_set_by_id(db, int(plan.question_set_id))
    if question_set is None:
        raise Exception("题集不存在")

    user_set_detail = await user_set_crud.get_user_set_by_id(db, int(plan.user_set_id))
    logger.info(f"用户集合详情,user_set_id:{plan.user_set_id},result:{json.dumps(model_to_dict(user_set_detail))}")
    if user_set_detail is None:
        raise Exception("用户集合不存在")

    # 用户id
    user_id_array = await user_set_crud.get_user_id_array(db, user_set_detail.user_set_identifier)
    logger.info(f"用户id:{user_id_array}")
    if user_id_array is None:
        raise Exception("用户集合不存在")

    # 删除历史用户任务(任务可能会重新发起)
    await delete_user_task_info(db, plan_id)

    # 添加用户任务
    for uid in user_id_array:
        # 生成用户任务
        user_task = UserQuizTasks(
            evaluation_id=plan.plan_id,
            task_id=str(uuid.uuid4()),
            task_name=plan.plan_name,
            task_desc=plan.desc,
            dim_set_identifier=system_quiz_criteria_set.criteria_set_identifier,
            question_set_identifier=question_set.question_set_identifier,
            evaluation_type=plan.quiz_type,
            task_state=1,
            user_id=uid,
            created_user=plan.create_user_id,
            created_user_name=plan.created_user,
        )
        user_task = await plan_task_crud.generate_user_evaluation_task(db, user_task)

        # 生成用户任务详情
        for qs_detail in system_task_details:
            user_task_info = UserQuizTasksInfo(
                evaluation_id=plan.plan_id,
                task_id=str(uuid.uuid4()),
                parent_task_id=user_task.task_id,
                question_id=qs_detail.question_id,
                question=qs_detail.question,
                system_answer=qs_detail.system_answer,
                task_state=1,
                user_id=uid,
                created_user=plan.create_user_id,
                created_user_name=plan.created_user,
            )
            await plan_task_crud.generate_user_evaluation_task(db, user_task_info)

            # 初始化用户评分
            for criteria in system_criteria_list:
                user_task_criteria_info = UserQuizTasksCriteriaInfo(
                    task_id=user_task_info.task_id,
                    user_id=user_task_info.user_id,
                    question_id=user_task_info.question_id,
                    title=criteria.title,
                    rating_key=criteria.rating_key,
                    rating_type=criteria.rating_type,
                    rating_int_value=-1,
                    rating_string_value="",
                    system_type="art",
                    feedback="",
                )
                await system_quiz_criteria_crud.generate_user_evaluation_task(db, user_task_criteria_info)


async def delete_user_task_info(db: AsyncSession, plan_id: str):
    if plan_id is None:
        return

    # 查询用户任务
    user_task_rq = UserTaskReq(evaluation_id=plan_id)
    user_task_list = await user_task_crud.list_user_task(db, user_task_rq)
    logger.info(f"user_task_list:{len(user_task_list)}")
    if len(user_task_list) == 0:
        return

    # 任务明细
    user_task_info_req = UserTaskInfoReq(evaluation_id=plan_id)
    user_task_info_list = await user_task_crud.list_user_task_info(db, user_task_info_req)
    logger.info(f"user_task_info_list:{len(user_task_info_list)}")

    user_task_info_ids = [t.task_id for t in user_task_info_list]
    logger.info(f"user_task_info_id size :{len(user_task_info_ids)}")

    # 删除任务
    await user_task_crud.delete_user_task_by_evaluation_id(db, plan_id)
    await user_task_crud.delete_user_task_info_by_evaluation_id(db, plan_id)

    # 删除评分标准
    if len(user_task_info_ids) > 0:
        await user_quiz_tasks_criteria_info_crud.delete_tasks_criteria_info_by_task_info_id(db, user_task_info_ids)

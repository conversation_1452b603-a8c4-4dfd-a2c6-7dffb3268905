import logging

from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.api.req.evaluation_model import UserTaskReq
from evaluation.services import user_service
from evaluation.share.orm import user_quiz_tasks_criteria_info_crud, question_crud, system_question_dislike_curd, \
    evaluation_plan_crud, user_task_crud
from evaluation.vo import criteria_report_vo

logger = logging.getLogger("plaintext")


# 评分汇总报告
async def summary_report(db: AsyncSession, plan_id: str) -> criteria_report_vo.SummaryReportModel:
    # 评分汇总报告
    user_quiz_tasks_criteria_infos = await user_quiz_tasks_criteria_info_crud.list_user_quiz_tasks_criteria_info_by_plan_id(
        db, plan_id)
    logger.info(f"user_quiz_tasks_criteria_infos:{user_quiz_tasks_criteria_infos}")

    if user_quiz_tasks_criteria_infos is None:
        return criteria_report_vo.SummaryReportModel()

    # 查看用户任务
    user_task_list = await user_task_crud.list_user_task(db, UserTaskReq(evaluation_id=plan_id))
    logger.info(f"user_task_list:{len(user_task_list)}")

    # 查看计划详情
    plan = await evaluation_plan_crud.get_quiz_plan(db, plan_id)
    logger.info(f"plan:{plan}")

    # 查看问题详情
    questions = await question_crud.list_question_details(db, int(plan.question_set_id))
    logger.info(f"questions:{len(questions)}")

    accuracy_total = 0.0
    professionalism_total = 0.0
    illusion_total = 0.0
    factor = len(user_task_list) * len(questions)

    for s in user_quiz_tasks_criteria_infos:
        if s.rating_int_value == -1:
            continue
        if s.rating_key == "a":
            accuracy_total += s.rating_int_value
        elif s.rating_key == "c":
            professionalism_total += s.rating_int_value
        elif s.rating_key == "b":
            illusion_total += s.rating_int_value

    # 平均值
    avg_accuracy = round(accuracy_total / factor, 2) if factor else 0.0
    avg_professionalism = round(professionalism_total / factor, 2) if factor else 0.0
    avg_illusion = round(illusion_total / factor, 2) if factor else 0.0

    return criteria_report_vo.SummaryReportModel(accuracy=avg_accuracy, professionalism=avg_professionalism,
                                                 illusion=avg_illusion)


async def report_group_by_question(db: AsyncSession, plan_id: str, page: int, page_size: int) -> (int, list[
    criteria_report_vo.SummaryReportModel]):
    try:

        total = await user_quiz_tasks_criteria_info_crud.count_group_by_question(db, plan_id)
        if total is None:
            return 0, []

        # 查看用户任务
        user_task_list = await user_task_crud.list_user_task(db, UserTaskReq(evaluation_id=plan_id))
        logger.info(f"user_task_list:{len(user_task_list)}")
        factor = len(user_task_list)

        summaryReportGroupByQuestionModels = await user_quiz_tasks_criteria_info_crud.report_group_by_question(db,
                                                                                                               plan_id,
                                                                                                               page,
                                                                                                               page_size)

        # 填充问题名称
        question_ids = [t.question_id for t in summaryReportGroupByQuestionModels]
        questions = await question_crud.get_question_set_by_ids(db, question_ids)
        questions_dict = {q.id: q.question for q in questions}

        for t in summaryReportGroupByQuestionModels:
            t.question = questions_dict[t.question_id]
            t.accuracy = round(t.accuracy / factor, 2) if factor else 0.0
            t.professionalism = round(t.professionalism / factor, 2) if factor else 0.0
            t.illusion = round(t.illusion / factor, 2) if factor else 0.0

        return total, summaryReportGroupByQuestionModels
    except Exception as e:
        logger.info(f"report_group_by_question occur error:{str(e)}")
        raise e


async def report_group_by_user(db, plan_id, page, page_size):
    try:

        total = await user_quiz_tasks_criteria_info_crud.count_group_by_user(db, plan_id)
        if total is None:
            return 0, []

            # 查看计划详情
        plan = await evaluation_plan_crud.get_quiz_plan(db, plan_id)
        logger.info(f"plan:{plan}")

        # 查看问题详情
        questions = await question_crud.list_question_details(db, int(plan.question_set_id))
        logger.info(f"questions:{len(questions)}")
        factor = len(questions)


        summaryReportGroupByQuestionModels = await (user_quiz_tasks_criteria_info_crud
                                                    .report_group_by_user(db, plan_id, page, page_size))

        # 获取用户信息
        user_ids = [t.user_id for t in summaryReportGroupByQuestionModels]
        user_infos = await user_service.get_users_by_user_ids(user_ids)
        user_info_name_set = {u.user_id: u.full_name for u in user_infos}
        user_info_name_en_set = {u.user_id: u.name for u in user_infos}

        # 填充用户名
        # 用户系统common和kcde返回的user_id不一样
        for t in summaryReportGroupByQuestionModels:
            t.user_name = user_info_name_set.get(t.user_id, "")
            t.user_name_en = user_info_name_en_set.get(t.user_id, "")
            t.accuracy = round(t.accuracy / factor, 2) if factor else 0.0
            t.professionalism = round(t.professionalism / factor, 2) if factor else 0.0
            t.illusion = round(t.illusion / factor, 2) if factor else 0.0

        return total, summaryReportGroupByQuestionModels
    except Exception as e:
        logger.info(f"report_group_by_user occur error:{str(e)}")
        raise e


async def list_dislike(db, plan_id, page, page_size):
    # 点踩列表
    total = await  system_question_dislike_curd.count_dislike(db, plan_id)
    if total is None:
        return 0, []

    dislikes = await system_question_dislike_curd.list_dislike_by_page(db, plan_id, page, page_size)

    # 填充用户
    for d in dislikes:
        users = await system_question_dislike_curd.list_dislike_users(db, d.dislike_id, d.question_id)
        user_full_names = [u.user_full_name for u in users]
        d.user_names = user_full_names

    return total, dislikes

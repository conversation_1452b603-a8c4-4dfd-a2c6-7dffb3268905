import json
import logging
from datetime import datetime
from typing import List

import httpx
from sqlalchemy import inspect

from evaluation.api.req.evaluation_model import EstimationReq
from evaluation.share.util import user
from evaluation.share.util.app_config import CONFIG
from evaluation.vo.model_vo import ModelVo, ListModelVo

logger = logging.getLogger("plaintext")


def get_header():
    header = {
        "Content-Type": "application/json"
    }
    if token := user.jwt_token.get():
        header["Authorization"] = f"Bearer {token}"
    return header


def model_to_dict(inspectable):
    """使用SQLAlchemy的inspect方法将模型转换为字典，并格式化日期时间字段"""
    data = {}
    for c in inspect(inspectable).mapper.column_attrs:
        value = getattr(inspectable, c.key)
        if isinstance(value, datetime):
            data[c.key] = value.strftime("%Y-%m-%d %H:%M:%S")
        else:
            data[c.key] = value
    return data


# 模型列表
async def fetch_models(page_index: int, page_size: int):
    logger.info("Fetching models...")
    url = f"{CONFIG.get('training', 'platform')}/api/v1/user/modelinfo/list?page_index={page_index}&page_size={page_size}"

    headers = get_header()
    # headers["host"] = CONFIG.get('training', 'host')

    async with httpx.AsyncClient() as client:
        raw_response = await client.get(url, headers=headers)
        raw_response.raise_for_status()
    logger.debug(f"Response: {raw_response.text}")
    if raw_response.status_code != 200:
        raise Exception(
            f"Failed to fetch_models, status_code: {raw_response.status_code}, text: {raw_response.text}")
    response = raw_response.json()
    if response["code"] != 200:
        raise Exception(
            f"Failed to fetch_models, code: {response['code']}, message: {response['message']}")

    model_data_dict = response.get("data", {})

    model_data = []
    for d in model_data_dict["data"]:
        if d["model_type"] > 2:
            continue

        vo = ModelVo(
            id=d["id"],
            model_name=d["model_name"],
            model_type=d["model_type"],
            checkpoint_id=d["checkpoint_id"],
            checkpoint_name=d["checkpoint_name"],
            model_parameters=json.dumps(d)
        )
        model_data.append(vo)
    return ListModelVo(data=model_data)


# 资源组
async def get_inference_resource_pool_groups():
    url = f"{CONFIG.get('inference', 'platform')}/api/v1/inference/service/resource-pool-group/list"
    headers = get_header()
    # headers["host"] = CONFIG.get('inference', 'host')

    # INFERENCE
    resource_category = CONFIG.get('inference', 'resource_category')
    params = {
        "resource_category": resource_category
    }

    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers, params=params)

    if response.status_code != 200:
        logger.error(
            f"Failed to get inference resource pool list, status_code: {response.status_code}, text: {response.text}")
        return []
    response_json = response.json()
    if response_json["code"] != 200:
        logger.error(
            f"Failed to get inference resource pool list, code: {response_json['code']}, message: {response_json['message']}")
        return []

    data = response_json.get("data", []) or []
    return data

async def get_inference_service_free_resource(resource_pool: str, resource_group: str):
    url = f"{CONFIG.get('inference', 'platform')}/api/v1/inference/service/free/resource"
    params = {
        "resource_pool": resource_pool,
        "resource_group": resource_group
    }

    header = get_header()
    # header["host"] = CONFIG.get('inference', 'host')

    async with httpx.AsyncClient() as client:
        response = await client.get(url, params=params, headers=header)

    if response.status_code != 200:
        logger.error(
            f"Failed to get free resource, status_code: {response.status_code}, text: {response.text}")
        return {}
    response_json = response.json()
    if response_json["code"] != 200:
        logger.error(
            f"Failed to get free resource, code: {response_json['code']}, message: {response_json['message']}")
        return {}

    return response_json.get("data", {})


async def estimate(request: EstimationReq) -> dict:
    if not request:
        return {"replicas": 0, "estimations": []}
    model_data = {}
    model_data["model_id"] = request.model_id
    model_data["gpu_type"] = request.gpu_type
    model_data["model_parameters"] = request.model_parameters
    model_data["context_length"] = request.context_length
    model_data["seq_len"] = request.seq_len
    model_data["type"] = request.type
    if request.resource_pool is not None:
        model_data["resource_pool"] = request.resource_pool
    if request.resource_group is not None:
        model_data["resource_group"] = request.resource_group
    replicas, model_gpu_usages = await estimate_replicas(model_data)
    for gpus_per_replica in model_gpu_usages:
        gpus_per_replica["replicas"] = replicas
    return {"replicas": replicas, "estimations": model_gpu_usages}


async def estimate_replicas(model):
    replicas = {}
    model_gpu_usages = []
    response_free_resource = await get_inference_service_free_resource(
        model.get("resource_pool", "evaluation"),
        model.get("resource_group", "default"))
    total_gpus = {response_free_resource.get("gpu_type"): response_free_resource.get("total_gpu")}
    gpu_type = model.get("gpu_type")
    if gpu_type not in total_gpus:
        logger.error(f'No gpu type {gpu_type} is available for model {model["model_id"]}')
        gpus_per_replica = 0
    else:
        gpus_per_replica = get_gpu_usage(model)
    model_gpu_usages.append({
        "model_id": model.get("model_id"),
        "gpus_per_replica": gpus_per_replica
    })
    if gpu_type not in replicas:
        replicas[gpu_type] = 0
    replicas[gpu_type] += gpus_per_replica
    for gpu_type, gpu_usage in replicas.items():
        replicas[gpu_type] = total_gpus[gpu_type] // gpu_usage if gpu_usage > 0 else 0
    return min(replicas.values()), model_gpu_usages


def get_gpu_usage(model) -> int:
    default_gpu_usage = CONFIG.getint("inference", "default_gpus_per_replica")

    if model.get("model_parameters") is None:
        return default_gpu_usage
    parameter_count = model.get("model_parameters")
    if type(parameter_count) is not str or len(parameter_count) == 0 or str(parameter_count[-1]).upper() != "B":
        return default_gpu_usage
    try:
        parameter_count = int(float(parameter_count[:-1]))
    except ValueError as e:
        logger.exception(f"Failed to parse parameter count: {parameter_count}")
        return default_gpu_usage

    context_length = None
    if model.get("context_length") is not None:
        context_length = model.get("context_length")
    elif model.get("seq_len") is not None:
        context_length = model.get("seq_len")

    if type(context_length) is not int or context_length <= 0:
        return default_gpu_usage

    if model.get("type") == "base":
        if parameter_count < 10:
            return 2 if context_length <= 8192 else 4 if context_length <= 16384 else 8
        elif parameter_count < 20:
            return 4 if context_length <= 8192 else 8
        else:
            return 8
    elif model.get("type") == "chat":
        if parameter_count < 10:
            return 1 if context_length <= 8192 else 2 if context_length <= 16384 else 4
        elif parameter_count < 20:
            return 2 if context_length <= 8192 else 4
        elif parameter_count < 40:
            return 4 if context_length <= 32768 else 8
        else:
            return 8
    else:
        return default_gpu_usage

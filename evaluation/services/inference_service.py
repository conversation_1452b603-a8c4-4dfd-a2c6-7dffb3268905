# 模型推理
import json
import logging
from datetime import datetime
from typing import Op<PERSON>

from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.api.req.evaluation_model import QueryPlanReq
from evaluation.api.req.evaluation_system_model import QuestionSetDetailReq
from evaluation.constants.constants import inference_service_status
from evaluation.services import evaluation_plan_service, plan_task_service, user_service, system_evaluation_service
from evaluation.share.infrence import kserve
from evaluation.share.orm import evaluation_plan_crud, inference_service_crud, plan_task_crud, question_crud, database, \
    crud, user_set_crud, system_quiz_plan_snapshot_crud
from evaluation.share.orm.model import SystemQuizPlanSnapshot
from evaluation.share.util import json_utils
from evaluation.share.util.app_config import CONFIG
from evaluation.share.util.json_utils import JsonUtils

logger = logging.getLogger("plaintext")


# 更新推理服务状态
# 推理服务状态1-更新中2-运行中3-下线中4-已下线5-异常
async def update_inference_task():
    logger.info("update_inference_task begin.....")
    async for db_session in database.get_db():
        # 更新中的推理服务
        updating_tasks = await inference_service_crud.list_inference_services(db_session, None, None, None, [1])
        logger.info(f"list_inference_services updating_tasks:{len(updating_tasks)}")
        if len(updating_tasks) == 0:
            return
        for t in updating_tasks:
            inference_rpc_resp = await evaluation_plan_service.fetch_services([t.inference_service_id])
            logger.info(f"fetch_services.service_id:{t.inference_service_id},result:{inference_rpc_resp}")
            if len(inference_rpc_resp) == 0:
                continue

            for i in inference_rpc_resp:
                if i["id"] != t.inference_service_id:
                    continue

                state = inference_service_status.get(i["status"], None)
                if state is None:
                    continue

                if state == 1:
                    logger.info(f"update_inference_task updating service:{t.inference_service_id}")
                    continue

                try:
                    # 更新推理服务信息
                    api_format = i["api_format"]
                    api_address = i["api_address"]
                    if "openai" in api_format:
                        openai_api_path = i["openai_api_path"]
                        if openai_api_path:
                            api_address = openai_api_path
                    await inference_service_crud.update_inference_service_url(
                        db_session, t.plan_id, state, i["internal_url"], api_address
                    )

                    if state == 2:
                        logger.info(f"update_inference_task running service:{t.inference_service_id}")
                        # 开始推理
                        await begin_inference_task(t.plan_id)
                        continue

                    if state == 3:
                        logger.info(f"update_inference_task offline doing service:{t.inference_service_id}")
                        continue

                    if state == 4:
                        logger.info(f"update_inference_task offline done service:{t.inference_service_id}")
                        continue

                    if state == 5:
                        logger.info(f"update_inference_task error service:{t.inference_service_id}")

                        # 部署异常
                        await update_eval_status(db_session, t.plan_id, 3, "部署异常")

                        # 释放推理资源
                        await evaluation_plan_service.delete_service(t.inference_service_id)
                        logger.info(
                            f"delete_service plan:{t.plan_id},name:{t.plan_name},service_id:{t.inference_service_id}")
                except Exception as e:
                    logger.error(f"update_inference_task occur error:{str(e)}")


# 开始推理
# 推理服务状态1-更新中2-运行中3-下线中4-已下线5-异常
async def begin_inference_task(plan_id: Optional[str]):
    logger.info("begin_inference_task begin.....")
    async for db_session in database.get_db():
        # 查询已经部署好的推理服务
        inference_services = await inference_service_crud.list_inference_services(db_session, plan_id, None, None, [2])
        if len(inference_services) == 0:
            return
        running = inference_services[0]

        try:
            # 任务--》推理中
            await update_eval_status(db_session, running.plan_id, 2, "推理中")

            # 任务详情
            task_info_list = await plan_task_crud.get_eval_task_detail_by_evaluation_id(db_session,
                                                                                        running.plan_id)
            logger.info(f"get_eval_task_detail_by_evaluation_id task_info_list:{task_info_list}")

            # 问题
            question_ids = [t.question_id for t in task_info_list]
            questions = await question_crud.get_question_set_by_ids(db_session, question_ids)
            questions_dict = {q.id: q for q in questions}

            header = {CONFIG['inference_eval']['header']: running.internal_host}

            for task_info in task_info_list:
                question_info = questions_dict.get(task_info.question_id)
                if question_info.question_type == "multi_round_qa":
                    # 多轮对话
                    qa_round = pase_qa_round_question(question_info.details)
                    response = await kserve.KServeClient(path=running.external_path,
                                                         header=header,
                                                         data=qa_round,
                                                         model_name=running.model_name,
                                                         model_type=running.model_type).generate()
                    logger.info(f"multi_round_qa response:{response}")

                    dicts = [obj.to_dict() for obj in response]
                    json_string = json.dumps(dicts, ensure_ascii=False)
                    logger.info(f"multi_round_qa sys_answer:{json_string}")

                    # 更新系统答案
                    sys_answer = json_string
                    await plan_task_crud.update_task_detail_sys_answer(db_session, task_info.task_id,
                                                                       sys_answer,
                                                                       None)
                else:
                    # 单选，多选，填空，判断
                    response = await kserve.KServeClient(path=running.external_path,
                                                         header=header,
                                                         data=[question_info.question],
                                                         model_name=running.model_name,
                                                         model_type=running.model_type).generate()
                    logger.info(f"single response:{response}")
                    system_answer_ = response[0].answer
                    track_id = response[0].track_id

                    # 更新系统答案
                    await plan_task_crud.update_task_detail_sys_answer(db_session, task_info.task_id,
                                                                       system_answer_,
                                                                       track_id)

            # 任务--》推理完成
            await update_eval_status(db_session, running.plan_id, 4, "推理完成")

            # 发布用户任务
            await plan_task_service.generate_user_task(db_session, running.plan_id)
            logger.info(f"generate_user_task done plan:{running.plan_id},name:{running.plan_name}")

            # 任务--》待评测
            await update_eval_status(db_session, running.plan_id, 4, "待评测")
        except Exception as e:
            logger.error(f"begin_inference_task occur error:{str(e)}")
            # 任务--》推理失败
            await update_eval_status(db_session, running.plan_id, 3, f"推理失败:{str(e)}")

        finally:
            # 释放推理资源
            await evaluation_plan_service.delete_service(running.inference_service_id)
            logger.info(
                f"delete_service plan:{running.plan_id},name:{running.plan_name},service_id:{running.inference_service_id}")

            # 更新推理服务状态（已下线）
            await inference_service_crud.update_inference_service_state(db_session, running.plan_id, 4)
            logger.info(f"update_inference_service_state offline plan:{running.plan_id},name:{running.plan_name}")


# 更新任务状态
# 0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束


async def update_eval_status(db: AsyncSession, plan_id: str, status: int, message: str):
    # 评测计划
    await evaluation_plan_crud.update_quiz_plan_status(db, plan_id, status, message)
    logger.info(f"update_quiz_plan_status:{plan_id},status:{status},message:{message}")

    # 任务
    await plan_task_crud.update_plan_task_state(db, plan_id, status, message)
    logger.info(f"update_plan_task_state:{plan_id},status:{status},message:{message}")


# 生成任务快照
async def generate_snapshot(db, plan_id):
    # 任务详情
    plan_info = await evaluation_plan_crud.get_quiz_plan(db, plan_id)
    logger.info(f"get_quiz_plan plan_info.plan_id:{plan_id},info:{json_utils.JsonUtils.to_json(plan_info)}")
    if plan_info is None:
        return

    if plan_info.state != 7:
        logger.info(f"generate_snapshot plan_id:{plan_id},state:{plan_info.state}")
        return

    # 判断快照是否已经生成
    systemQuizPlanSnapshots = await system_quiz_plan_snapshot_crud.get_system_quiz_plan_snapshot(db, plan_id,
                                                                                                 None,
                                                                                                 None)
    logger.info(
        f"get_system_quiz_plan_snapshot_by_id systemQuizPlanSnapshots:{len(systemQuizPlanSnapshots)},plan_id:{plan_id}")
    if len(systemQuizPlanSnapshots) > 0:
        # 已经生成
        return

    # 题集
    question_set = await question_crud.get_question_set_by_id(db, int(plan_info.question_set_id))
    question_detail = await system_evaluation_service.get_question_set_detail(db, QuestionSetDetailReq(
        question_set_identifier=question_set.question_set_identifier, fetch_total=True, page=1, page_size=10000))

    # 用户集合
    user_set_detail = await user_set_crud.get_user_set_by_id(db, int(plan_info.user_set_id))
    user_set_data = await user_service.get_evaluation_group_detail(db, user_set_detail.user_set_identifier)
    members = user_set_data.get("members", [])

    # 生成任务快照
    snapshot = SystemQuizPlanSnapshot(
        evaluation_id=plan_info.plan_id,
        question_set_identifier=question_set.question_set_identifier,
        question_set_config=JsonUtils.to_json(question_detail),
        user_set_identifier=user_set_detail.user_set_identifier,
        user_set_config=JsonUtils.to_json(members),
        create_time=datetime.now(),
    )

    await system_quiz_plan_snapshot_crud.add_system_quiz_plan_snapshot(db, snapshot)


# 获取任务快照
async def get_snapshot(db: AsyncSession, plan_id: Optional[str], question_set_identifier: Optional[str],
                       user_set_identifier: Optional[str]):
    systemQuizPlanSnapshots = await system_quiz_plan_snapshot_crud.get_system_quiz_plan_snapshot(db, plan_id,
                                                                                                 question_set_identifier,
                                                                                                 user_set_identifier)
    if len(systemQuizPlanSnapshots) == 0:
        return []

    result = []
    for e in systemQuizPlanSnapshots:
        var = {
            "evaluation_id": e.evaluation_id,
            "question_set_identifier": e.question_set_identifier,
            "question_set_config": JsonUtils.pase_json(e.question_set_config),
            "user_set_config": JsonUtils.pase_json(e.user_set_config),
            "user_set_identifier": e.user_set_identifier,
        }
        result.append(var)

    return result


# 获取用户集合
async def get_members_from_snapshot(db: AsyncSession, plan_id: str, user_set_identifier: str) -> list:
    systemQuizPlanSnapshots = await get_snapshot(db, plan_id, None, user_set_identifier)
    if len(systemQuizPlanSnapshots) == 0:
        return []

    members = systemQuizPlanSnapshots[0]["user_set_config"]
    if members is None:
        return []

    return members


# 获取问题集合
async def get_question_set_from_snapshot(db: AsyncSession, plan_id: str, question_set_identifier: str) -> dict:
    systemQuizPlanSnapshots = await get_snapshot(db, plan_id, question_set_identifier, None)
    if len(systemQuizPlanSnapshots) == 0:
        return {}

    question_set_details = systemQuizPlanSnapshots[0]["question_set_config"]
    if question_set_details is None:
        return {}

    return question_set_details


# 题集名称快照
async def get_question_set_name_snapshot(db: AsyncSession, plan_ids: list[str]) -> dict:
    snapshots = await system_quiz_plan_snapshot_crud.get_system_quiz_plan_snapshot_by_plan_ids(db, plan_ids)
    logger.info(
        f"get_question_set_name_snapshot snapshots:{len(snapshots)},plan_ids:{json_utils.JsonUtils.to_json(plan_ids)}")
    if not snapshots:
        return {}

    question_name_dict = {
        f"{e.evaluation_id}-{e.question_set_identifier}": JsonUtils.pase_json(e.question_set_config).get("name", None)
        for e in snapshots
    }

    logger.info(
        f"get_question_set_name_snapshot question_name_dict: {JsonUtils.to_json(question_name_dict)}")
    return question_name_dict


# 解析多轮对话问题
# {"qa_round": [{"question": "q1", "reference_answer": "a1"}, {"question": "q2", "reference_answer": "a2"}]}
def pase_qa_round_question(qa_round_question: str) -> [str]:
    data = []
    questions = json.loads(JsonUtils.to_json(qa_round_question))
    for q in questions["qa_round"]:
        data.append(q["question"])
    return data

import json
from typing import List, Any, Optional

from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.api.evaluate_schema import EvaluationTaskType
from evaluation.api.req.base_model import BaseModelPageListReq
from evaluation.api.req.evaluation_model import (
    CreateEvaluationTaskReq,
    QuizQuestion,
    SyStemCriteriaInfo, QueryPlanReq,
)
from evaluation.api.req.evaluation_system_model import (
    QuestionSetDeleteReq,
    SystemQuestionData,
    SystemQuestionCreateReq,
    SystemQuestionTypeData,
    SystemQuestionDeleteReq,
    SystemQuestionUpdateReq,
    QuestionSetCreateReq,
    QuestionSetUpdateReq,
    QuestionSetDataRelationReq,
    QuestionSetData,
    QuestionSetDetailReq,
)
from evaluation.constants.constants import QUESTION_SET_DISCARD_TIP, QUESTION_DISCARD_TIP
from evaluation.share.orm import crud, system_quiz_criteria_crud, evaluation_plan_crud, question_crud
from evaluation.share.orm.model import SystemQuizQuestionSetRelationsV2, SystemQuizQuestions_V2
from evaluation.share.orm.platform_schema import <PERSON>riteria
from evaluation.share.util import user, json_utils
from evaluation.share.util.permission import Permission, should_match
from evaluation.share.util.plaintext_log import logger
from evaluation.share.util.user import get_user_id_v2, get_user_name


# 根据task_id和question_id获取评测任务明细
async def get_evaluation_task_info_by_task_id_and_question_id(
        db: AsyncSession, task_id: str, question_id: int
):
    task_info = await crud.get_evaluation_task_info_by_task_id_and_question_id(
        db, task_id, question_id
    )
    return task_info


# 创建评测任务
def create_evaluation_task(db: AsyncSession, val: CreateEvaluationTaskReq):
    evaluation_task = crud.create_evaluation_task(db, val)
    return evaluation_task


# 获取系统评测任务
async def get_system_task_by_evaluation_id(db: AsyncSession, evaluation_id: str):
    system_task = await crud.get_system_task_by_evaluation_id(db, evaluation_id)
    return system_task


# 根据评测id获取评测标准列表
async def get_criteria_list_by_evaluation_id(db: AsyncSession, evaluation_id: str):
    criteria_list, criteria_set_identifier = (
        await crud.get_criteria_list_by_evaluation_id(db, evaluation_id)
    )
    return criteria_list


# 根据评测标准自增id获取评测标准列表
async def get_criteria_list_by_id(db: AsyncSession, id: int):
    criteria_list = await crud.get_criteria_list_by_id(db, id)
    return criteria_list


async def get_criteria_set_identifier_by_id(db: AsyncSession, criteria_set_id: int) -> Optional[str]:
    try:
        criteria = await system_quiz_criteria_crud.get_criteria_sets_by_id(db, criteria_set_id)
        return criteria.criteria_set_identifier if criteria else None
    except Exception as e:
        logger.error(f"Error fetching criteria set identifier for ID {id}: {str(e)}")
        return None


def convert_to_pydantic(model_instance, pydantic_model):
    model_dict = {column.name: getattr(model_instance, column.name) for column in model_instance.__table__.columns}
    return pydantic_model(**model_dict)


# 获取评测标准列表
async def get_criteria_list_by_criteria_set_identifier(
        db: AsyncSession, criteria_set_identifier: str
):
    criteria_list = await crud.get_criteria_list_by_criteria_set_identifier(
        db, criteria_set_identifier
    )

    pydantic_criteria_list = [convert_to_pydantic(criteria, Criteria) for criteria in criteria_list]
    return pydantic_criteria_list


# 获取评测标准
async def get_evaluation_criteria_by_evaluation_id(
        db: AsyncSession, evaluation_id: str
):
    criteria_list, criteria_set_identifier = (
        await crud.get_criteria_list_by_evaluation_id(db, evaluation_id)
    )
    data = [
        SyStemCriteriaInfo(
            title=criteria.title,
            rating_type=criteria.rating_type,
            rating_key=criteria.rating_key,
            value=criteria.value,
        )
        for criteria in criteria_list or []
    ]
    return data, criteria_set_identifier


# 获取用户评测题
async def get_quiz_questions_by_evaluation_id_and_user_id(
        db: AsyncSession, user_id: str
):
    data = await crud.get_quiz_questions_by_evaluation_id_and_user_id(db, user_id)
    logger.info(f"get_quiz_questions_by_evaluation_id_and_user_id: {data}")
    return data


async def get_quiz_questions_by_evaluation_id_and_user_id_v2(
        db: AsyncSession, user_id: str
):
    data = await crud.get_quiz_questions_by_evaluation_id_and_user_id_v2(db, user_id)
    logger.info(f"get_quiz_questions_by_evaluation_id_and_user_id: {data}")
    return data


async def create_quiz_question(db: AsyncSession, qq: QuizQuestion):
    await crud.create_quiz_question(db, qq)
    return


# 获取召回文档
async def get_recall_documents(db: AsyncSession, task_id: str) -> List[Any]:
    try:
        task_info = await crud.get_evaluation_task_info_by_task_id(db, task_id)
        return json.loads(task_info.source) if task_info and task_info.source else []
    except json.JSONDecodeError:
        return []


# 获取系统所有题目
async def get_system_quiz_questions(db: AsyncSession, req: BaseModelPageListReq):
    # 权限校验
    user_permissions = user.get_user_permissions()
    has_permission = should_match([Permission.EVAL_MGMT.value, Permission.VIEW_EVAL.value], user_permissions)
    if not has_permission:
        logger.info(f"no permission, user:{user.get_user_name()}, user_permissions:{user_permissions}.")
        return 0, []

    count, questions = await crud.get_system_quiz_questions(
        db, req.page, req.page_size, req.question_type, req.order_str
    )
    data = []
    if count == 0:
        return count, data

    for question in questions:
        if isinstance(question.details, dict):
            parsed_details = question.details
        else:
            details = question.details if question.details is not None else "{}"
            parsed_details = json.loads(details)
        created_str = question.created_at.strftime("%Y-%m-%d %H:%M:%S")
        updated_str = question.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        data.append(
            SystemQuestionData(
                id=question.id,
                question_id=question.question_id,
                question=question.question,
                question_type=question.question_type,
                question_desc=question.question_desc,
                version=question.version,
                created_at=created_str,
                updated_at=updated_str,
                state=question.state,
                details=parsed_details,
            )
        )
    return count, data


# 获取系统题型列表
async def get_system_quiz_question_types(db: AsyncSession):
    question_types = await crud.get_system_quiz_question_types(db)
    data = []
    for question_type in question_types:
        data.append(
            SystemQuestionTypeData(
                id=question_type.id,
                question_type=question_type.question_type,
                question_type_desc=question_type.question_type_desc,
            )
        )
    return data


# 获取系统评测任务列表
async def get_evaluation_task_list(db: AsyncSession, req: BaseModelPageListReq):
    return await crud.get_evaluation_task_list(
        db, req.page, req.page_size, req.fetch_total
    )


# 创建系统问题到题库
async def add_system_question(db: AsyncSession, val: SystemQuestionCreateReq):
    if val.question_type == "qa":
        category = "subjective"
    else:
        category = "objective"
    return await crud.add_system_question(
        db,
        val.question,
        val.question_desc,
        val.question_type_id,
        val.question_type,
        val.details,
        category,
        val.user_id,
    )


# 修改系统问题到题库
async def update_system_question(db: AsyncSession, val: SystemQuestionUpdateReq):
    # 验证题库是否存在
    select_quesion_exec = await db.execute(select(SystemQuizQuestions_V2).filter_by(question_id=val.question_id))
    select_quesion = select_quesion_exec.scalars().first()
    if select_quesion is None:
        raise Exception("题目不存在")

    # 校验问题是否在使用
    question_plans = await question_crud.list_question_plan_relation(db, val.question_id)
    uncompleted_plans = [plan for plan in question_plans if plan.plan_status in [0, 1, 2, 4, 5, 6]]
    if uncompleted_plans and len(uncompleted_plans) > 0:
        raise Exception(f"该问题正在被\"{uncompleted_plans[0].plan_name}\"使用中，不能修改")

    if val.question_type == "qa":
        category = "subjective"
    elif val.question_type.lower() == EvaluationTaskType.MULTI_ROUND_QA.value:
        category = "subjective"
        val.question = get_first_round_question(val.details).strip()
        if not val.question:
            raise Exception("问题不存在")
    else:
        category = "objective"

    isSuccess = await crud.update_system_question(
        db,
        val.question_id,
        val.question,
        val.question_desc,
        val.question_type_id,
        val.question_type,
        val.details,
        category,
        get_user_name(),
    )
    if not isSuccess:
        raise Exception("更新失败")


# 删除系统题库问题
async def delete_system_question(db: AsyncSession, val: SystemQuestionDeleteReq):
    user_permissions = user.get_user_permissions()
    has_permission = should_match([Permission.EVAL_MGMT.value], user_permissions)
    if not has_permission:
        raise Exception("没有权限")

    # 验证题库是否存在
    select_question_exec = await db.execute(select(SystemQuizQuestions_V2).filter_by(question_id=val.question_id))
    select_question = select_question_exec.scalars().first()
    if select_question is None:
        raise Exception("题目不存在")

    # 校验问题是否在使用
    question_plans = await question_crud.list_question_plan_relation(db, val.question_id)
    uncompleted_plans = [plan for plan in question_plans if plan.plan_status in [0, 1, 2, 4, 5, 6]]
    if uncompleted_plans and len(uncompleted_plans) > 0:
        raise Exception(f"该问题正在被\"{uncompleted_plans[0].plan_name}\"使用中，不能删除")

    return await crud.delete_system_question(db, val.question_id, val.question_version)


# 创建题集
async def create_question_set(db: AsyncSession, val: QuestionSetCreateReq):
    if not await crud.check_question_ids(db, val.question_ids):
        raise Exception("题集中的问题不存在")

    # 题集名称不能重复
    question_set = await question_crud.get_question_set_by_name(db, val.name)
    if question_set:
        raise Exception(f"题集名称:{val.name}已存在")

    return await crud.create_question_set(
        db, val.name, val.desc, val.question_ids, get_user_name()
    )


# 校验题目id列表中的问题是否存在
async def check_question_ids(db: AsyncSession, question_ids: list[str]):
    return await crud.check_question_ids(db, question_ids)


# 修改题集
async def update_question_set(db: AsyncSession, val: QuestionSetUpdateReq):
    if await crud.check_question_set_exists(db, val.question_set_identifier):
        raise Exception("题集不存在")
    if not await crud.check_question_ids(db, val.question_ids):
        raise Exception("题集中的问题不存在")

    # 检测题集是否在使用
    current_question_set = await question_crud.get_question_set_by_question_set_identifier(db,
                                                                                           val.question_set_identifier)
    plans, total = await evaluation_plan_crud.get_quiz_plan_list(db,
                                                                 QueryPlanReq(question_set_id=current_question_set.id))
    if total > 0:
        for plan in plans:
            # 0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束
            if plan.state in [0, 1, 2, 4, 5, 6]:
                raise Exception(f"\"{plan.plan_name}\"任务进行中，不能修改该题集")

    # 题集名称不能重复
    question_set = await question_crud.get_question_set_by_name(db, val.name)
    if question_set and question_set.question_set_identifier != val.question_set_identifier:
        raise Exception(f"题集名称:{val.name}已存在")

    return await crud.update_question_set(
        db,
        val.question_set_identifier,
        val.name,
        val.desc,
        val.question_ids,
        get_user_name(),
    )
from evaluation.share.util.user_info_decorator import inject_user_info
@inject_user_info({"created_user_id": "created_user_name"})
async def get_question_sets(db: AsyncSession, req: BaseModelPageListReq):
    # 权限校验
    # user_permissions = user.get_user_permissions()
    # has_permission = should_match([Permission.EVAL_MGMT.value, Permission.VIEW_EVAL.value], user_permissions)
    # if not has_permission:
    #     logger.info(f"no permission, user:{user.get_user_name()}, user_permissions:{user_permissions}.")
    #     return 0, []

    total, sets = await crud.get_question_sets(
        db, req.page, req.page_size, req.fetch_total, req.search_term, req.order_str
    )
    multi_round_check_query = select(SystemQuizQuestionSetRelationsV2.question_set_identifier) \
        .join(SystemQuizQuestions_V2,
              SystemQuizQuestionSetRelationsV2.question_id == SystemQuizQuestions_V2.question_id) \
        .where(SystemQuizQuestions_V2.question_type == "multi_round_qa") \
        .distinct()

    multi_round_result = await db.execute(multi_round_check_query)
    multi_round_sets = {row for row in multi_round_result.scalars().all()}

    data = [
        QuestionSetData(
            id=question_set.id,
            question_set_identifier=question_set.question_set_identifier,
            name=question_set.name,
            desc=question_set.desc,
            version=question_set.version,
            created_at=(
                question_set.created_at.strftime("%Y-%m-%d %H:%M:%S")
                if question_set.created_at
                else None
            ),
            updated_at=(
                question_set.updated_at.strftime("%Y-%m-%d %H:%M:%S")
                if question_set.updated_at
                else None
            ),
            questions_number=question_set.questions_number,
            has_multi_round=question_set.question_set_identifier in multi_round_sets,
            created_user_id=question_set.created_user_id
            # Check if this set has multi-round questions
        )
        for question_set in sets
    ]

    return total, data


# 问题关联题集
async def relation_question_set(db: AsyncSession, val: QuestionSetDataRelationReq):
    if not crud.check_question_ids(db, val.question_ids):
        raise Exception("题集中的问题不存在")
    return await crud.question_set_relations(
        db, val.question_ids, val.question_set_identifier, True
    )


# 获取题集详情
async def get_question_set_detail(db: AsyncSession, req: QuestionSetDetailReq):
    question_set = await crud.get_question_set_detail(
        db,
        req.question_set_identifier,
        req.page,
        req.page_size,
        req.fetch_total,
        req.search_term,
    )
    return question_set


# 获取某个系统题目
async def get_system_quiz_question(db: AsyncSession, question_id: str):
    # if question is None:
    #     raise Exception("问题不存在")
    # if isinstance(question.details, dict):
    #     parsed_details = question.details
    # else:
    #     details = question.details if question.details is not None else "{}"
    #     parsed_details = json.loads(question.details)

    question = await question_crud.list_question_info_by_id(db, question_id)
    details = question.details if question.details is not None else "{}"
    created_str = question.created_at.strftime("%Y-%m-%d %H:%M:%S")
    updated_str = question.updated_at.strftime("%Y-%m-%d %H:%M:%S")
    data = SystemQuestionData(
        id=question.id,
        question_id=question.question_id,
        question=question.question,
        question_type=question.question_type,
        question_desc=question.question_desc,
        version=question.version,
        created_at=created_str,
        updated_at=updated_str,
        state=question.state,
        details=details,
        created_user_name=question.created_user_id,
        updated_user_name=question.updated_user_id,
    )
    return data


# 获取任务评测题
async def get_quiz_questions_by_task_id_and_user_id(db: AsyncSession, task_id: str):
    data = await crud.get_quiz_questions_by_task_id_and_user_id(
        db, task_id, get_user_id_v2()
    )
    questions_list = data if data is not None else []
    logger.info(f"get_quiz_questions_by_task_id_and_user_id: {questions_list}")
    return questions_list


# 创建系统问题到题库
async def add_system_question(db: AsyncSession, val: SystemQuestionCreateReq):
    if val.question_type.lower() == "qa":
        category = "subjective"
    elif val.question_type.lower() == EvaluationTaskType.MULTI_ROUND_QA.value:
        category = "subjective"
        val.question = get_first_round_question(val.details).strip()
        if not val.question:
            raise Exception("问题不存在")
    else:
        category = "objective"
    return await crud.add_system_question(
        db,
        val.question,
        val.question_desc,
        val.question_type_id,
        val.question_type,
        val.details,
        category,
        get_user_name(),
    )


# 多轮类型题目默认去第一组数据给 question
def get_first_round_question(question_info_details_dict) -> str:
    if not question_info_details_dict:
        return ""
    if isinstance(question_info_details_dict, dict):
        qa_round = question_info_details_dict.get("qa_round", [])
        if qa_round and isinstance(qa_round, list) and len(qa_round) > 0:
            return qa_round[0].get("question", "")
    return ""


async def check_discarded_question(db: AsyncSession, question_id: str):
    question_sets = await crud.get_question_set_by_question_id(db, question_id)
    if not question_sets:
        raise Exception("题库不存在")
    else:
        question_set_identifiers = {qs["question_set_id"] for qs in question_sets}
        task_list = await crud.get_task_by_question_set_identifiers_and_unstart_task(
            db, question_set_identifiers
        )

        if task_list and len(task_list) > 0:
            task_names = "、".join(task.task_name for task in task_list)
            message = f"【{task_names}】{QUESTION_SET_DISCARD_TIP}"
        else:
            set_names = "、".join(
                question_set["name"] for question_set in question_sets
            )
            message = f"【{set_names}】{QUESTION_DISCARD_TIP}"
    return {"message": message}


# 删除题集 delete_question_set
async def delete_question_set(db: AsyncSession, val: QuestionSetDeleteReq):
    user_permissions = user.get_user_permissions()
    has_permission = should_match([Permission.EVAL_MGMT.value], user_permissions)
    if not has_permission:
        raise Exception("无权限")
    db_set = await crud.get_question_set_by_id(db, val.question_set_identifier)
    if db_set is None:
        raise Exception("题集不存在")
    elif db_set.state == 3:
        raise Exception("题集已经废弃")

    # 校验问题是否在使用
    req = QueryPlanReq(question_set_id=db_set.id)
    plans, total = await evaluation_plan_crud.get_quiz_plan_list(db, req)
    if total > 0:
        for plan in plans:
            # 0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束
            if plan.state in [0, 1, 2, 4, 5, 6]:
                raise Exception(f"\"{plan.plan_name}\"任务进行中，不能删除该题集")

    await crud.delete_question_set(db, val.question_set_identifier)
    return "删除成功"


# 检查题集id关联的任务
async def check_task_by_question_set_identifier(
        db: AsyncSession, question_set_identifier: str
):
    task_list = await crud.get_task_by_question_set_identifiers_and_unstart_task(
        db, [question_set_identifier]
    )

    if not task_list:
        raise Exception("任务不存在")
    else:
        task_names = "、".join(task.task_name for task in task_list)
        message = (
            f"【{task_names}】{QUESTION_SET_DISCARD_TIP}"
        )

    return {"message": message}

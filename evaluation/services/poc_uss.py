import logging
from typing import List, Optional

import httpx
from evaluation.services.evaluation_plan_service import get_header
from evaluation.share.util.app_config import CONFIG
from evaluation.share.util.json_utils import JsonUtils
from evaluation.share.orm.platform_schema import User

logger = logging.getLogger("plaintext")


async def get_user_list_from_poc(search_term: Optional[str]) -> List[User]:
    logger.info(f"get_user_list_uss begin: {search_term}")
    # url = f"{CONFIG.get('common', 'platform')}/basic_user/api/v1/users"
    role_id = 5  # 评测人员角色id
    url = f"{CONFIG.get('common', 'platform')}/api/v1/roles/{role_id}/users"

    header = get_header()
    # header["host"] = CONFIG.get('common', 'host')

    async with httpx.AsyncClient() as client:
        response = await client.get(
            url=url,
            headers=header,
        )

        logger.info(f"get_user_list_from_poc url: {url},head:{JsonUtils.to_json(header)},userInfo: {response}")
        result = response.json()

        if search_term:
            search_term_lower = search_term.lower()
            filtered_users = [
                User(
                    user_id=str(employee["user_id"]) if employee["user_id"] is not None else 0,
                    user_no=str(employee["user_id"]) if employee["user_id"] is not None else "",
                    name=employee["user_name"].lower() if employee["user_name"] is not None else "",
                    full_name=employee["user_full_name"] if employee["user_full_name"] is not None else "",
                    department=employee["user_department"] if employee["user_department"] is not None else "",
                    # roles=employee["roles"] if employee["roles"] is not None else [],
                )
                for employee in result["data"]
                if search_term_lower in employee["user_name"].lower()
                   or search_term_lower in employee["user_full_name"].lower()
            ]
        else:
            filtered_users = [
                User(
                    user_id=str(employee["user_id"]) if employee["user_id"] is not None else 0,
                    user_no=str(employee["user_id"]) if employee["user_id"] is not None else "",
                    name=employee["user_name"].lower() if employee["user_name"] is not None else "",
                    full_name=employee["user_full_name"] if employee["user_full_name"] is not None else "",
                    department=employee["user_department"] if employee["user_department"] is not None else "",
                    # roles=employee["roles"] if employee["roles"] is not None else [],
                )
                for employee in result["data"]
            ]

        return filtered_users


async def get_users_from_poc_by_user_ids(user_ids: List[str]) -> List[User]:
    try:
        user_info_list = await get_user_list_from_poc(None)
    except Exception as e:
        logger.error(f"get_users_by_user_ids error: {e}")
        return []
    if len(user_ids) == 0:
        return user_info_list

    result = []
    for u in user_info_list:
        if u.user_id in user_ids:
            result.append(u)
    return result

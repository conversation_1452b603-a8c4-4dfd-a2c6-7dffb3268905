import json
import logging
import uuid
from enum import Enum
from typing import List, Optional

import httpx
from sqlalchemy import join, select
from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.api.evaluate_schema import EvaluationTaskType
from evaluation.constants.status_enum import QuizPlanStatusEnum, QuizTypeEnum, QuizPlanDelEnum
from evaluation.api.req.evaluation_model import (
    Dim,
    UserInfo,
    QuizPlanReq,
    QuizPlanObj,
    Product,
    DimSetObj, FetchModelsReq, EstimationReq, QueryPlanReq, UserTaskReq
)
from evaluation.services import plan_task_service, inference_service
from evaluation.share.orm import crud, evaluation_plan_crud, plan_task_crud, database, inference_service_crud, \
    user_task_crud
from evaluation.share.orm.model import (
    SystemQuizPlans,
    SystemQuizQuestionSetRelationsV2,
    SystemQuizQuestions_V2,
)
from evaluation.share.orm.platform_schema import User
from evaluation.share.util import user, json_utils
from evaluation.share.util.app_config import CONFIG
from evaluation.share.util.json_utils import JsonUtils
from evaluation.share.util.permission import Permission, should_match


def get_header():
    header = {
        "Content-Type": "application/json"
    }
    if token := user.jwt_token.get():
        header["Authorization"] = f"Bearer {token}"
    return header


class ServiceState(Enum):
    DEFAULT = (0, "未开始")
    UPDATING = (10, "更新中")
    RUNNING = (50, "运行中")
    DELETING = (80, "删除中")
    OFFLINE_IN_PROGRESS = (90, "下线中")
    OFFLINE = (100, "已下线")
    ERROR = (-100, "异常")
    UNKNOWN = (-10, "未知")

    def __init__(self, value, label):
        self._value_ = value
        self.label = label


def convert_state(state: str):
    if state not in ServiceState.__members__:
        # 尽可能兼容未知的新增状态
        logger.warn(f"Unknown service state: {state}")
        return ServiceState.UNKNOWN
    return ServiceState[state]


# All = "all"
logger = logging.getLogger("plaintext")


async def get_quiz_dim(db: AsyncSession, dim_set_id: int):
    # 根据评测标准集合ID获取的具体评测标准
    dims = list()
    data = await evaluation_plan_crud.get_quiz_criteria(db, dim_set_id)
    for row, _, _ in data:
        dim = Dim(
            criteria=row.title,
            desc=row.value,
            rating_key=row.rating_key,
            rating_type=row.rating_type,
        )
        dims.append(dim)
    return dims


async def get_quiz_dim_sets(db: AsyncSession, dim_set_id: int):
    # 查询评测标准集合和集合对应的标准
    set_dict = {}
    data = await evaluation_plan_crud.get_quiz_criteria(db, dim_set_id)
    for criteria, _, dim_set in data:
        set_id = dim_set.id
        criteria_set_identifier = dim_set.criteria_set_identifier
        quiz_type = dim_set.quiz_type

        # 创建Dim对象
        dim = Dim(
            criteria=criteria.title,
            desc=criteria.value,
            rating_key=criteria.rating_key,
            rating_type=criteria.rating_type,
        )

        # 创建DimSetObj对象
        if quiz_type not in set_dict:
            set_dict[quiz_type] = [
                DimSetObj(
                    id=set_id,
                    criteria_set_identifier=criteria_set_identifier,
                    criterias=[dim],
                )
            ]
        else:
            # 检查是否已经有一个与当前criteria_set_identifier相对应的DimSetObj对象
            for dim_set_obj in set_dict[quiz_type]:
                if dim_set_obj.criteria_set_identifier == criteria_set_identifier:
                    dim_set_obj.criterias.append(dim)
                    break
            else:  # 如果没有找到相对应的DimSetObj对象，创建一个新的
                set_dict[quiz_type].append(
                    DimSetObj(
                        id=set_id,
                        criteria_set_identifier=criteria_set_identifier,
                        criterias=[dim],
                    )
                )

    return set_dict


async def get_quiz_set_user(db: AsyncSession, user_set_id: int):
    # 根据用户集合获取结合的具体用户
    user_list = list()
    data = await evaluation_plan_crud.get_quiz_set_user(db, user_set_id)
    for user in data:
        user_obj = UserInfo(
            user_id=user.user_id, full_name=user.full_name, department=user.department
        )
        user_list.append(user_obj)
    return user_list


async def get_user_info(db: AsyncSession, page_size: int, page: int, search_term: str):
    # 权限校验
    user_permissions = user.get_user_permissions()
    has_permission = should_match([Permission.EVAL_MGMT.value, Permission.VIEW_EVAL.value], user_permissions)
    if not has_permission:
        return [], 0

    # 获取 用于评测的所有用户信息
    user_list = list()
    from evaluation.services import user_service

    try:
        user_list: List[User] = await user_service.get_user_list(search_term)
        return user_list, len(user_list)
    except Exception as e:
        logger.error(f"get_users_by_user_ids error: {e}")
        return [], 0


async def create_quiz_plan(db: AsyncSession, val: QuizPlanReq):
    # 创建评测计划
    quiz_plan, message = await evaluation_plan_crud.create_quiz_plan(db, val)
    return quiz_plan, message


async def update_quiz_plan(db: AsyncSession, val: QuizPlanReq):
    # 更新评测计划
    message = ""
    plan = await evaluation_plan_crud.get_quiz_plan(db, val.plan_id)
    # print("update_quiz_plan------", plan)
    if not plan:
        message = "记录不存在"
        return False, message
    success = await evaluation_plan_crud.update_quiz_plan(db, val)
    return success, message


async def get_quiz_plans(db: AsyncSession, queryReq: QueryPlanReq) -> (list, int):
    user_permissions = user.get_user_permissions()
    has_permission = should_match([Permission.EVAL_MGMT.value, Permission.VIEW_EVAL.value], user_permissions)
    if not has_permission:
        logger.info(f"no permission, user:{user.get_user_name()}, user_permissions:{user_permissions}.")
        return [], 0

    # 获取评测计划列表
    plan_list = []
    data, total = await evaluation_plan_crud.get_quiz_plan_list(db, queryReq)
    logger.info(f"get_quiz_plan_list size: {total},page_size: {queryReq.page_size}, page: {queryReq.page}")

    if total <= 0:
        return plan_list, total

    q_set = await evaluation_plan_crud.get_q_set(db)
    q_identifier_set = await evaluation_plan_crud.get_q_identifier_set(db)
    user_set = await evaluation_plan_crud.get_user_set(db)
    dim_set = await evaluation_plan_crud.get_dim_set(db)

    # 题集快照
    plan_ids = [d.plan_id for d in data]
    question_set_name_snapshot = await inference_service.get_question_set_name_snapshot(db, plan_ids)

    # 转成评测计划对象
    for plan in data:
        obj = await generate_plan_obj(plan, q_set, q_identifier_set, user_set, dim_set)

        # 题集名称快照
        key = f"{obj.plan_id}-{obj.question_identifier}"
        logger.info(f"get_quiz_plans key: {key}")
        if key in question_set_name_snapshot:
            obj.question_set = question_set_name_snapshot[key]

        # 统计任务执行情况
        user_tasks = await user_task_crud.list_user_task(db, UserTaskReq(evaluation_id=plan.plan_id))
        # 已完成的任务数量
        done_tasks = [t for t in user_tasks if t.task_state == 3]
        obj.task_num = len(user_tasks)
        obj.task_evaluation_num = len(done_tasks)

        plan_list.append(obj)
    return plan_list, total


# 检查题集是否含有多轮的题目
async def check_qa_round_dim_set(db: AsyncSession, question_set_id):
    query = (
        select(SystemQuizQuestionSetRelationsV2.question_set_identifier)
        .select_from(
            join(
                SystemQuizQuestionSetRelationsV2,
                SystemQuizQuestions_V2,
                SystemQuizQuestionSetRelationsV2.question_id
                == SystemQuizQuestions_V2.question_id,
            )
        )
        .where(
            SystemQuizQuestions_V2.question_type
            == EvaluationTaskType.MULTI_ROUND_QA.value,
            SystemQuizQuestionSetRelationsV2.question_set_identifier == question_set_id,
        )
        .distinct()
    )

    result = await db.execute(query)
    has_multi_round = result.scalars().first() is not None

    return has_multi_round


async def get_quiz_plan_detail(db: AsyncSession, plan_id: str) -> []:
    # 获取评测计划详情
    plan = await evaluation_plan_crud.get_quiz_plan(db, plan_id)
    logger.info(f"get_quiz_plan_detail plan_id: {plan_id},plan: {plan}")

    if not plan:
        message = f"没有查询到 plan_id 为 {plan_id} 的记录"
        raise Exception(message)

    q_set = await evaluation_plan_crud.get_q_set(db)
    q_identifier_set = await evaluation_plan_crud.get_q_identifier_set(db)
    user_set = await evaluation_plan_crud.get_user_set(db)
    dim_set = await evaluation_plan_crud.get_dim_set(db)
    _, user_set_members = await crud.get_user_set_detail_by_id(db, plan.user_set_id)
    plan_obj = await generate_plan_obj(
        plan, q_set, q_identifier_set, user_set, dim_set
    )
    return plan_obj


async def copy_plan(db: AsyncSession, plan_id: str):
    plan_detail = await evaluation_plan_crud.get_quiz_plan(db, plan_id)
    logger.info(f"get plan detail.plan_id:{plan_id},plan_detail:{JsonUtils.to_json(plan_detail)}")
    if plan_detail is None:
        raise Exception("获取评测计划详情失败")

    plan_name_copy = f"{plan_detail.plan_name}_copy_{uuid.uuid4().hex[:4]}"[:50]
    # 创建评测计划
    req = QuizPlanReq(
        plan_name=plan_name_copy,
        desc=plan_detail.desc,
        dim_set_id=plan_detail.dim_set_id,
        question_set_id=plan_detail.question_set_id,
        user_set_id=plan_detail.user_set_id,
        model_id=plan_detail.model_id,
        model_name=plan_detail.model_name,
        model_type=plan_detail.model_type,
        model_parameters=plan_detail.model_parameters,
        gpu_type=plan_detail.gpu_type,
        replica_num=plan_detail.replica_num,
        replica_gpu_num=plan_detail.replica_gpu_num,
        resource_group=plan_detail.resource_group,
        resource_pool=plan_detail.resource_pool,
    )
    await create_quiz_plan(db, req)


# 生成评测计划对象
# async
async def generate_plan_obj(plan: SystemQuizPlans, q_set: dict[int, str], q_identifier_set: dict[int, str],
                            user_set: dict[int, str],
                            dim_set: dict[int, str]):
    return QuizPlanObj(
        id=plan.id,
        plan_id=plan.plan_id,
        plan_name=plan.plan_name,
        desc=plan.desc,
        quiz_type=plan.quiz_type,
        quiz_type_name=QuizTypeEnum.get_description_by_code(plan.quiz_type),
        dim_set_id=plan.dim_set_id,
        dim_set=dim_set.get(plan.dim_set_id, "unknown"),
        question_set_id=plan.question_set_id,
        question_identifier=q_identifier_set.get(plan.question_set_id, "unknown"),
        question_set=q_set.get(plan.question_set_id, "unknown"),
        user_set_id=plan.user_set_id,
        user_set=user_set.get(plan.user_set_id, "unknown"),
        created_user=plan.created_user,
        updated_user=plan.updated_user,
        created_at=f"{plan.created_at:%Y-%m-%d %H:%M:%S}",
        updated_at=f"{plan.updated_at:%Y-%m-%d %H:%M:%S}",
        state=plan.state,
        state_desc=QuizPlanStatusEnum.get_description_by_code(plan.state),
        model_id=plan.model_id,
        model_name=plan.model_name,
        model_parameters=plan.model_parameters,
        gpu_type=plan.gpu_type,
        replica_num=plan.replica_num,
        replica_gpu_num=plan.replica_gpu_num,
        resource_group=plan.resource_group,
        resource_pool=plan.resource_pool
    )


# 发布评测计划
async def publish_quiz_plan(db: AsyncSession, plan_id: str):
    plan = await evaluation_plan_crud.get_quiz_plan(db, plan_id)
    if not plan:
        raise Exception("评测计划不存在")

    if plan.state != QuizPlanStatusEnum.get_enum_code(QuizPlanStatusEnum.UNPUBLISHED):
        logger.info(f"publish_quiz_plan plan_id: {plan_id}, state: {plan.state}")
        raise Exception("评测计划已经发布")

    try:
        if plan.quiz_type != "art":
            raise Exception("不支持此类型计划发布")

        # 删除历史部署记录
        await inference_service_crud.delete_inference_service(db, plan_id)
        logger.info(f"delete_inference_service end.plan_id:{plan_id}")

        # 部署推理服务
        service_id, service_state = await create_inference_service(plan)
        logger.info(
            f"create_inference_service end.plan_id:{plan.plan_id},service_id:{service_id},service_status:{service_state}")

        # 添加推理服务，更新中
        await inference_service_crud.create_inference_service(db, plan.plan_id, plan.plan_name,
                                                              plan.model_id,
                                                              plan.model_name,
                                                              service_id, 1, plan.model_type)
        logger.info(f"create_inference_service end.plan_id:{plan.plan_id}")

        # 添加评测任务
        user_quiz_task = await plan_task_service.generate_plan_task(db, plan)
        logger.info(
            f"generate_plan_task end.plan_id:{plan.plan_id},user_quiz_task:{user_quiz_task.task_id}")

        # 添加任务明细
        await plan_task_service.generate_plan_task_info(db, plan, user_quiz_task)
        logger.info(f"generate_plan_task_info end.plan_id:{plan.plan_id}")

        # 更新计划状态-->排队中
        await evaluation_plan_crud.publish_quiz_plan(db, plan_id)
        logger.info(f"publish_quiz_plan end.plan_id:{plan.plan_id}")

    except Exception as e:
        logger.error(f"publish_quiz_plan error: {e}")
        raise Exception(f"发布评测计划失败:{str(e)}")


# 删除评测计划
async def delete_quiz_plan(db: AsyncSession, plan_ids: list[str]):
    plans = await evaluation_plan_crud.get_quiz_plan_by_plan_ids(db, plan_ids)
    if len(plans) == 0:
        raise Exception("数据异常")
    if len(plans) != len(plan_ids):
        raise Exception("数据异常")

    try:
        for p in plans:
            await evaluation_plan_crud.delete_quiz_plan(db, p.plan_id)
    except Exception as e:
        logger.info(f"delete_quiz_plan occur error:{e}")
        raise e


# 任务取消
async def cancel_task(db, plan_id):
    # 查询计划
    plan = await evaluation_plan_crud.get_quiz_plan(db, plan_id)
    if not plan:
        raise Exception("评测计划不存在")

    # 查询推理服务
    inference_services = await inference_service_crud.list_inference_services(db, plan_id, None, None, None)
    if len(inference_services) == 0:
        return

    # 更新推理服务状态-->下线
    await inference_service_crud.update_inference_service_state(db, plan_id, 4)

    # 下线已经部署的推理服务
    try:
        for service in inference_services:
            await delete_service(service.inference_service_id)
            logger.info(f"delete_service end.service_id:{service.inference_service_id}")
    except Exception as e:
        logger.error(f"cancel_task error: {e}")

    # 任务--》推理失败
    await inference_service.update_eval_status(db, plan_id, 3, "任务取消")


async def fetch_models(page_index: int, page_size: int):
    logger.info("Fetching models...")
    url = f"{CONFIG.get('training', 'platform')}/api/v1/user/modelinfo/list?page_index={page_index}&page_size={page_size}"
    header = get_header()
    # header["host"] = CONFIG.get('training', 'host')

    async with httpx.AsyncClient() as client:
        raw_response = await client.get(url, headers=header)
        raw_response.raise_for_status()
    logger.debug(f"Response: {raw_response.text}")
    if raw_response.status_code != 200:
        raise Exception(
            f"Failed to fetch_models, status_code: {raw_response.status_code}, text: {raw_response.text}")
    response = raw_response.json()
    if response["code"] != 200:
        raise Exception(
            f"Failed to fetch_models, code: {response['code']}, message: {response['message']}")
    return response.get("data", {})


async def get_inference_resource_pool_groups():
    url = f"{CONFIG.get('inference', 'platform')}/api/v1/inference/service/resource-pool-group/list"
    header = get_header()
    # header["host"] = CONFIG.get('inference', 'host')

    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=header)

    if response.status_code != 200:
        raise Exception(
            f"Failed to get inference resource pool list, status_code: {response.status_code}, text: {response.text}")
    response_json = response.json()
    if response_json["code"] != 200:
        raise Exception(
            f"Failed to get inference resource pool list, code: {response_json['code']}, message: {response_json['message']}")

    return response_json.get("data", {})


async def get_inference_service_free_resource(resource_pool: str, resource_group: str):
    url = f"{CONFIG.get('inference', 'platform')}/api/v1/inference/service/free/resource"
    params = {
        "resource_pool": resource_pool,
        "resource_group": resource_group
    }

    header = get_header()
    # header["host"] = CONFIG.get('inference', 'host')

    async with httpx.AsyncClient() as client:
        response = await client.get(url, params=params, headers=header)

    if response.status_code != 200:
        raise Exception(
            f"Failed to get free resource, status_code: {response.status_code}, text: {response.text}")
    response_json = response.json()
    if response_json["code"] != 200:
        raise Exception(
            f"Failed to get free resource, code: {response_json['code']}, message: {response_json['message']}")

    return response_json.get("data", {})


async def estimate(request: EstimationReq) -> dict:
    if not request:
        return {"replicas": 0, "estimations": []}
    model_data = {}
    model_data["model_id"] = request.model_id
    model_data["gpu_type"] = request.gpu_type
    model_data["model_parameters"] = request.model_parameters
    model_data["context_length"] = request.context_length
    model_data["seq_len"] = request.seq_len
    model_data["type"] = request.type
    if request.resource_pool is not None:
        model_data["resource_pool"] = request.resource_pool
    if request.resource_group is not None:
        model_data["resource_group"] = request.resource_group
    replicas, model_gpu_usages = await estimate_replicas(model_data)
    for gpus_per_replica in model_gpu_usages:
        gpus_per_replica["replicas"] = replicas
    return {"replicas": replicas, "estimations": model_gpu_usages}


async def estimate_replicas(model):
    replicas = {}
    model_gpu_usages = []
    response_free_resource = await get_inference_service_free_resource(
        model.get("resource_pool", "evaluation"),
        model.get("resource_group", "default"))
    total_gpus = {response_free_resource.get("gpu_type"): response_free_resource.get("total_gpu")}
    gpu_type = model.get("gpu_type")
    if gpu_type not in total_gpus:
        logger.error(f'No gpu type {gpu_type} is available for model {model["model_id"]}')
        gpus_per_replica = 0
    else:
        gpus_per_replica = get_gpu_usage(model)
    model_gpu_usages.append({
        "model_id": model.get("model_id"),
        "gpus_per_replica": gpus_per_replica
    })
    if gpu_type not in replicas:
        replicas[gpu_type] = 0
    replicas[gpu_type] += gpus_per_replica
    for gpu_type, gpu_usage in replicas.items():
        replicas[gpu_type] = total_gpus[gpu_type] // gpu_usage if gpu_usage > 0 else 0
    return min(replicas.values()), model_gpu_usages


def get_gpu_usage(model) -> int:
    default_gpu_usage = CONFIG.getint("inference", "default_gpus_per_replica")

    if model.get("model_parameters") is None:
        return default_gpu_usage
    parameter_count = model.get("model_parameters")
    if type(parameter_count) is not str or len(parameter_count) == 0 or str(parameter_count[-1]).upper() != "B":
        return default_gpu_usage
    try:
        parameter_count = int(float(parameter_count[:-1]))
    except ValueError as e:
        logger.exception(f"Failed to parse parameter count: {parameter_count}")
        return default_gpu_usage

    context_length = None
    if model.get("context_length") is not None:
        context_length = model.get("context_length")
    elif model.get("seq_len") is not None:
        context_length = model.get("seq_len")

    if type(context_length) is not int or context_length <= 0:
        return default_gpu_usage

    if model.get("type") == "base":
        if parameter_count < 10:
            return 2 if context_length <= 8192 else 4 if context_length <= 16384 else 8
        elif parameter_count < 20:
            return 4 if context_length <= 8192 else 8
        else:
            return 8
    elif model.get("type") == "chat":
        if parameter_count < 10:
            return 1 if context_length <= 8192 else 2 if context_length <= 16384 else 4
        elif parameter_count < 20:
            return 2 if context_length <= 8192 else 4
        elif parameter_count < 40:
            return 4 if context_length <= 32768 else 8
        else:
            return 8
    else:
        return default_gpu_usage


async def create_inference_service(plan: SystemQuizPlans) -> tuple:
    task_id = plan.plan_id
    plan_id = plan.plan_id
    model_id = plan.model_id
    cn_service_name = plan.plan_name
    en_service_name = f"id-{plan.id}"
    model_parameters_ = json.loads(plan.model_parameters)
    request_body = {"en_name": en_service_name, "cn_name": cn_service_name, "service_label": "eval-human",
                    "model_name": plan.model_name, "model_properties": model_parameters_,
                    "replicas": plan.replica_num, "task_id": task_id, "resource_pool": plan.resource_pool,
                    "resource_group": plan.resource_group, "gpu_type": plan.gpu_type,
                    "per_replica_gpus": plan.replica_gpu_num, "create_user_id": plan.create_user_id,
                    "create_by": plan.created_user, "evaluation_type": "human"}
    logger.debug(f"Create inference service request body: {request_body}")

    url = f"{CONFIG['inference']['platform']}/api/v1/inference/service/evaluation/deploy"
    header_ = get_header()
    # header_["host"] = CONFIG['inference']['host']

    logger.info(f"begin devlop url:{url},header:{header_},body:{request_body}")
    async with httpx.AsyncClient() as client:
        response = await client.post(url, headers=header_, json=request_body)
    if response.status_code != 200:
        raise Exception(f"Failed to create inference service, task_id: {plan_id}, model_id: {model_id}, "
                        f"status_code: {response.status_code}, text: {response.text}")
    response_json = response.json()
    if response_json["code"] != 200:
        raise Exception(f"Failed to create inference service, task_id: {plan_id}, model_id: {model_id}, "
                        f"code: {response_json['code']}, message: {response_json['message']}")

    service_id = response_json["data"]["id"]
    service_state = convert_state(response_json["data"]["status"])
    logger.info(f"Creating inference service, task_id: {plan_id}, model_id: {model_id}, service_id: {service_id}, "
                f"state: {service_state}")

    return service_id, service_state


async def delete_service(service_id: int):
    request_body = {"id": service_id}
    logger.debug(f"Delete inference service request body: {json.dumps(request_body, ensure_ascii=False)}")

    url = f"{CONFIG['inference']['platform']}/api/v1/inference/service/evaluation/offline"
    header = get_header()
    # header["host"] = CONFIG['inference']['host']

    async with httpx.AsyncClient() as client:
        response = await client.post(url, headers=header, json=request_body)
    if response.status_code != 200:
        raise Exception(f"Failed to delete inference service, service_id: {service_id}, "
                        f"status_code: {response.status_code}, text: {response.text}")
    response_json = response.json()
    if response_json["code"] != 200:
        raise Exception(f"Failed to delete inference service, service_id: {service_id}, "
                        f"code: {response_json['code']}, message: {response_json['message']}")

    service_state = convert_state(response_json["data"]["status"])
    logger.info(f"Deleting inference service, service_id: {service_id}, state: {service_state}")


"""
批量查询推理服务实例状态：http://hanhai-dev.ai.ksyun.com/inference/api/v1/inference/service/evaluation/status
{
"code": 200,
"message": "success",
"data": [
    {
        "id": 3512,
        "status": "RUNNING",
        "en_name": "f442aab0-38c2-456d-919d-c2bec2d1665b-1728377235964",
        "message": "",
        "age": "",
        "internal_url": "f442aab0-38c2-456d-919d-c2bec2d1665b-1728377235964.hanhai-inference.svc.cluster.local",
        "api_address": "/v2/models/qwen1.5_7b-chat/infer"
    }
]
}
"""


async def fetch_services(service_ids: list[int]) -> list:
    url = f"{CONFIG['inference']['platform']}/api/v1/inference/service/evaluation/status"
    header_ = get_header()
    # header_["host"] = CONFIG['inference']['host']

    logger.info(f"fetch_services url:{url},head:{header_},ids:{service_ids}")
    async with httpx.AsyncClient() as client:
        response = await client.post(url, headers=header_, json={"ids": service_ids})
    if response.status_code != 200:
        raise Exception(f"Failed to get inference service state, service_ids: {json.dumps(service_ids)}, "
                        f"status_code: {response.status_code}, text: {response.text}")
    response_json = response.json()
    if response_json["code"] != 200:
        raise Exception(f"Failed to get inference service state, service_ids: {service_ids}, "
                        f"code: {response_json['code']}, message: {response_json['message']}")
    return response_json["data"]


async def get_quiz_plan_users(db: AsyncSession):
    # 创建评测计划
    users = await evaluation_plan_crud.get_quiz_plan_users(db)
    return users

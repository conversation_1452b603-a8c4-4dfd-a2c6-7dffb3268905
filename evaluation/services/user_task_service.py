import logging
import uuid
from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.api.req.evaluation_model import UserTaskInfoReq, UserTaskReq
from evaluation.constants.status_enum import QuizPlanStatusEnum, QuizTypeEnum, QuizPlanDelEnum
from evaluation.services import user_service
from evaluation.share.orm import crud, evaluation_plan_crud, plan_task_crud, user_task_crud
from evaluation.share.orm.model import (
    SystemQuizPlans,
    UserQuizTasks,
    UserQuizTasksInfo,
    SystemQuizPlanTask,
    SystemQuizPlanTaskInfo,
)
from evaluation.share.util.user import get_user_id_v2, get_user_name
from evaluation.vo.task_progress_vo import TaskProgress

logger = logging.getLogger("plaintext")


# 添加用户评测任务
async def list_user_task(db: AsyncSession, userTask: UserQuizTasks) -> SystemQuizPlanTask | None:
    user_id = get_user_id_v2()
    name = get_user_name()

    user_task = await plan_task_crud.generate_eval_task(db, userTask)
    return user_task


# 任务进度条
async def list_task_progress(db: AsyncSession, plan_id: str) -> list[TaskProgress]:
    # 评测任务
    req = UserTaskReq(evaluation_id=plan_id)
    user_tasks = await user_task_crud.list_user_task(db, req)
    if len(user_tasks) == 0:
        return []

    # 用户信息
    user_ids = [str(u.user_id) for u in user_tasks]
    user_infos = await user_service.get_users_by_user_ids(user_ids)
    user_name_dict = {u.user_id: u.name for u in user_infos}

    task_progress = []
    for task in user_tasks:
        # 评测任务详情
        user_task_info_req = UserTaskInfoReq(parent_task_id=task.task_id)
        user_task_infos = await user_task_crud.list_user_task_info(db, user_task_info_req)

        finish_tasks = [t for t in user_task_infos if t.task_state == 3]
        task_evaluation_num_ = len(finish_tasks)
        task_num_ = len(user_task_infos)

        progress = TaskProgress(user_id=task.user_id, user_name=user_name_dict.get(str(task.user_id), ""),
                                task_num=task_num_, task_evaluation_num=task_evaluation_num_)
        task_progress.append(progress)

    return task_progress



import asyncio
import json
import traceback

from evaluation.api.req.user_task_model import User<PERSON>askObj
from evaluation.share.util.plaintext_log import logger
import random
from typing import Any, List, Dict, Tuple
from evaluation.vo.dislike_model import DislikeQueryModel
from sqlalchemy.ext.asyncio import AsyncSession

from evaluation.constants.status_enum import (
    EvaluationTaskStatusEnum,
)
from evaluation.api.req.evaluation_model import (
    RateQuestionReq,
    SubmitEvaluationTaskReq,
    ReviewQuestionReq,
    Product, UserTaskReq, QueryPlanReq, UserTaskInfoReq
)
from evaluation.share.orm import crud, database, user_task_crud, evaluation_plan_crud, system_quiz_criteria_crud, \
    user_quiz_tasks_criteria_info_crud, system_question_dislike_curd
from evaluation.share.orm.crud import (
    list_questions_by_question_set_identifier,
    get_question_by_id,
)
from evaluation.share.orm.evaluation_plan_crud import get_quiz_set_user
from evaluation.share.orm.model import SystemQuiz<PERSON>lans, UserQuizTasks, model_to_dict
from evaluation.services import system_evaluation_service, inference_service
from evaluation.single.knowleage_client import KnowledgeClientSingleton
from evaluation.share.util import metric, user
from evaluation.share.util.permission import should_match, Permission
from evaluation.share.util.sse import (
    SSEChatModel,
    get_sse_chat,
)
from evaluation.share.util.user import (
    get_user_name,
    get_user_full_name,
    get_user_department, get_user_id_v2,
)


# 获取用户评测任务信息
async def get_evaluation_task_by_user_id(db: AsyncSession, user_id: str):
    return await crud.get_evaluation_task_by_user_id(db, user_id)


# 检查评测标准是否打分过了
async def check_criteria_rated(db: AsyncSession, task_id: str):
    criteria_info_list = await crud.get_evaluation_task_criteria_info_by_task_id(
        db, task_id
    )
    for criteria_info in criteria_info_list:
        if criteria_info.system_type == "ab":
            if (
                    criteria_info.rating_string_value is None
                    or criteria_info.rating_string_value == ""
            ):
                return False
        else:
            if criteria_info.rating_key == "b":
                return True
            if (
                    criteria_info.rating_int_value is None
                    or criteria_info.rating_int_value == -1
            ):
                return False

    return True


# 针对每个问题的不同评测标准进行评分
async def rate_quiz_question(req: RateQuestionReq, db: AsyncSession):
    user_task_info = await crud.get_evaluation_task_info_by_task_id(db, req.task_id)
    if user_task_info is None:
        raise Exception(f"用户任务明细不存在:{req.task_id}")

    if user_task_info.user_id != get_user_id_v2():
        raise Exception(f"只能对自己的任务评分:{req.task_id}")

    # 用户主任务
    user_task = await crud.get_user_task_by_task_id(db, user_task_info.parent_task_id)
    if user_task is None:
        raise Exception(f"用户任务不存在:{req.task_id}")

    if user_task.task_state == 1:
        # 未开始==》评测中
        # 更新用户任务为评分中
        await user_task_crud.update_user_task_status(db, user_task.task_id, 2)

        # plan、plan_task由待评测更新为评测中
        # 推理完成、评测任务下发后，任务处于待评测状态，当有用户进行评测后，任务处于评测中的状态
        plan = await evaluation_plan_crud.get_quiz_plan(db, user_task.evaluation_id)
        if plan.state == 4:
            # 任务
            # 待评测 --》评测中
            await inference_service.update_eval_status(db, plan.plan_id, 5, "评测中")


    # 更新用户任务明细为评分中
    await user_task_crud.update_user_task_info_status(db, user_task_info.task_id, 2)

    # 评分
    await crud.rate_quiz_question(
        db,
        user_id=get_user_id_v2(),
        question_id=req.question_id,
        task_id=req.task_id,
        rating_key=req.rating_key,
        rating_value=req.rating_value,
        feedback=req.feedback,
    )

    # 当所有维度评分完成，则当前任务明细状态为已完成
    user_criteria_infos = await user_quiz_tasks_criteria_info_crud.list_user_quiz_tasks_criteria_info(db,
                                                                                                      user_task_info.task_id)
    criteria_infos_dict = {t.rating_key: t.rating_int_value for t in user_criteria_infos}
    # 准确性(a-star);专业性(c-star)
    # {a:准确性,c:专业性}
    # -1 为默认值
    if criteria_infos_dict["a"] > 0 and criteria_infos_dict["c"] > 0:
        # 评测完成
        await user_task_crud.update_user_task_info_status(db, user_task_info.task_id, 3)


# 更新metric
async def update_metric(req: RateQuestionReq, db: AsyncSession):
    answer = await crud.get_evaluation_task_info_by_task_id(db, req.task_id)
    if answer is None:
        return
    criteria_dict = {
        "a": "accuracy",
        "c": "professionalism",
        "b": "illusion",
        "AB": "ab",
        "logical_coherence": "logical_coherence",
        "topic_continuity": "topic_continuity",
    }
    rating_key = criteria_dict[req.rating_key]
    if rating_key is None:
        rating_key = req.rating_key
    if rating_key == "ab":
        return
    metric_value = {
        "evaluation.type": "artificial",
        "evaluation.record_type": "question",
        "evaluation.task_id": req.task_id,
        "evaluation.question_id": int(req.question_id),
        f"evaluation.rating.{rating_key}": int(req.rating_value),
        "trace.id": answer.trace_id,
        "evaluation.user.id": get_user_id_v2(),
        "evaluation.user.name": get_user_name(),
        "evaluation.user.full_name": get_user_full_name(),
        "evaluation.user.department": get_user_department(),
        "evaluation.time.at.update": metric.get_current_time(),
    }

    metric.start()
    metric.update("evaluation", metric_value)
    metric.stop()


# 提交用户评测任务
async def submit_evaluation_task(req: SubmitEvaluationTaskReq, db: AsyncSession):
    user_task = await crud.get_evaluation_task_by_task_id(db, req.task_id)
    if user_task is None:
        raise Exception(f"用户任务不存在:{req.task_id}")

    if user_task.user_id != get_user_id_v2():
        raise Exception(f"只能提交自己的评测任务:{req.task_id}")

    uncompleted_list = await crud.judge_task_finish_v2(db, req.task_id)
    if len(uncompleted_list) > 0:
        raise Exception("评测任务未完成")

    await crud.submit_evaluation_task(
        db, user_id=user_task.user_id, task_id=user_task.task_id
    )

    # 当所有任务完成时，计划状态为已完成
    user_task_rq = UserTaskReq(evaluation_id=user_task.evaluation_id)
    user_task_list = await user_task_crud.list_user_task(db, user_task_rq)
    for task in user_task_list:
        if task.task_state != 3:
            return

    # 任务评测中--》任务结束
    await inference_service.update_eval_status(db, user_task.evaluation_id, 7, "任务结束")
    logger.info(f"update_eval_status task_id:{user_task.evaluation_id}, status:任务结束")

    # 生成快照
    await inference_service.generate_snapshot(db, user_task.evaluation_id)
    logger.info(f"update_eval_status generate_snapshot plan_id:{user_task.evaluation_id}")


# 评价问题的参考答案
async def review_question(req: ReviewQuestionReq, db: AsyncSession):
    user_task_info = await crud.get_evaluation_task_info_by_task_id(db, req.task_id)
    user_task = await crud.get_evaluation_task_by_task_id(db, user_task_info.parent_task_id)
    return await crud.review_question(
        db, task_id=req.task_id, rating=req.rating, feedback=req.feedback
    )


# 更新问题的系统答案
async def update_system_answer(
        db: AsyncSession, task_id: str, question_id: int, question: str
):
    try:
        system_answer = None
        while system_answer is None:
            knowledgeClientSingleton = KnowledgeClientSingleton()
            sse_chat_model: SSEChatModel = await get_sse_chat(
                knowledgeClientSingleton.knowledge_base_url, question
            )
            return await crud.update_system_answer(
                db,
                task_id,
                question_id,
                sse_chat_model.get_messages(),
                sse_chat_model.trace_id,
                sse_chat_model.reference,
            )
    except Exception as e:
        logger.error(
            f"Error updating system answer for task {task_id} and question {question_id}: {e}"
        )
        return False


# 更新ab 问题的系统答案
async def update_ab_system_answer(
        db: AsyncSession, task_id: str, question_id: int, question: str, eval_product: str
):
    try:
        task_info = await crud.get_evaluation_task_info_by_task_id_and_question_id(
            db, task_id, question_id
        )

        db_sys_answer_list = (
            json.loads(task_info.system_answer) if task_info.system_answer else []
        )

        if eval_product:
            eval_product_list = json.loads(eval_product)
            sys_answer_list = []
            system_ab_list = ["a", "b"]
            index = 0
            for product_dict in eval_product_list:
                product = Product(**product_dict)
                sys_type = system_ab_list[index]

                if db_sys_answer_list:
                    exist_answer = next(
                        (
                            ans
                            for ans in db_sys_answer_list
                            if ans["sys_type"] == sys_type and ans["answer"]
                        ),
                        None,
                    )
                else:
                    exist_answer = None

                if exist_answer:
                    sys_answer_list.append(exist_answer)
                else:
                    try:
                        sse_chat_model: SSEChatModel = await get_sse_chat(
                            product.system, question
                        )
                        sys_answer_list.append(
                            {
                                "answer": sse_chat_model.get_messages(),
                                "sys_type": sys_type,
                                "track_id": sse_chat_model.trace_id,
                                "reference_str": sse_chat_model.reference,
                            }
                        )
                    except Exception as e:
                        logger.error(
                            f"Error updating system answer for task {task_id} and question {question_id}: {e}"
                        )
                        return False

                index += 1

            sys_answer_str = json.dumps(sys_answer_list, ensure_ascii=False)
            await crud.update_ab_sys_answer(db, task_id, question_id, sys_answer_str)
            return True
    except Exception as e:
        logger.error(
            f"Error updating system answer for task {task_id} and question {question_id}: {e}"
        )
        return False


async def fetch_and_append_sys_answer(qa_round_item, knowledge_client_url, index):
    question = qa_round_item.get("question")
    if not question:
        raise ValueError("Question not found in qa_round_item")

    sse_chat_model = await get_sse_chat(knowledge_client_url, question)
    return {
        "question": question,
        "answer": sse_chat_model.get_messages(),
        "sys_type": "art",
        "track_id": sse_chat_model.trace_id,
        "reference_str": sse_chat_model.reference,
        "round_index": index
    }


async def update_qz_round_system_answer(db: AsyncSession, task_id: str, question_id: int):
    try:
        question_info = await get_question_by_id(db, question_id)
        qa_round = question_info.details.get("qa_round")
        if not qa_round:
            raise Exception("qa_round does not exist")

        knowledgeClientSingleton = KnowledgeClientSingleton()
        tasks = [fetch_and_append_sys_answer(item, knowledgeClientSingleton.knowledge_base_url, idx)
                 for idx, item in enumerate(qa_round)]
        sys_answer_list = await asyncio.gather(*tasks, return_exceptions=True)

        sys_answers_to_store = []
        for answer in sys_answer_list:
            if isinstance(answer, Exception):
                logger.error(f"Failed to process a question in task {task_id}, question {question_id}: {answer}")
            else:
                sys_answers_to_store.append(answer)

        if not sys_answers_to_store:
            return False

        sys_answer_str = json.dumps(sys_answers_to_store, ensure_ascii=False)
        await crud.update_ab_sys_answer(db, task_id, question_id, sys_answer_str)
        return True
    except Exception as e:
        logger.error(f"Error updating system answer for task {task_id} and question {question_id}: {e}")
        return False


# 分配问题不获取系统答案
async def generate_plan_task(
        db: AsyncSession, plan: SystemQuizPlans, user_id: str
) -> Any | None:
    try:

        eval_product = []
        if plan.eval_product:
            eval_product_list = json.loads(plan.eval_product)
            eval_product = [
                Product(**product_dict) for product_dict in eval_product_list
            ]
        system_version_info = ""
        system_version = eval_product[0].version
        for product in eval_product:
            system_version_info += f"{product.version_info} "
        question_set = await list_questions_by_question_set_identifier(
            db, plan.question_set_id
        )
        random.shuffle(question_set)
        criteria_set_identifier = (
            await system_evaluation_service.get_criteria_set_identifier_by_id(
                db, plan.dim_set_id
            )
        )

        qa_round_criteria_set_identifier = (
            await system_evaluation_service.get_criteria_set_identifier_by_id(
                db, plan.qa_round_dim_set_id
            )
        )
        user_task = await crud.generate_evaluation_task(
            db,
            user_id=user_id,
            evaluation_id=plan.plan_id,
            task_name=plan.plan_name,
            task_desc=plan.desc,
            system_version=system_version,
            system_version_info=system_version_info,
            evaluation_type=plan.quiz_type,
            dim_set_id=plan.dim_set_id,
            qa_round_dim_set_id=plan.qa_round_dim_set_id,
            criteria_set_identifier=criteria_set_identifier,
            question_set_identifier=plan.question_set_id,
            qa_round_criteria_set_identifier=qa_round_criteria_set_identifier,
            task_state=EvaluationTaskStatusEnum.GENERATING,
            task_num=len(question_set),
            eval_product=plan.eval_product
        )

        if not user_task:
            return None

        criteria_set = await system_evaluation_service.get_criteria_list_by_criteria_set_identifier(
            db, criteria_set_identifier
        )
        qa_round_criteria_set = await system_evaluation_service.get_criteria_list_by_criteria_set_identifier(
            db, qa_round_criteria_set_identifier
        )

        for question in question_set:
            await system_evaluation_service.create_evaluation_task_info(
                db,
                user_id,
                question.id,
                criteria_set,
                qa_round_criteria_set,
                plan.plan_id,
                user_task.task_id,
                question.question,
                question.question_type,
                plan.quiz_type,
                eval_product=plan.eval_product,
            )
        return user_task

    except Exception as e:
        tb_info = traceback.format_exc()
        logger.error(f"chat error: {e}\nTraceback info:\n{tb_info}")
        logger.error(
            f"Error generating evaluation task for user {user_id} and evaluation {plan.plan_id}: {e}"
        )

    return None


# 是否是任务管理员
async def is_task_admin(db: AsyncSession, account_key: str) -> bool:
    admin_user_dict = await get_admin_users(db)
    for account in admin_user_dict:
        if account_key in account:
            return True
    return False


async def get_user_tasks(db: AsyncSession, user_task_req: UserTaskReq) -> (int, list):
    # 权限校验
    user_permissions = user.get_user_permissions()
    has_permission = should_match([Permission.EVAL_MGMT.value, Permission.VIEW_EVAL.value, Permission.MGMT_EVAL_TASK.value, Permission.VIEW_EVAL_TASK.value], user_permissions)
    if not has_permission:
        logger.info(f"no permission, user:{user.get_user_name()}, user_permissions:{user_permissions}.")
        return 0, []

    user_task_req.user_id = get_user_id_v2()
    total, userQuizTasks = await user_task_crud.list_user_task_by_page(db, user_task_req)
    if total <= 0:
        return 0, []

    result = list()
    q_name_set = await evaluation_plan_crud.get_q_identifier_name_set(db)

    # 题集快照
    plan_ids = [d.evaluation_id for d in userQuizTasks]
    question_set_name_snapshot = await inference_service.get_question_set_name_snapshot(db, plan_ids)

    for task in userQuizTasks:
        # 查询任务明细
        user_task_infos = await user_task_crud.list_user_task_info(db, UserTaskInfoReq(parent_task_id=task.task_id,
                                                                                       user_id=get_user_id_v2()))
        done_task = [t for t in user_task_infos if t.task_state == 3]
        done_task_count = len(done_task)
        all_task_count = len(user_task_infos)

        obj = UserTaskObj(id=task.id,
                          evaluation_id=task.evaluation_id,
                          task_id=task.task_id,
                          user_id=task.user_id,
                          task_name=task.task_name,
                          task_desc=task.task_desc,
                          question_set_name=q_name_set.get(task.question_set_identifier, "unknown"),
                          created_at=f"{task.created_at:%Y-%m-%d %H:%M:%S}",
                          updated_at=f"{task.updated_at:%Y-%m-%d %H:%M:%S}",
                          task_state=task.task_state,
                          task_num=all_task_count,
                          task_evaluation_num=done_task_count,
                          created_user=task.created_user,
                          created_user_name=task.created_user_name
                          )

        # 题集名称快照
        key = f"{task.evaluation_id}-{task.question_set_identifier}"
        if key in question_set_name_snapshot:
            obj.question_set_name = question_set_name_snapshot[key]

        # 是否可评分
        if obj.user_id == get_user_id_v2():
            obj.rate_enable = True
        result.append(obj)
    return total, result


# 任务管理员列表
async def get_admin_users(db: AsyncSession) -> list[dict]:
    req = UserTaskReq()
    req.user_id = get_user_id_v2()
    userQuizTasks = await user_task_crud.list_user_task(db, req)

    user_id_set = set()
    user_infos = []
    for task in userQuizTasks:
        if task.created_user in user_id_set:
            continue
        user_infos.append({task.created_user: task.created_user_name})
        user_id_set.add(task.created_user)
    return user_infos


# 根据任务状态获取任务全部列表
async def get_user_tasks_by_state(db: AsyncSession, state: int):
    return await crud.get_user_tasks_by_state(db, state)


# 根据用户任务id获取任务详情
async def get_user_task_detail_v2(db: AsyncSession, task_id: str):
    # 用户评测任务
    user_task = await crud.get_user_task_by_task_id(db, task_id)
    if user_task is None:
        raise Exception(f"用户任务不存在:{task_id}")

    # 评测计划
    quiz_plan = await evaluation_plan_crud.get_quiz_plan(db, user_task.evaluation_id)

    user_permissions = user.get_user_permissions()
    has_permission = should_match([Permission.EVAL_MGMT.value], user_permissions)
    if not user_task:
        raise Exception("用户任务不存在")
    elif user_task.user_id != get_user_id_v2() and has_permission is False:
        raise Exception("无权限查看他人任务")

    try:
        # 评审标准集合版本号
        user_task.criteria_set_identifier = (
            await system_evaluation_service.get_criteria_set_identifier_by_id(
                db, quiz_plan.dim_set_id
            )
        )

        # 评审标准
        criteria_set = await system_evaluation_service.get_criteria_list_by_criteria_set_identifier(
            db, user_task.criteria_set_identifier
        )

        # 问题集合
        single_round_questions, multiple_round_questions = (
            await crud.get_quiz_questions_by_task_id_v2(db, task_id)
        )

        # 设置点踩状态
        evaluation_id = user_task.evaluation_id
        query_dislike = DislikeQueryModel(evaluation_id=evaluation_id, user_id=get_user_id_v2())
        user_dislike = await system_question_dislike_curd.list_dislike_user_by_condition(db, query_dislike)
        user_dislike_dict = {t.question_id: 0 for t in user_dislike}

        if single_round_questions and len(single_round_questions) > 0:
            for s in single_round_questions:
                s["dislike_enable"] = user_dislike_dict.get(s["question_id"], 1)

        if multiple_round_questions and len(multiple_round_questions) > 0:
            for s in multiple_round_questions:
                s["dislike_enable"] = user_dislike_dict.get(s["question_id"], 1)

        # 是否有权限评分
        rate_enable = False

        # 如果当前任务已完成，不可评分,非自己的任务不能评分
        login_user_id = get_user_id_v2()
        if user_task.user_id == login_user_id and user_task.task_state != 3:
            rate_enable = True

        return {
            "task_info": {
                "evaluation_id": user_task.evaluation_id,
                "task_type": user_task.evaluation_type,
                "task_state": user_task.task_state,
                "task_name": user_task.task_name,
                "task_desc": user_task.task_desc,
                "created_at": user_task.created_at,
                "task_num": user_task.task_num,
                "evaluation_state": user_task.evaluation_state,
                "task_evaluation_num": user_task.task_evaluation_num,
                "eval_product": user_task.eval_product,
                "user_id": user_task.user_id,
                "rate_enable": rate_enable,
            },
            "criterias": criteria_set,
            "single_round_questions": single_round_questions,
            "multiple_round_questions": multiple_round_questions,
        }
    except Exception as e:
        logger.error(f"Error getting user task detail for task {task_id}: {e}")
        raise Exception("获取用户任务详情失败")


# 根据用户任务id获取任务详情
async def get_user_task_detail(db: AsyncSession, task_id: str):
    user_task = await crud.get_user_task_by_task_id(db, task_id)
    user_permissions = user.get_user_permissions()
    has_permission = should_match([Permission.EVAL_MGMT.value], user_permissions)
    if not user_task:
        raise Exception("用户任务不存在")
    elif user_task.user_id != get_user_id_v2() and has_permission is False:
        raise Exception("无权限查看他人任务")

    try:

        # 评审标准集合版本号
        user_task.criteria_set_identifier = (
            await system_evaluation_service.get_criteria_set_identifier_by_id(
                db, user_task.dim_set_id
            )
        )

        # 评审标准
        criteria_set = await system_evaluation_service.get_criteria_list_by_criteria_set_identifier(
            db, user_task.criteria_set_identifier
        )

        # 问题集合
        questions_list = (
            await system_evaluation_service.get_quiz_questions_by_task_id_and_user_id(
                db, task_id
            )
        )

        return {
            "task_info": {
                "system_version": user_task.system_version,
                "system_version_info": user_task.system_version_info,
                "task_type": user_task.evaluation_type,
                "task_state": user_task.task_state,
                "task_name": user_task.task_name,
                "task_desc": user_task.task_desc,
                "created_at": user_task.created_at,
                "task_num": user_task.task_num,
                "evaluation_state": user_task.evaluation_state,
                "task_evaluation_num": user_task.task_evaluation_num,
                "eval_product": user_task.eval_product,
                "user_id": user_task.user_id,
            },
            "criterias": criteria_set,
            "questions": questions_list,
        }
    except Exception as e:
        logger.error(f"Error getting user task detail for task {task_id}: {e}")
        raise Exception("获取用户任务详情失败")

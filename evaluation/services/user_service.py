from typing import List, Optional
import httpx
import logging
from evaluation.api.base import ErrorR<PERSON>
from evaluation.api.req.evaluation_model import QueryPlanReq
from evaluation.api.req.user_model import (
    EvaluationGroupCreateRequest,
    EvaluationGroupUpdateRequest,
)
from evaluation.services import ac
from evaluation.share.exception.hanhai_exception import HanHaiException
from evaluation.share.orm import crud, user_set_crud, evaluation_plan_crud
from evaluation.share.orm.platform_schema import User
from sqlalchemy.ext.asyncio import AsyncSession
from evaluation.api.req.base_model import BaseModelPageListReq
from evaluation.share.util import user
from evaluation.share.util.app_config import CONFIG
from evaluation.share.util.permission import should_match, Permission
from evaluation.share.util.user import get_user_id_v2, get_user_full_name
from evaluation.share.util.util import timestamp_to_str
from evaluation.share.util.user_info_decorator import inject_user_info

logger = logging.getLogger("plaintext")


async def get_user_list(search_term: Optional[str]) -> List[User]:
    return await ac.get_user_list_with_search_term(search_term)

# 根据 list<user_id> 获取用户信息
async def get_users_by_user_ids(user_ids: List[str]) -> List[User]:
    try:
        logger.debug(f"get_users_by_user_ids user_ids:{user_ids}")
        user_info_list = await ac.get_user_list_with_search_term(None)
    except Exception as e:
        logger.error(f"get_users_by_user_ids error: {e}")
        return []
    if len(user_ids) == 0:
        return user_info_list

    result = []
    for u in user_info_list:
        if u.user_id in user_ids:
            result.append(u)
    return result

# 根据评测组标识获取人员列表
async def get_user_set_members(db: AsyncSession, user_set_identifier: str) -> List[User]:
    _, user_set_members = await crud.get_user_set_detail(db, user_set_identifier)
    members = await get_users_by_user_ids(user_set_members)
    if not members:
        return []
    return members


async def batch_get_user_set_detail(db: AsyncSession, user_set_identifiers: list[str]) -> dict:
    user_set_members_dict = await crud.get_user_set_relation(db, user_set_identifiers)

    user_id_set = set()
    for v in user_set_members_dict.values():
        if len(v) > 0:
            for t in v:
                user_id_set.add(t)

    # 获取用户信息
    members = await get_users_by_user_ids(list(user_id_set))

    # 根据user_set_identifier过滤members
    result = {}
    for key, value in user_set_members_dict.items():
        result[key] = [member for member in members if member.user_id in value]

    return result


# 获取评测组列表
@inject_user_info({"created_user": "created_user_name", "updated_user": "updated_user_name"})
async def get_evaluation_group_list(
        db: AsyncSession, req: BaseModelPageListReq
) -> (int, list):

    # 权限校验
    user_permissions = user.get_user_permissions()
    has_permission = should_match([Permission.EVAL_MGMT.value, Permission.VIEW_EVAL.value], user_permissions)
    if not has_permission:
        logger.info(f"no permission, user:{user.get_user_name()}, user_permissions:{user_permissions}.")
        return 0, []

    total, user_set = await crud.get_user_sets(
        db, req.page, req.page_size, req.fetch_total, req.search_term, req.order_str
    )
    data = []

    user_set_identifiers = [user_set_item.user_set_identifier for user_set_item in user_set]
    members_dict = await batch_get_user_set_detail(db, user_set_identifiers)
    logger.info(f"members:{members_dict}")

    for user_set_item in user_set:
        members_name = ""
        if user_set_item.user_set_identifier in members_dict:
            members = members_dict[user_set_item.user_set_identifier]
            members_name = ",".join([member.full_name for member in members])
        data.append(
            {
                "created_at": timestamp_to_str(user_set_item.created_at),
                "id": user_set_item.id,
                "quiz_type": user_set_item.quiz_type,
                "description": user_set_item.description,
                "updated_user": user_set_item.updated_user,
                "state": user_set_item.state,
                "user_set_identifier": user_set_item.user_set_identifier,
                "name": user_set_item.name,
                "updated_at": timestamp_to_str(user_set_item.updated_at),
                "created_user": user_set_item.created_user,
                "version": user_set_item.version,
                "related_count": user_set_item.related_count,
                "members_name": members_name,
            }
        )
    return total, data


# 新建评测组
async def create_evaluation_group(
        db: AsyncSession, req: EvaluationGroupCreateRequest
) -> str:
    try:

        # 校验名称
        user_set = await user_set_crud.get_user_set_by_name(db, req.name)
        if user_set:
            raise Exception("评测组名称已存在")

        return await crud.create_user_set(
            db,
            name=req.name,
            description=req.description,
            members=req.members,
            created_user=get_user_full_name(),
        )
    except Exception as e:
        logger.error(f"create_evaluation_group error: {str(e)}")
        raise HanHaiException(f"创建评测组失败:{str(e)}")


# 删除评测组
async def delete_evaluation_group(db: AsyncSession, user_set_identifiers: list[str]) -> str:
    try:
        logger.info(
            f"delete_evaluation_group: {user_set_identifiers},user_id:{get_user_id_v2()},user_name:{user.get_user_name()}")

        # 校验
        user_set_list = await user_set_crud.get_user_set_by_identifiers(db, user_set_identifiers)
        if not user_set_list:
            raise Exception("评测组不存在")

        for user_set in user_set_list:
            req = QueryPlanReq(user_set_id=user_set.id)
            plans, total = await evaluation_plan_crud.get_quiz_plan_list(db, req)
            if total == 0:
                continue
            for plan in plans:
                # 0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束
                if plan.state in [0, 1, 2, 4, 5, 6]:
                    raise Exception(f"\"{plan.plan_name}\"任务进行中，不能删除改评测组")

        # 删除评测组
        await user_set_crud.delete_user_set(db, user_set_identifiers)
        await user_set_crud.delete_user_set_relations(db, user_set_identifiers)
        return "删除成功"
    except Exception as e:
        logger.error(f"delete_evaluation_group error: {e}")
        raise e


# 更新评测组
async def update_evaluation_group(
        db: AsyncSession, req: EvaluationGroupUpdateRequest
):
    try:
        # await check_user_ids_exist(req.members)
        last_user_set = await crud.get_last_version_user_set(db, req.user_set_identifier)
        if last_user_set is None:
            raise Exception("评测组不存在")

        # 校验评测组是否可以更新
        plans, total = await evaluation_plan_crud.get_quiz_plan_list(db, QueryPlanReq(user_set_id=last_user_set.id))
        if total > 0:
            for plan in plans:
                # 0: 未发布 1: 排队中  2: 模型推理中 3: 模型推理失败 4:待评测 5 评测中  6 结果生成中 7 任务结束
                if plan.state in [0, 1, 2, 4, 5, 6]:
                    raise Exception(f"\"{plan.plan_name}\"任务进行中，不能更新该评测组")

        # 更新评测组
        await crud.update_user_set(db, req.user_set_identifier, req.name, req.description)

        # 更新评测组人员
        await crud.update_user_set_relations(db, req.user_set_identifier, req.members)

    except ValueError as e:
        logger.error(f"update_evaluation_group error: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"update_evaluation_group error: {str(e)}")
        raise e


# 评测组详情
@inject_user_info({"created_user": "created_user_name", "updated_user": "updated_user_name"})
async def get_evaluation_group_detail(db: AsyncSession, user_set_identifier: str):
    user_set, user_set_members = await crud.get_user_set_detail(db, user_set_identifier)
    if user_set is not None:

        members = await get_users_by_user_ids(user_set_members)
        user_set_data = {
            "created_at": timestamp_to_str(user_set.created_at),
            "id": user_set.id,
            "quiz_type": user_set.quiz_type,
            "description": user_set.description,
            "updated_user": user_set.updated_user,
            "state": user_set.state,
            "user_set_identifier": user_set.user_set_identifier,
            "name": user_set.name,
            "updated_at": timestamp_to_str(user_set.updated_at),
            "created_user": user_set.created_user,
            "version": user_set.version,
            "members": members,
        }
    else:
        user_set_data = {}

    return user_set_data


async def get_user_name_map_by_ids(user_ids: List[str]) -> dict[str, str]:
    """
    根据user_id列表批量获取用户姓名映射
    
    Args:
        user_ids: 用户ID列表
        
    Returns:
        用户ID到姓名的映射字典 {user_id: full_name}
    """
    if not user_ids:
        return {}
    
    users = await get_users_by_user_ids(user_ids)
    return {user.user_id: user.full_name for user in users if user.user_id}


# 根据关键字过滤评测组人员列表
async def filter_evaluation_group_members(
        user_set_data, search_term: str
) -> list:
    members = user_set_data.get("members", [])
    if search_term:
        search_term_lower = search_term.lower()
        filtered_members = [
            member
            for member in members
            if search_term_lower in member.full_name.lower()
               or search_term_lower in member.department.lower()
        ]
    else:
        filtered_members = members

    return filtered_members

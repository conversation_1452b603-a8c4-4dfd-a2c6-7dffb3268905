from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any

# 获取客观题集评测结果，生产评测报告返回
#
#
# css_style = """
#   <style>
#     * {
#       margin: 0;
#       padding: 0;
#       box-sizing: border-box;
#     }
#
#     body {
#       background-color: #f3f4f6;
#       padding: 24px;
#       height: 100vh;
#     }
#
#     .card {
#       border-radius: 8px;
#       background-color: white;
#       padding: 24px;
#       min-height: 100%;
#     }
#     .header {
#       margin-bottom: 24px;
#     }
#
#     .table-header {
#       display: flex;
#       align-items: center;
#       gap: 8px;
#       margin-bottom: 4px;
#     }
#
#     .table-header .block {
#       width: 4px;
#       height: 24px;
#       background-color: #ef4444;
#     }
#
#     .time {
#       font-size: 14px;
#       color: #777;
#       margin-bottom: 24px;
#       margin-left: 12px;
#       font-weight: normal;
#     }
#
#     .title {
#       font-size: 24px;
#       font-weight: 600;
#       color: #1f2937;
#     }
#
#     .my-table {
#       border-radius: 4px;
#       width: 100%;
#       border-collapse: collapse;
#       overflow: hidden;
#     }
#
#     .my-table th,
#     .my-table td {
#       padding: 8px;
#     }
#
#     .my-table th:first-child,
#     .my-table td:first-child {
#       width: 240px;
#     }
#
#     .my-table tr:nth-child(even) {
#       background-color: #f2f2f2;
#     }
#
#     .my-table th {
#       text-align: left;
#       background-color: #ef4444;
#       color: white;
#     }
#
#     .my-table td {
#       color: #525252;
#       /* 灰色字体 */
#     }
#   </style>
#     """
#
# html = """
# <!DOCTYPE html>
# <html lang="en">
#
# <head>
#   <meta charset="UTF-8">
#   <meta name="viewport" content="width=device-width, initial-scale=1.0">
#   <title>客观题集评测报告</title>
#   %s
# </head>
#
# <body>
#   <div class="card">
#
#     <div class="header">
#       <div class="table-header">
#         <div class="block"></div>
#         <h1 class="title">客观题集评测报告</h1>
#       </div>
#       <h2 class="time">评测ID: %s </h2>
#       <h2 class="time">评测时间：%s </h2>
#     </div>
#
#     <table class="dataframe my-table">
#       <thead>
#         <tr>
#           <th>任务类型</th>
#           <th>召回类型</th>
#           <th>模型</th>
#           <th>题集数量</th>
#           <th>正确率</th>
#           <th>召回率</th>
#         </tr>
#       </thead>
#       <tbody>
#         %s
#       </tbody>
#     </table>
#   </div>
# </body>
# </html>
# """


# async def quiz_objective_report(db: AsyncSession, evaluation_id: str) -> Any:
#     results = {}
#     data = await crud_objective_eval.system_objective_quiz_rs(db, evaluation_id)
#     tr_content = ""
#     eval_id = ""
#     html_content = ""
#     eval_time = ""
#     for obj in data:
#         eval_time = obj.created_at
#         eval_id = obj.evaluation_id
#         tr = f"""
#                 <tr>
#                     <td>{obj.question_type}</td>
#                     <td>{obj.recall_type}</td>
#                     <td>{obj.model}</td>
#                     <td>{obj.question_count}</td>
#                     <td>{round(obj.correct_rate * 100, 2) if obj.correct_rate > 0 else 0}</td>
#                     <td>{round(obj.recall_rate * 100, 2)if obj.recall_rate > 0 else 0}</td>
#                </tr>
#              """
#         tr_content += tr
#     if tr_content:
#         html_content = html % (css_style, eval_id, eval_time, tr_content)
#     results["content"] = html_content
#     results["eval_id"] = eval_id
#     return results

# import json
# from typing import List, Optional
# from pydantic import BaseModel
# from evaluation.share.orm.platform_schema import User
#
#
# # 评测任务队列名称
# EVALUATION_TASK_QUEUE = "evaluation_task_queue"
# # uss 用户人员列表名称
# USS_USER_LIST = "uss_user_list"
#
# CACHE_EXPIRE_IN_SECONDS = 600
#
#
# class TaskInfo(BaseModel):
#     task_id: str
#     user_id: str
#     question_id: int
#     parent_task_id: str
#     question: str
#     system_url: Optional[str] = None
#     question_type: Optional[str] = None
#
#
# class RedisClient:
#     def __init__(self, host, port, db, password):
#         self.host = host
#         self.port = port
#         self.db = db
#         self.password = password
#         import aioredis
#
#         if hasattr(self, "password") and self.password:
#             self.client = aioredis.from_url(
#                 f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
#             )
#         else:
#             self.client = aioredis.from_url(
#                 f"redis://{self.host}:{self.port}/{self.db}"
#             )
#
#     def delete(self, key):
#         return self.client.delete(key)
#
#     async def clear_task_queue(self, queue_prefix):
#         queue_name = f"{EVALUATION_TASK_QUEUE}_{queue_prefix}"
#         await self.client.delete(queue_name)
#
#     async def get_task_from_redis(self, queue_prefix) -> TaskInfo | None:
#         queue_name = f"{EVALUATION_TASK_QUEUE}_{queue_prefix}"
#         task_json = await self.client.lpop(queue_name)
#         if not task_json:
#             return None
#         task_data = json.loads(task_json)
#         task_info = TaskInfo(**task_data)
#         return task_info
#
#     async def push_task_to_redis(self, queue_prefix, task_json: str):
#         queue_name = f"{EVALUATION_TASK_QUEUE}_{queue_prefix}"
#         await self.client.lpush(queue_name, task_json)
#
#     async def get_task_list_num(self, queue_prefix):
#         queue_name = f"{EVALUATION_TASK_QUEUE}_{queue_prefix}"
#         return await self.client.llen(queue_name)
#
#     async def get_user_list(self, search_term: str) -> List[User]:
#         cache_key = f"{USS_USER_LIST}:{search_term}"
#         cached_data = await self.client.get(cache_key)
#
#         if not cached_data:
#             from evaluation.services import user_service
#
#             user_list: List[User] = await user_service.get_user_list_uss(search_term)
#             user_list_dict = [
#                 user.dict() for user in user_list
#             ]
#             await self.client.set(
#                 cache_key, json.dumps(user_list_dict), ex=CACHE_EXPIRE_IN_SECONDS
#             )
#             return user_list
#
#         user_list_dict = json.loads(cached_data)
#         user_list = [
#             User(**user_dict) for user_dict in user_list_dict
#         ]
#         return user_list
#
#     async def get_user_by_user_id(self, user_id: str) -> Optional[User]:
#         cache_key = f"user:{user_id}"
#         cached_user = await self.client.get(cache_key)
#
#         if cached_user:
#             return User(**json.loads(cached_user))
#
#         user_list = await self.get_user_list("")
#         for user in user_list:
#             if user.user_id == user_id:
#                 await self.client.set(
#                     cache_key, json.dumps(user.dict()), ex=CACHE_EXPIRE_IN_SECONDS
#                 )
#                 return user
#
#         return None
#
#     async def get_user_by_user_ids(self, user_ids: List[str]) -> List[User]:
#         user_list = []
#         for user_id in user_ids:
#             user = await self.get_user_by_user_id(user_id)
#             if user:
#                 user_list.append(user)
#         return user_list
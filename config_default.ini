[DEFAULT]
project = hanhai_human_eval
version = v20240520
namespace = ${project}-${environment}
container_root = /app/human-eval
app_root = /data/app
log_root = /data/log
pod_uid = na

;[api]
;host = 127.0.0.1
;port = 9115
;session_cookie = session_${project}_${environment}
;session_secret = eJf5cJ1eNML0nrcqBPNGX0Vag2HoEcHN
;knowledge_base_url = http://${environment}.cc.ai.ksyun.com
;knowledge_base_port = 80
;knowledge_chat_path = api/v1/test/chat
;max_age = 21600
;

[observability]
log_level = 10
;apm_server_url = http://dev.kibana.ai.ksyun.com:8200
;apm_service_name = ${project}-admin-${environment}
;apm_ignore_urls = /api/v1/base/status
;plaintext_log_file = ${log_root}/${namespace}/admin-${pod_uid}.log
;ecs_log_file = ${log_root}/${namespace}/admin-${pod_uid}.ecs.json
;metric_log_file = ${log_root}/${namespace}/admin-${pod_uid}.metric.json
;# CRITICAL = 50 FATAL = CRITICAL ERROR = 40 WARNING = 30 WARN = WARNING INFO = 20 DEBUG = 10

;# MB
;max_log_size = 100000000
;max_log_file = 10

[mysql]
db_name = ${project}_${environment}
db_username = root
db_password = 123456
db_host = localhost
db_port = 3306
dsn = mysql+aiomysql://${db_username}:${db_password}@${db_host}:${db_port}/${db_name}?charset=utf8mb4
sync_dsn = mysql+pymysql://${db_username}:${db_password}@${db_host}:${db_port}/${db_name}?charset=utf8mb4
decrypt_aes_key = CUSR/0hrCx1XT43C4aGjJ5YEbvT3rI7f

[auth]
auth_type = USS
chat_api_sk = c9d2dd06ff2fc6a51896158a88d166207c6a40c51f3b30b5e9fbfecbd44f0acb
allow_run_as_admin = true
enable_auth = true
url_uss_user_info = http://uss.ksyun.com/api/checklogin
url_uss_role_users = https://uss.ksyun.com/third_party/business/get_role_employees


[training]
platform = http://hanhai-${environment}.ai.ksyun.com/model-manage
host = hanhai-${environment}.ai.ksyun.com

[inference]
platform = http://hanhai-${environment}.ai.ksyun.com/inference
host = hanhai-${environment}.ai.ksyun.com
default_gpus_per_replica = 4
resource_category = INFERENCE


[inference_eval]
platform = http://inference-${environment}.ai.ksyun.com
host = inference-${environment}.ai.ksyun.com
header = Custom-Host

[common]
platform = http://hanhai-poc-${environment}.ai.ksyun.com/common
host = hanhai-poc-${environment}.ai.ksyun.com

[auth_server]
platform = http://ec.kcde.ksyun.com

[channel]
platform = common
role =
source = HH

[jwt]
key_version = 240301
public_key = ${container_root}/conf/jwt_public_${key_version}-${environment}.pem


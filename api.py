# coding=utf-8

import os

import uvicorn
from elasticapm.contrib.starlette import make_apm_client, ElasticAPM
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware

from evaluation.api.eval_job_api import register_eval_job_api
from evaluation.api.eval_report_api import register_eval_report_api
from evaluation.api.evaluation_api import register_evaluation_api
from evaluation.api.evaluation_plan_api import register_evaluation_plan_api
from evaluation.api.evaluation_base_data_api import register_evaluation_base_data_api
from evaluation.api.menu_api import register_setting_api
from evaluation.api.questions_api import register_questions_api
from evaluation.api.questions_set_api import register_questions_set_api
from evaluation.api.user_api import register_user_api
from evaluation.api.user_eval_task_api import register_user_eval_task_api
from evaluation.api.validation_exception_handler import register_validation_exception_handler
from evaluation.config.args import parser
from evaluation.job import init_cron_job
from evaluation.share.middleware import exception_handling_middleware
from evaluation.share.orm import database as eval_db
from evaluation.share.util import plaintext_log, metric, user
from evaluation.share.util.app_config import config_instance, CONFIG

from evaluation.share.util.reqid import RequestIDMiddleware, CustomloggerMiddleware


# def init_observability(application: FastAPI):
#     apm_client = make_apm_client({
#         'SERVER_URL': CONFIG['observability']['apm_server_url'],
#         'SERVICE_NAME': CONFIG['observability']['apm_service_name'],
#         'ENVIRONMENT': CONFIG['DEFAULT']['environment'],
#         'TRANSACTION_IGNORE_URLS': CONFIG['observability']['apm_ignore_urls'],
#     })
#     application.add_middleware(ElasticAPM, client=apm_client)


def init_app(config_file: str = ""):
    env = os.environ.get('APP_ENV')
    env = "local"
    # env = "poc"
    config_instance.load_config(
        prefix="config", env=env, specified_file=config_file)

    # 初始化数据库
    eval_db.init()

    plaintext_log.init(log_level=int(CONFIG["observability"]["log_level"]))

    # metric.init(
    #     CONFIG["observability"]["metric_log_file"],
    #     int(CONFIG["observability"]["max_log_size"]),
    #     int(CONFIG["observability"]["max_log_file"]),
    # )


def create_app():
    """
    uvicorn 导入字符串方式启动, 导入此函数
    PYTHONPATH=~/human-eval APP_ENV=local uvicorn  --port 7861 --reload
    """

    app = FastAPI(
        title="瀚海人工评测系统",
        description="基于 FastAPI 的人工智能模型评测后端系统",
        version="1.7.0",
        docs_url="/eval/api/v1/docs",
        redoc_url="/eval/api/v1/redoc",
        openapi_url="/eval/api/v1/openapi.json"
    )
    init_app(args.config_file)

    # app.add_middleware(AccessLogMiddleware)
    # Add CORS middleware to allow all origins
    # 在config.py中设置OPEN_DOMAIN=True，允许跨域
    # set OPEN_DOMAIN=True in config.py to allow cross-domain
    app.add_middleware(
        exception_handling_middleware.ExceptionHandlingMiddleware)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"])
    app.add_middleware(CustomloggerMiddleware)
    app.add_middleware(RequestIDMiddleware)

    # app.add_middleware(SessionMiddleware,
    #                    session_cookie=CONFIG["api"]["session_cookie"],
    #                    secret_key=CONFIG["api"]["session_secret"],
    #                    max_age=int(CONFIG["api"]["max_age"]))

    user.init_jwt_public_key(CONFIG["jwt"]["public_key"])
    # app.add_middleware(user.AuthenticationMiddleware)

    register_questions_api(app)
    register_questions_set_api(app)
    register_evaluation_api(app)
    register_evaluation_plan_api(app)
    register_evaluation_base_data_api(app)
    register_setting_api(app)
    register_user_api(app)
    # init_observability(app)
    register_user_eval_task_api(app)
    register_eval_report_api(app)
    register_eval_job_api(app)
    register_validation_exception_handler(app)

    # 定时任务
    # init_cron_job.init_job()

    return app


def start_app(_app, host, port, **kwargs):
    if kwargs.get("ssl_keyfile") and kwargs.get("ssl_certfile"):
        uvicorn.run(_app, host=host, port=port, reload=True, access_log=False,
                    ssl_keyfile=kwargs.get("ssl_keyfile"),
                    ssl_certfile=kwargs.get("ssl_certfile"))
    else:
        uvicorn.run(_app, host=host, port=port, access_log=False)


if __name__ == "__main__":
    args = parser.parse_args()

    _app = create_app()
    start_app(_app, args.host, args.port)
